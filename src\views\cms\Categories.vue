<template>
  <div class="bg-white p-6 rounded-lg shadow-md mx-auto max-w-4xl">
    <h2 class="text-2xl font-bold mb-4">文章分类</h2>
    <el-row :gutter="20" class="flex flex-wrap">
      <el-col
        :xs="24"
        :sm="12"
        :md="8"
        :lg="6"
        v-for="category in categories"
        :key="category.id"
        class="mb-4"
      >
        <el-card
          :body-style="{ padding: '0px' }"
          class="h-full cursor-pointer hover:shadow-lg transition-shadow"
          @click.native="handleCategoryClick(category)"
        >
          <div class="p-4 text-center">
            <h3 class="text-lg font-semibold">{{ category.name }}</h3>
            <p class="text-gray-500 text-sm">{{ category.description }}</p>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-pagination
      v-model:current-page="currentPage"
      :page-size="pageSize"
      :total="total"
      layout="total, prev, pager, next"
      class="mt-4 flex justify-center"
      @current-change="handlePageChange"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const categories = ref([])
const currentPage = ref(1)
const pageSize = ref(12)
const total = ref(0)

onMounted(() => {
  fetchCategories()
})

const fetchCategories = () => {
  // 模拟数据
  categories.value = [
    { id: 1, name: '政策法规', description: '房产中介相关政策和法规解读' },
    { id: 2, name: '行业动态', description: '房产中介行业最新动态和趋势' },
    { id: 3, name: '市场分析', description: '房产市场数据分析和预测' },
    { id: 4, name: '培训资讯', description: '房产中介培训相关信息和通知' },
    // 更多数据...
  ]
  total.value = categories.value.length
}

const handleCategoryClick = (category) => {
  router.push({ path: '/cms/articles', query: { categoryId: category.id } })
}

const handlePageChange = (page) => {
  currentPage.value = page
  fetchCategories()
}
</script>

<style scoped>
/* 文章分类特定样式 */
.el-card {
  transition: box-shadow 0.3s ease;
}
.el-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
</style>
