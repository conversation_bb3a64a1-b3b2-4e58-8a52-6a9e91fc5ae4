<template>
  <div class="bg-white p-6 rounded-lg shadow-md">
    <h2 class="text-2xl font-bold mb-4">经纪人管理</h2>
    <el-button type="primary" class="mb-4" @click="handleAdd">添加经纪人</el-button>
    <el-table :data="brokers" border style="width: 100%" v-loading="loading">
      <el-table-column prop="id" label="ID" width="80"></el-table-column>
      <el-table-column prop="name" label="姓名" width="120"></el-table-column>
      <el-table-column prop="id_card" label="身份证号" width="180"></el-table-column>
      <el-table-column prop="phone" label="手机号" width="120"></el-table-column>
      <el-table-column prop="certificate_type" label="证书类型" width="100">
        <template #default="scope">
          {{ getCertificateTypeLabel(scope.row.certificate_type) }}
        </template>
      </el-table-column>
      <el-table-column prop="certificate_number" label="证书编号" width="150"></el-table-column>
      <el-table-column prop="status" label="审核状态" width="100">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusLabel(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="身份证照片" width="120">
        <template #default="scope">
          <div v-if="scope.row.id_card_image">
            <el-image :src="getImageUrl(scope.row.id_card_image)" :preview-src-list="[getImageUrl(scope.row.id_card_image)]"
              class="w-16 h-10 object-cover rounded border cursor-pointer" fit="cover">
              <template #error>
                <div class="w-16 h-10 flex items-center justify-center bg-gray-100 text-gray-400 text-xs rounded border">
                  加载失败
                </div>
              </template>
            </el-image>
          </div>
          <div v-else class="w-16 h-10 flex items-center justify-center bg-gray-50 text-gray-400 text-xs rounded border">
            暂无图片
          </div>
        </template>
      </el-table-column>
      <el-table-column label="证书照片" width="120">
        <template #default="scope">
          <div v-if="scope.row.certificate_image">
            <el-image :src="getImageUrl(scope.row.certificate_image)" :preview-src-list="[getImageUrl(scope.row.certificate_image)]"
              class="w-16 h-10 object-cover rounded border cursor-pointer" fit="cover">
              <template #error>
                <div class="w-16 h-10 flex items-center justify-center bg-gray-100 text-gray-400 text-xs rounded border">
                  加载失败
                </div>
              </template>
            </el-image>
          </div>
          <div v-else class="w-16 h-10 flex items-center justify-center bg-gray-50 text-gray-400 text-xs rounded border">
            暂无图片
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="280">
        <template #default="scope">
          <el-button size="small" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="small" type="info" @click="handleViewPassword(scope.row)" plain>查看密码</el-button>
          <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination v-model:current-page="currentPage" :page-size="pageSize" :total="total" layout="total, prev, pager, next" class="mt-4"
      @current-change="handlePageChange" />
    <el-dialog v-model="dialogVisible" title="添加/编辑经纪人" width="50%">
      <el-form :model="form" :rules="rules" ref="formRef">
        <el-form-item label="姓名" prop="name">
          <el-input v-model="form.name" placeholder="请输入姓名"></el-input>
        </el-form-item>
        <el-form-item label="身份证号" prop="id_card">
          <el-input v-model="form.id_card" placeholder="请输入身份证号"></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入手机号"></el-input>
        </el-form-item>
        <el-form-item label="证书类型" prop="certificate_type">
          <el-select v-model="form.certificate_type" placeholder="请选择证书类型">
            <el-option label="经纪人" value="broker"></el-option>
            <el-option label="助理" value="assistant"></el-option>
            <el-option label="培训" value="training"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="证书编号" prop="certificate_number">
          <el-input v-model="form.certificate_number" placeholder="请输入证书编号"></el-input>
        </el-form-item>
        <el-form-item label="上传身份证照片">
          <!-- 显示现有图片 -->
          <div v-if="form.id_card_image && form.id" class="mb-3">
            <div class="text-sm text-gray-600 mb-2">当前身份证照片：</div>
            <el-image :src="getImageUrl(form.id_card_image)" :preview-src-list="[getImageUrl(form.id_card_image)]" class="w-32 h-20 object-cover rounded border"
              fit="cover">
              <template #error>
                <div class="w-32 h-20 flex items-center justify-center bg-gray-100 text-gray-400 text-sm rounded border">
                  图片加载失败
                </div>
              </template>
            </el-image>
          </div>
          <el-upload class="upload-demo" action="#" :auto-upload="false" :on-change="handleIdCardChange" :on-remove="handleIdCardRemove" :limit="1"
            :on-exceed="handleExceed" :file-list="idCardFileList" accept="image/*">
            <el-button size="small" type="primary">{{ form.id_card_image && form.id ? '重新上传' : '点击上传' }}</el-button>
            <template #tip>
              <div class="el-upload__tip">只能上传图片文件，且不超过10mb</div>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item label="上传证书照片">
          <!-- 显示现有图片 -->
          <div v-if="form.certificate_image && form.id" class="mb-3">
            <div class="text-sm text-gray-600 mb-2">当前证书照片：</div>
            <el-image :src="getImageUrl(form.certificate_image)" :preview-src-list="[getImageUrl(form.certificate_image)]"
              class="w-32 h-20 object-cover rounded border" fit="cover">
              <template #error>
                <div class="w-32 h-20 flex items-center justify-center bg-gray-100 text-gray-400 text-sm rounded border">
                  图片加载失败
                </div>
              </template>
            </el-image>
          </div>
          <el-upload class="upload-demo" action="#" :auto-upload="false" :on-change="handleCertificateChange" :on-remove="handleCertificateRemove" :limit="1"
            :on-exceed="handleExceed" :file-list="certificateFileList" accept="image/*">
            <el-button size="small" type="primary">{{ form.certificate_image && form.id ? '重新上传' : '点击上传' }}</el-button>
            <template #tip>
              <div class="el-upload__tip">只能上传图片文件，且不超过10mb</div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>

    <!-- 查看密码对话框 -->
    <el-dialog v-model="passwordDialogVisible" title="经纪人登录密码" width="400px" center>
      <div class="text-center">
        <div class="mb-4">
          <span class="text-gray-600">经纪人：</span>
          <span class="font-medium">{{ selectedBroker.name }}</span>
        </div>
        <div class="mb-4">
          <span class="text-gray-600">手机号：</span>
          <span class="font-medium">{{ selectedBroker.phone }}</span>
        </div>
        <div class="mb-6">
          <!-- 有初始密码时显示 -->
          <div v-if="brokerPassword">
            <div class="text-gray-600 mb-2">初始登录密码：</div>
            <div class="bg-gray-50 p-4 rounded border text-lg font-mono select-all">
              {{ brokerPassword }}
            </div>
            <div class="text-xs text-gray-500 mt-2">
              注意：此为初始密码，经纪人登录后可能已修改
            </div>
          </div>
          <!-- 无初始密码时显示 -->
          <div v-else>
            <div class="text-center py-6">
              <el-icon size="48" class="text-gray-400 mb-3">
                <Lock />
              </el-icon>
              <div class="text-gray-600 mb-2">初始密码已失效</div>
              <div class="text-sm text-gray-500">
                该经纪人已登录过，初始密码不再显示
              </div>
              <div class="text-xs text-gray-400 mt-2">
                如需重置密码，请联系系统管理员
              </div>
            </div>
          </div>
        </div>
        <el-button v-if="brokerPassword" type="primary" @click="copyPassword" icon="DocumentCopy">
          复制密码
        </el-button>
      </div>
      <template #footer>
        <el-button @click="passwordDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Lock } from '@element-plus/icons-vue'
import { getBrokerList, addBroker } from '@/api/agency'

const brokers = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const dialogVisible = ref(false)
const loading = ref(false)
const passwordDialogVisible = ref(false)
const selectedBroker = ref({})
const brokerPassword = ref('')
const form = ref({
  id: null,
  name: '',
  id_card: '',
  phone: '',
  certificate_type: '',
  certificate_number: '',
  id_card_image: '',
  certificate_image: '',
  initial_password: null,
})
const formRef = ref(null)
const idCardFileList = ref([])
const certificateFileList = ref([])
const rules = {
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  id_card: [{ required: true, message: '请输入身份证号', trigger: 'blur' }],
  phone: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
  certificate_type: [{ required: true, message: '请选择证书类型', trigger: 'change' }],
  certificate_number: [{ required: true, message: '请输入证书编号', trigger: 'blur' }],
}

onMounted(() => {
  fetchBrokers()
})

const fetchBrokers = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      per_page: pageSize.value
    }
    const response = await getBrokerList(params)
    brokers.value = response.data || []
    total.value = response.total || 0
  } catch (error) {
    console.error('获取经纪人列表失败:', error)
    ElMessage.error('获取经纪人列表失败')
    // 如果API调用失败，使用模拟数据作为后备
    brokers.value = [
      {
        id: 1,
        name: '张三',
        id_card: '110101199001011234',
        phone: '13800138000',
        certificate_type: 'broker',
        certificate_number: 'CERT001',
        status: 'pending',
        id_card_image: '/storage/id_card/broker_id_card_example1.png',
        certificate_image: '/storage/certificate/broker_cert_example1.png',
        initial_password: '138000', // 有初始密码
      },
      {
        id: 2,
        name: '李四',
        id_card: '110101199001021234',
        phone: '13800138001',
        certificate_type: 'assistant',
        certificate_number: 'CERT002',
        status: 'approved',
        id_card_image: '', // 没有身份证图片的情况
        certificate_image: '/storage/certificate/broker_cert_example2.png',
        initial_password: null, // 已登录过，初始密码失效
      }
    ]
    total.value = brokers.value.length
  } finally {
    loading.value = false
  }
}

const handleAdd = () => {
  form.value = {
    id: null,
    name: '',
    id_card: '',
    phone: '',
    certificate_type: '',
    certificate_number: '',
    id_card_image: '',
    certificate_image: '',
    initial_password: null,
  }
  idCardFileList.value = []
  certificateFileList.value = []
  dialogVisible.value = true
}

const handleEdit = (row) => {
  form.value = {
    ...row,
    // 确保图片字段存在
    id_card_image: row.id_card_image || '',
    certificate_image: row.certificate_image || '',
    initial_password: row.initial_password || null
  }
  idCardFileList.value = []
  certificateFileList.value = []
  dialogVisible.value = true
}

const handleViewPassword = (row) => {
  selectedBroker.value = row
  // 检查是否存在初始密码
  if (row.initial_password) {
    brokerPassword.value = row.initial_password
  } else {
    brokerPassword.value = null // 密码已失效
  }
  passwordDialogVisible.value = true
}



const copyPassword = async () => {
  try {
    await navigator.clipboard.writeText(brokerPassword.value)
    ElMessage.success('密码已复制到剪贴板')
  } catch (error) {
    // 如果现代API不可用，使用传统方法
    try {
      const textArea = document.createElement('textarea')
      textArea.value = brokerPassword.value
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      ElMessage.success('密码已复制到剪贴板')
    } catch (fallbackError) {
      ElMessage.error('复制失败，请手动复制密码')
    }
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除经纪人 "${row.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 这里应该调用删除API，但接口文档中没有提供删除接口
    // 暂时只在前端移除
    const index = brokers.value.findIndex(broker => broker.id === row.id)
    if (index !== -1) {
      brokers.value.splice(index, 1)
      total.value = brokers.value.length
      ElMessage.success('删除成功')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleSubmit = async () => {
  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    // 创建FormData对象用于文件上传
    const formData = new FormData()
    formData.append('name', form.value.name)
    formData.append('id_card', form.value.id_card)
    formData.append('phone', form.value.phone)
    formData.append('certificate_type', form.value.certificate_type)
    formData.append('certificate_number', form.value.certificate_number)

    // 添加身份证图片
    if (idCardFileList.value.length > 0 && idCardFileList.value[0].raw) {
      formData.append('id_card_image', idCardFileList.value[0].raw)
    }

    // 添加证书图片
    if (certificateFileList.value.length > 0 && certificateFileList.value[0].raw) {
      formData.append('certificate_image', certificateFileList.value[0].raw)
    }

    if (!form.value.id) {
      // 添加新经纪人
      await addBroker(formData)
      ElMessage.success('经纪人添加成功')
    } else {
      // 编辑功能暂未实现
      ElMessage.warning('编辑功能暂未实现')
    }

    dialogVisible.value = false
    await fetchBrokers() // 重新获取列表
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error(error.response?.data?.message || '提交失败')
  }
}

const handlePageChange = (page) => {
  currentPage.value = page
  fetchBrokers()
}

// 身份证图片处理
const handleIdCardChange = (file, fileList) => {
  idCardFileList.value = fileList
}

const handleIdCardRemove = (file, fileList) => {
  idCardFileList.value = fileList
}

// 证书图片处理
const handleCertificateChange = (file, fileList) => {
  certificateFileList.value = fileList
}

const handleCertificateRemove = (file, fileList) => {
  certificateFileList.value = fileList
}

const handleExceed = (files, fileList) => {
  ElMessage.warning('最多只能上传1个文件')
}

// 证书类型标签转换
const getCertificateTypeLabel = (type) => {
  const typeMap = {
    'broker': '经纪人',
    'assistant': '助理',
    'training': '培训'
  }
  return typeMap[type] || type
}

// 状态标签转换
const getStatusLabel = (status) => {
  const statusMap = {
    'pending': '待审核',
    'approved': '已通过',
    'rejected': '已驳回'
  }
  return statusMap[status] || status
}

// 状态类型转换
const getStatusType = (status) => {
  const typeMap = {
    'pending': 'warning',
    'approved': 'success',
    'rejected': 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取图片URL
const getImageUrl = (imagePath) => {
  if (!imagePath) return ''
  // 如果是完整URL，直接返回
  if (imagePath.startsWith('http')) return imagePath
  // 如果是相对路径，添加基础URL
  return `http://127.0.0.1:8000${imagePath}`
}
</script>

<style scoped>
/* 经纪人管理特定样式 */
</style>
