<template>
  <div class="bg-white p-6 rounded-lg shadow-md">
    <h2 class="text-2xl font-bold mb-4">数据统计</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div class="bg-blue-50 p-4 rounded-lg shadow-inner">
        <h3 class="text-lg font-semibold mb-2">中介机构数量统计</h3>
        <div id="agencyChart" style="width: 100%; height: 300px"></div>
      </div>
      <div class="bg-green-50 p-4 rounded-lg shadow-inner">
        <h3 class="text-lg font-semibold mb-2">经纪人数量统计</h3>
        <div id="brokerChart" style="width: 100%; height: 300px"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import * as echarts from 'echarts'

onMounted(() => {
  initAgencyChart()
  initBrokerChart()
})

const initAgencyChart = () => {
  const chartDom = document.getElementById('agencyChart')
  const myChart = echarts.init(chartDom)
  const option = {
    tooltip: {
      trigger: 'item',
    },
    legend: {
      top: '5%',
      left: 'center',
    },
    series: [
      {
        name: '中介机构数量',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 40,
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          { value: 1048, name: '已审核' },
          { value: 735, name: '待审核' },
          { value: 580, name: '未通过' },
        ],
      },
    ],
  }
  myChart.setOption(option)
}

const initBrokerChart = () => {
  const chartDom = document.getElementById('brokerChart')
  const myChart = echarts.init(chartDom)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      data: ['已审核', '待审核', '未通过'],
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: ['中介公司A', '中介公司B', '中介公司C', '中介公司D', '中介公司E'],
      },
    ],
    yAxis: [
      {
        type: 'value',
      },
    ],
    series: [
      {
        name: '已审核',
        type: 'bar',
        emphasis: {
          focus: 'series',
        },
        data: [320, 332, 301, 334, 390],
      },
      {
        name: '待审核',
        type: 'bar',
        stack: 'Ad',
        emphasis: {
          focus: 'series',
        },
        data: [220, 182, 191, 234, 290],
      },
      {
        name: '未通过',
        type: 'bar',
        stack: 'Ad',
        emphasis: {
          focus: 'series',
        },
        data: [150, 232, 201, 154, 190],
      },
    ],
  }
  myChart.setOption(option)
}
</script>

<style scoped>
/* 数据统计特定样式 */
</style>
