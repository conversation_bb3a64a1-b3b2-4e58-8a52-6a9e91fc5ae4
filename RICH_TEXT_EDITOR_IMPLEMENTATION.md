# 富文本编辑器功能实现

## 🎯 实现目标

为文章管理添加功能强大的富文本编辑器，支持：
- 完整的富文本编辑功能
- 图片上传（Base64和服务器上传两种模式）
- Word文档粘贴并保持格式
- 拖拽上传图片
- 美观的界面设计

## ✅ 主要实现内容

### 1. 依赖安装

```bash
npm install quill vue-quill-editor quill-image-uploader quill-image-resize-module
```

安装的包说明：
- **quill**: 核心富文本编辑器
- **vue-quill-editor**: Vue3的Quill封装
- **quill-image-uploader**: 图片上传插件
- **quill-image-resize-module**: 图片大小调整插件

### 2. 基础富文本编辑器组件

#### 2.1 组件特性 (`src/components/RichTextEditor.vue`)

**核心功能**:
- ✅ 完整的富文本编辑工具栏
- ✅ 图片上传（Base64模式）
- ✅ Word格式保持
- ✅ 自定义样式
- ✅ 响应式设计

**工具栏功能**:
```javascript
[
  [{ 'header': [1, 2, 3, 4, 5, 6, false] }],  // 标题
  [{ 'font': [] }],                            // 字体
  [{ 'size': ['small', false, 'large', 'huge'] }], // 字号
  ['bold', 'italic', 'underline', 'strike'],  // 文本格式
  [{ 'color': [] }, { 'background': [] }],     // 颜色
  [{ 'script': 'sub' }, { 'script': 'super' }], // 上下标
  [{ 'list': 'ordered' }, { 'list': 'bullet' }], // 列表
  [{ 'indent': '-1' }, { 'indent': '+1' }],    // 缩进
  [{ 'align': [] }],                           // 对齐
  ['blockquote', 'code-block'],               // 引用和代码
  ['link', 'image', 'video'],                 // 链接、图片、视频
  ['clean']                                    // 清除格式
]
```

**图片上传处理**:
```javascript
const imageHandler = () => {
  const input = document.createElement('input')
  input.setAttribute('type', 'file')
  input.setAttribute('accept', 'image/*')
  input.click()

  input.onchange = () => {
    const file = input.files[0]
    if (file) {
      // 文件大小检查（5MB限制）
      if (file.size > 5 * 1024 * 1024) {
        alert('图片大小不能超过5MB')
        return
      }

      // 转换为Base64
      const reader = new FileReader()
      reader.onload = (e) => {
        const base64 = e.target.result
        const range = quill.getSelection()
        const index = range ? range.index : quill.getLength()
        quill.insertEmbed(index, 'image', base64)
        quill.setSelection(index + 1)
      }
      reader.readAsDataURL(file)
    }
  }
}
```

**Word格式保持**:
```javascript
// 支持粘贴Word内容并保持格式
quill.clipboard.addMatcher(Node.ELEMENT_NODE, (node, delta) => {
  // 保持基本的格式
  if (node.tagName === 'STRONG' || node.tagName === 'B') {
    return delta.compose(new Quill.import('delta')().retain(delta.length(), { bold: true }))
  }
  if (node.tagName === 'EM' || node.tagName === 'I') {
    return delta.compose(new Quill.import('delta')().retain(delta.length(), { italic: true }))
  }
  if (node.tagName === 'U') {
    return delta.compose(new Quill.import('delta')().retain(delta.length(), { underline: true }))
  }
  return delta
})
```

### 3. 增强版富文本编辑器组件

#### 3.1 组件特性 (`src/components/AdvancedRichTextEditor.vue`)

**增强功能**:
- ✅ 服务器图片上传支持
- ✅ 上传进度显示
- ✅ 拖拽上传图片
- ✅ 批量图片上传
- ✅ 更完整的Word格式保持
- ✅ 上传失败降级处理

**双模式图片上传**:
```javascript
// Props配置
uploadMode: {
  type: String,
  default: 'base64', // 'base64' | 'server'
  validator: (value) => ['base64', 'server'].includes(value)
}

// 服务器上传模式
const uploadToServer = async (file, index) => {
  try {
    uploadDialogVisible.value = true
    uploadProgress.value = 0
    uploadMessage.value = '正在上传图片...'

    const response = await uploadImage(file)
    const imageUrl = response.data?.url || response.url
    
    if (imageUrl) {
      quill.insertEmbed(index, 'image', imageUrl)
      quill.setSelection(index + 1)
    }
  } catch (error) {
    // 上传失败时降级为base64
    uploadAsBase64(file, index)
  }
}
```

**拖拽上传支持**:
```javascript
// 处理拖拽上传
const handleDrop = async (e) => {
  e.preventDefault()
  e.stopPropagation()
  
  const files = e.dataTransfer.files
  if (files && files.length > 0) {
    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      if (file.type.startsWith('image/')) {
        await handleImageUpload(file)
      }
    }
  }
}
```

### 4. 图片上传API接口

#### 4.1 API封装 (`src/api/upload.js`)

```javascript
// 单图片上传
export function uploadImage(file) {
  const formData = new FormData()
  formData.append('image', file)
  
  return request.post('/upload/image', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 批量图片上传
export function uploadImages(files) {
  const formData = new FormData()
  
  for (let i = 0; i < files.length; i++) {
    formData.append('images[]', files[i])
  }
  
  return request.post('/upload/images', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
```

#### 4.2 后台API要求

**上传接口**:
```
POST /api/upload/image
Content-Type: multipart/form-data

参数:
- image: File (图片文件)

响应:
{
  "success": true,
  "data": {
    "url": "/storage/images/20250124_123456_abc.jpg",
    "filename": "20250124_123456_abc.jpg",
    "size": 1024000
  },
  "message": "上传成功"
}
```

### 5. 文章管理页面集成

#### 5.1 组件使用

```vue
<!-- 在文章编辑表单中使用 -->
<el-form-item label="文章内容" prop="content">
  <RichTextEditor
    v-model="form.content"
    placeholder="请输入文章内容，支持富文本格式、图片上传和Word粘贴"
    height="400px"
  />
</el-form-item>

<!-- 或使用增强版（支持服务器上传） -->
<el-form-item label="文章内容" prop="content">
  <AdvancedRichTextEditor
    v-model="form.content"
    placeholder="请输入文章内容，支持富文本格式、图片上传和Word粘贴"
    height="400px"
    upload-mode="server"
  />
</el-form-item>
```

#### 5.2 文章内容显示

```vue
<!-- 查看文章时的富文本内容显示 -->
<div class="article-content" v-html="viewArticle.content"></div>
```

**显示样式**:
```css
/* 文章内容显示样式 */
:deep(.article-content) {
  line-height: 1.6;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

:deep(.article-content img) {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 10px auto;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.article-content table) {
  border-collapse: collapse;
  width: 100%;
  margin: 10px 0;
  border: 1px solid #ddd;
}

:deep(.article-content blockquote) {
  border-left: 4px solid #409eff;
  padding: 10px 16px;
  margin: 10px 0;
  background-color: #f9f9f9;
  border-radius: 4px;
}
```

## 🎨 功能特点

### 1. 图片上传功能

**Base64模式**:
- ✅ 无需后台支持
- ✅ 即时预览
- ✅ 适合小图片
- ❌ 文件大小限制
- ❌ 数据库存储压力大

**服务器上传模式**:
- ✅ 支持大文件
- ✅ 减少数据库压力
- ✅ 图片可复用
- ✅ 上传进度显示
- ❌ 需要后台支持

### 2. Word格式保持

**支持的格式**:
- ✅ 标题（H1-H6）
- ✅ 文本格式（粗体、斜体、下划线、删除线）
- ✅ 列表（有序、无序）
- ✅ 链接
- ✅ 引用
- ✅ 代码块
- ✅ 表格（基本支持）

### 3. 用户体验

**交互特性**:
- ✅ 拖拽上传图片
- ✅ 批量图片上传
- ✅ 上传进度提示
- ✅ 错误处理和降级
- ✅ 响应式设计
- ✅ 键盘快捷键支持

## 🧪 使用方法

### 1. 基础使用

```vue
<template>
  <RichTextEditor
    v-model="content"
    placeholder="请输入内容"
    height="300px"
    :disabled="false"
  />
</template>

<script setup>
import { ref } from 'vue'
import RichTextEditor from '@/components/RichTextEditor.vue'

const content = ref('')
</script>
```

### 2. 增强版使用

```vue
<template>
  <AdvancedRichTextEditor
    v-model="content"
    placeholder="请输入内容"
    height="400px"
    upload-mode="server"
    @change="handleContentChange"
  />
</template>

<script setup>
import { ref } from 'vue'
import AdvancedRichTextEditor from '@/components/AdvancedRichTextEditor.vue'

const content = ref('')

const handleContentChange = (newContent) => {
  console.log('内容变化:', newContent)
}
</script>
```

### 3. 组件方法

```javascript
// 获取组件实例
const editorRef = ref()

// 可用方法
editorRef.value.getHTML()        // 获取HTML内容
editorRef.value.getText()        // 获取纯文本
editorRef.value.setHTML(html)    // 设置HTML内容
editorRef.value.focus()          // 聚焦编辑器
editorRef.value.blur()           // 失焦编辑器
editorRef.value.insertImage(url) // 插入图片（增强版）
```

## 🚀 测试验证

### 1. 功能测试
- [ ] 富文本编辑功能正常
- [ ] 图片上传功能正常
- [ ] Word粘贴保持格式
- [ ] 拖拽上传图片
- [ ] 内容保存和显示
- [ ] 响应式布局

### 2. 兼容性测试
- [ ] Chrome浏览器
- [ ] Firefox浏览器
- [ ] Safari浏览器
- [ ] Edge浏览器
- [ ] 移动端浏览器

### 3. 性能测试
- [ ] 大文档编辑性能
- [ ] 图片上传速度
- [ ] 内存使用情况

## ✅ 实现状态

- [x] 基础富文本编辑器
- [x] 增强版富文本编辑器
- [x] 图片上传API接口
- [x] 文章管理页面集成
- [x] Word格式保持
- [x] 拖拽上传支持
- [x] 样式美化
- [x] 错误处理

---

**开发状态**: ✅ 完成  
**编辑器类型**: 基础版 + 增强版  
**测试地址**: `http://localhost:5174/admin/articles`  
**主要特性**: 富文本编辑 + 图片上传 + Word格式保持
