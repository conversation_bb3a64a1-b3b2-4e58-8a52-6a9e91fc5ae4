<template>
  <div class="bg-white min-h-screen flex flex-col">
    <!-- 固定头部 -->
    <div class="bg-white shadow-sm border-b sticky top-0 z-10">
      <div class="flex justify-between items-center p-6">
        <div class="flex items-center">
          <el-button
            type="primary"
            plain
            icon="ArrowLeft"
            @click="goBack"
            class="mr-4"
            size="default"
          >
            返回
          </el-button>
          <h2 class="text-2xl font-bold">机构详情</h2>
        </div>
        <div class="flex gap-2">
          <el-button
            type="primary"
            @click="editMode = !editMode"
            plain
            :icon="editMode ? 'Close' : 'EditPen'"
          >
            {{ editMode ? '取消编辑' : '编辑' }}
          </el-button>
          <el-button
            v-if="agency.status === 'pending'"
            type="success"
            @click="showApproveDialog"
            plain
            icon="Check"
          >
            审核通过
          </el-button>
          <el-button
            v-if="agency.status === 'pending'"
            type="danger"
            @click="showRejectDialog"
            plain
            icon="Close"
          >
            驳回
          </el-button>
          <el-button type="danger" @click="showDeleteDialog" plain icon="Delete"> 删除 </el-button>
        </div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="flex-grow p-6">
      <!-- 编辑模式 -->
      <el-form
        v-if="editMode"
        :model="agency"
        label-width="160px"
        class="grid grid-cols-1 lg:grid-cols-2 gap-6"
      >
        <el-form-item label="机构名称" required>
          <el-input v-model="agency.name" placeholder="请输入机构名称" />
        </el-form-item>
        <el-form-item label="统一社会信用代码" required>
          <el-input v-model="agency.credit_code" placeholder="请输入统一社会信用代码" />
        </el-form-item>
        <el-form-item label="法人代表" required>
          <el-input v-model="agency.legal_person" placeholder="请输入法人代表姓名" />
        </el-form-item>
        <el-form-item label="联系电话" required>
          <el-input v-model="agency.phone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="备案证明号">
          <el-input v-model="agency.license_number" placeholder="请输入备案证明号" />
        </el-form-item>
        <el-form-item label="注册地址" class="col-span-1 lg:col-span-2">
          <el-input v-model="agency.address" placeholder="请输入地址" />
        </el-form-item>
        <el-form-item label="组织机构代码证" class="col-span-1 lg:col-span-2">
          <el-upload
            class="upload-demo"
            action="/api/upload"
            :headers="uploadHeaders"
            :show-file-list="false"
            :before-upload="beforeUpload"
            :on-success="handleUploadSuccess"
            :data="{ type: 'agency_cert', folder: 'agency' }"
            accept="image/*"
          >
            <el-button type="primary" plain>上传图片</el-button>
            <template #tip>
              <div class="text-gray-400 mt-1 text-sm">
                请上传清晰的组织机构代码证图片，支持jpg/png格式
              </div>
            </template>
          </el-upload>
          <div v-if="agency.org_code_image" class="mt-2">
            <el-image
              :src="agency.org_code_image"
              alt="组织机构代码证"
              class="h-40 w-auto object-contain rounded border"
              :preview-src-list="[agency.org_code_image]"
              :append-to-body="true"
              :preview-teleported="true"
              :z-index="9999"
            />
          </div>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="agency.status" style="width: 100%">
            <el-option label="已通过" value="approved" />
            <el-option label="已驳回" value="rejected" />
          </el-select>
        </el-form-item>
        <el-form-item
          label="驳回原因"
          v-if="agency.status === 'rejected'"
          class="col-span-1 lg:col-span-2"
        >
          <el-input
            type="textarea"
            v-model="agency.reject_reason"
            placeholder="请输入驳回原因"
            rows="3"
          />
        </el-form-item>
        <div class="col-span-1 lg:col-span-2 flex justify-end gap-2 pt-4">
          <el-button @click="editMode = false">取消</el-button>
          <el-button type="primary" @click="saveAgency" :loading="loading">保存</el-button>
        </div>
      </el-form>

      <!-- 详情模式 -->
      <div v-else class="mt-4">
        <!-- 基本信息和组织机构代码证 -->
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
          <!-- 左侧基本信息 -->
          <div class="lg:col-span-3">
            <el-descriptions
              :title="agency.name"
              :column="1"
              border
              class="w-full"
              :labelStyle="{ width: '180px', fontWeight: '500' }"
            >
              <el-descriptions-item label="统一社会信用代码">
                {{ agency.credit_code }}
              </el-descriptions-item>
              <el-descriptions-item label="法人代表">
                {{ agency.legal_person }}
              </el-descriptions-item>
              <el-descriptions-item label="联系电话">
                {{ agency.phone }}
              </el-descriptions-item>
              <el-descriptions-item label="备案证明号">
                {{ agency.license_number || '未填写' }}
              </el-descriptions-item>
              <el-descriptions-item label="注册地址">
                {{ agency.address }}
              </el-descriptions-item>
              <el-descriptions-item label="审核状态">
                <el-tag :type="statusTagType(agency.status)" effect="light" round>
                  {{ statusText(agency.status) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item
                label="驳回原因"
                v-if="
                  agency.status === 'rejected' ||
                  (agency.status === 'pending' && agency.reject_reason)
                "
              >
                <span class="text-red-500">{{ agency.reject_reason || '无' }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="创建时间">
                {{ agency.created_at || '无记录' }}
              </el-descriptions-item>
              <el-descriptions-item label="更新时间">
                {{ agency.updated_at || '无记录' }}
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <!-- 右侧组织机构代码证图片 -->
          <div class="lg:col-span-1">
            <div class="bg-gray-50 p-4 rounded-lg h-full flex flex-col">
              <h3 class="text-lg font-semibold mb-2 text-gray-700">组织机构代码证</h3>
              <div class="flex-grow flex items-center justify-center overflow-hidden">
                <el-image
                  v-if="agency.org_code_image"
                  :src="agency.org_code_image"
                  fit="contain"
                  class="w-full max-h-full object-contain rounded border"
                  :preview-src-list="[agency.org_code_image]"
                  :append-to-body="true"
                  :preview-teleported="true"
                  :z-index="9999"
                />
                <div
                  v-else
                  class="flex items-center justify-center h-full w-full bg-gray-100 rounded border border-dashed border-gray-300"
                >
                  <span class="text-gray-400">暂无图片</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
          <el-card shadow="hover" class="!overflow-visible">
            <div class="flex items-center">
              <el-icon class="text-3xl text-blue-500 mr-4"><UserFilled /></el-icon>
              <div>
                <div class="text-gray-500 text-sm">旗下经纪人</div>
                <div class="text-2xl font-bold text-blue-600">
                  {{ agency.stats?.brokers_count || 0 }}
                </div>
              </div>
            </div>
          </el-card>

          <el-card shadow="hover" class="!overflow-visible">
            <div class="flex items-center">
              <el-icon class="text-3xl text-green-500 mr-4"><Document /></el-icon>
              <div>
                <div class="text-gray-500 text-sm">已通过审核经纪人</div>
                <div class="text-2xl font-bold text-green-600">
                  {{ agency.stats?.approved_brokers_count || 0 }}
                </div>
              </div>
            </div>
          </el-card>

          <el-card shadow="hover" class="!overflow-visible">
            <div class="flex items-center">
              <el-icon class="text-3xl text-orange-500 mr-4"><Calendar /></el-icon>
              <div>
                <div class="text-gray-500 text-sm">注册天数</div>
                <div class="text-2xl font-bold text-orange-600">{{ daysSinceRegistration }}</div>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 未来可以添加经纪人列表或其他相关信息 -->
        <div class="mt-8 border-t pt-6">
          <h3 class="text-lg font-bold mb-4">旗下经纪人列表</h3>
          <div class="text-gray-500 text-center py-8 bg-gray-50 rounded">
            该功能正在开发中，敬请期待...
          </div>
        </div>
      </div>
    </div>

    <!-- 驳回对话框 -->
    <el-dialog
      v-model="rejectDialogVisible"
      title="驳回申请"
      width="30%"
      :append-to-body="true"
      destroy-on-close
    >
      <el-form>
        <el-form-item label="驳回原因" required>
          <el-input
            type="textarea"
            v-model="rejectReason"
            placeholder="请输入驳回原因"
            rows="3"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex justify-end gap-2">
          <el-button @click="rejectDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="rejectAgencyHandler" :disabled="!rejectReason.trim()"
            >确认</el-button
          >
        </div>
      </template>
    </el-dialog>

    <!-- 通过确认对话框 -->
    <el-dialog
      v-model="approveDialogVisible"
      title="审核通过确认"
      width="30%"
      :append-to-body="true"
      destroy-on-close
    >
      <p>确定要通过该中介机构的审核申请吗？</p>
      <template #footer>
        <div class="flex justify-end gap-2">
          <el-button @click="approveDialogVisible = false">取消</el-button>
          <el-button type="success" @click="approveAgencyHandler">确认通过</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 删除确认对话框 -->
    <el-dialog
      v-model="deleteDialogVisible"
      title="删除确认"
      width="30%"
      :append-to-body="true"
      destroy-on-close
    >
      <div class="text-center">
        <el-icon class="text-5xl text-red-500 mb-4"><WarningFilled /></el-icon>
        <p class="text-lg mb-2">您确定要删除该中介机构吗？</p>
        <p class="text-gray-500 text-sm">此操作将软删除该机构及其相关数据，可通过数据库恢复</p>
      </div>
      <template #footer>
        <div class="flex justify-end gap-2">
          <el-button @click="deleteDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="deleteAgencyHandler" :loading="loading"
            >确认删除</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  EditPen,
  Check,
  Close,
  UserFilled,
  Document,
  Calendar,
  Delete,
  WarningFilled,
} from '@element-plus/icons-vue'
import {
  getAgencyDetail,
  updateAgency,
  approveAgency,
  rejectAgency,
  deleteAgency,
  verifyAgency,
} from '@/api/agency'

// 路由和导航
const route = useRoute()
const router = useRouter()
const agencyId = computed(() => route.params.id)

// 数据和状态
const agency = ref({
  id: 0,
  name: '',
  org_code: '',
  org_code_image: '',
  address: '',
  legal_name: '',
  contact: '',
  license_number: '',
  status: 'pending',
  reject_reason: '',
  credit_code: '', // 用于显示统一社会信用代码
  legal_person: '', // 用于显示法人代表
  phone: '', // 用于显示联系电话
  created_at: '',
  updated_at: '',
  stats: {
    brokers_count: 0,
    approved_brokers_count: 0,
  },
})

const loading = ref(false)
const editMode = ref(false)
const rejectDialogVisible = ref(false)
const approveDialogVisible = ref(false)
const deleteDialogVisible = ref(false)
const rejectReason = ref('')

// 上传相关
const uploadHeaders = computed(() => {
  return {
    Authorization: `Bearer ${localStorage.getItem('token')}`,
    Accept: 'application/json',
  }
})

// 计算属性
const daysSinceRegistration = computed(() => {
  // 使用创建时间代替注册时间
  if (!agency.value.created_at) return '未知'

  const createDate = new Date(agency.value.created_at)
  const today = new Date()
  const diffTime = Math.abs(today - createDate)
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  return diffDays
})

// 生命周期钩子
onMounted(() => {
  fetchAgencyDetail()
})

// 方法
async function fetchAgencyDetail() {
  loading.value = true
  try {
    const res = await getAgencyDetail(agencyId.value)

    // 将API返回的数据映射到本地数据模型
    agency.value = {
      ...agency.value,
      id: res.id,
      name: res.name,
      org_code: res.org_code || '',
      org_code_image: res.org_code_image || '',
      address: res.address || '',
      legal_name: res.legal_name || '',
      legal_person: res.legal_name || '', // 同步到legal_person字段
      contact: res.contact || '',
      phone: res.contact || '', // 同步到phone字段
      license_number: res.license_number || '',
      status: res.status || 'pending',
      reject_reason: res.reject_reason || '',
      credit_code: res.org_code || '', // 同步到credit_code字段
      user: res.user || {},
      created_at: res.created_at || '',
      updated_at: res.updated_at || '',
      // 其他字段保持不变
    }

    // 测试数据，实际应由API提供
    agency.value.stats = {
      brokers_count: res.stats?.brokers_count || 8,
      approved_brokers_count: res.stats?.approved_brokers_count || 5,
    }
  } catch (error) {
    console.error('获取机构详情失败:', error)
    ElMessage.error('获取机构详情失败')
  } finally {
    loading.value = false
  }
}

function goBack() {
  router.back()
}

async function saveAgency() {
  loading.value = true
  try {
    // 准备要发送的数据
    const updateData = {
      name: agency.value.name,
      org_code: agency.value.credit_code,
      address: agency.value.address,
      legal_name: agency.value.legal_person,
      contact: agency.value.phone,
      license_number: agency.value.license_number,
      status: agency.value.status,
      reject_reason: agency.value.status === 'rejected' ? agency.value.reject_reason : '',
      org_code_image: agency.value.org_code_image,
    }

    await updateAgency(agencyId.value, updateData)
    ElMessage.success('保存成功')
    editMode.value = false
    fetchAgencyDetail() // 重新获取数据
  } catch (error) {
    console.error('保存机构信息失败:', error)
    ElMessage.error('保存机构信息失败')
  } finally {
    loading.value = false
  }
}

// 上传相关方法
function beforeUpload(file) {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

function handleUploadSuccess(response) {
  agency.value.org_code_image = response.url
  ElMessage.success('上传成功')
}

// 审核相关方法
function showRejectDialog() {
  rejectReason.value = ''
  rejectDialogVisible.value = true
}

function showApproveDialog() {
  approveDialogVisible.value = true
}

function showDeleteDialog() {
  deleteDialogVisible.value = true
}

async function rejectAgencyHandler() {
  if (!rejectReason.value.trim()) {
    ElMessage.warning('请输入驳回原因')
    return
  }

  loading.value = true
  try {
    await verifyAgency(agencyId.value, 'rejected', rejectReason.value)
    ElMessage.success('已驳回申请')
    rejectDialogVisible.value = false
    fetchAgencyDetail() // 重新获取数据
  } catch (error) {
    console.error('驳回申请失败:', error)
    ElMessage.error('驳回申请失败')
  } finally {
    loading.value = false
  }
}

async function approveAgencyHandler() {
  loading.value = true
  try {
    await verifyAgency(agencyId.value, 'approved')
    ElMessage.success('已通过审核')
    approveDialogVisible.value = false
    fetchAgencyDetail() // 重新获取数据
  } catch (error) {
    console.error('通过审核失败:', error)
    ElMessage.error('通过审核失败')
  } finally {
    loading.value = false
  }
}

async function deleteAgencyHandler() {
  loading.value = true
  try {
    await deleteAgency(agencyId.value)
    ElMessage.success('删除成功')
    deleteDialogVisible.value = false
    router.push('/admin/agencies') // 删除后返回列表页
  } catch (error) {
    console.error('删除失败:', error)
    ElMessage.error('删除失败')
  } finally {
    loading.value = false
  }
}

// 状态展示相关方法
function statusText(status) {
  const statusMap = {
    unfilled: '未填写',
    pending: '待审核',
    approved: '已通过',
    rejected: '已驳回',
  }
  return statusMap[status] || status
}

function statusTagType(status) {
  const typeMap = {
    unfilled: 'info',
    pending: 'warning',
    approved: 'success',
    rejected: 'danger',
  }
  return typeMap[status] || 'info'
}
</script>

<style scoped>
.el-descriptions :deep(.el-descriptions__body) {
  width: 100%;
}

.el-descriptions :deep(.el-descriptions__label) {
  width: 160px;
  font-weight: 500;
}

:deep(.el-image-viewer__wrapper) {
  z-index: 9999 !important;
}

:deep(.el-image-viewer__mask) {
  z-index: 9998 !important;
}
</style>
