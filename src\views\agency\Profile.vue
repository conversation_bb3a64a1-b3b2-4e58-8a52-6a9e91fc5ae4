<template>
  <div class="bg-white p-6 rounded-lg shadow-md">
    <h2 class="text-2xl font-bold mb-4">企业信息管理</h2>
    <el-form :model="profile" :rules="rules" ref="profileRef" label-width="120px" class="max-w-2xl">
      <el-form-item label="企业名称" prop="name">
        <el-input
          v-model="profile.name"
          placeholder="请输入企业名称"
          :disabled="!isEditing"
        ></el-input>
      </el-form-item>
      <el-form-item label="组织机构代码" prop="org_code">
        <el-input
          v-model="profile.org_code"
          placeholder="请输入组织机构代码"
          :disabled="!isEditing"
        ></el-input>
      </el-form-item>
      <el-form-item label="注册地址" prop="address">
        <el-input
          v-model="profile.address"
          placeholder="请输入注册地址"
          :disabled="!isEditing"
        ></el-input>
      </el-form-item>
      <el-form-item label="法人姓名" prop="legal_name">
        <el-input
          v-model="profile.legal_name"
          placeholder="请输入法人姓名"
          :disabled="!isEditing"
        ></el-input>
      </el-form-item>
      <el-form-item label="联系方式" prop="contact">
        <el-input
          v-model="profile.contact"
          placeholder="请输入联系方式"
          :disabled="!isEditing"
        ></el-input>
      </el-form-item>
      <el-form-item label="备案证明号" prop="license_number">
        <el-input
          v-model="profile.license_number"
          placeholder="请输入备案证明号"
          :disabled="!isEditing"
        ></el-input>
      </el-form-item>
      <el-form-item label="审核状态">
        <el-tag
          :type="
            profile.status === 'approved'
              ? 'success'
              : profile.status === 'pending'
                ? 'warning'
                : 'danger'
          "
        >
          {{
            profile.status === 'approved'
              ? '已通过'
              : profile.status === 'pending'
                ? '待审核'
                : '未通过'
          }}
        </el-tag>
      </el-form-item>
      <el-form-item label="组织机构代码证" v-if="profile.org_code_image">
        <el-image
          :src="profile.org_code_image"
          style="width: 200px; height: 100px"
          fit="cover"
        ></el-image>
      </el-form-item>
      <el-form-item label="上传代码证" v-if="isEditing">
        <el-upload
          class="upload-demo"
          action="#"
          :on-preview="handlePreview"
          :on-remove="handleRemove"
          :before-remove="beforeRemove"
          multiple
          :limit="1"
          :on-exceed="handleExceed"
          :file-list="fileList"
        >
          <el-button size="small" type="primary">点击上传</el-button>
          <template #tip>
            <div class="el-upload__tip">只能上传图片文件，且不超过10mb</div>
          </template>
        </el-upload>
      </el-form-item>
      <el-form-item>
        <el-button v-if="!isEditing" type="primary" @click="isEditing = true">编辑</el-button>
        <template v-else>
          <el-button type="primary" @click="handleSave">保存</el-button>
          <el-button @click="handleCancel">取消</el-button>
        </template>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const isEditing = ref(false)
const profile = ref({
  name: '',
  org_code: '',
  address: '',
  legal_name: '',
  contact: '',
  license_number: '',
  status: 'pending',
  org_code_image: '',
})
const originalProfile = ref({})
const fileList = ref([])
const profileRef = ref(null)
const rules = {
  name: [{ required: true, message: '请输入企业名称', trigger: 'blur' }],
  org_code: [{ required: true, message: '请输入组织机构代码', trigger: 'blur' }],
  address: [{ required: true, message: '请输入注册地址', trigger: 'blur' }],
  legal_name: [{ required: true, message: '请输入法人姓名', trigger: 'blur' }],
  contact: [{ required: true, message: '请输入联系方式', trigger: 'blur' }],
  license_number: [{ required: true, message: '请输入备案证明号', trigger: 'blur' }],
}

onMounted(() => {
  fetchProfile()
})

const fetchProfile = () => {
  // 模拟数据
  profile.value = {
    name: '中介公司A',
    org_code: 'ORG001',
    address: '北京市朝阳区1号',
    legal_name: '张三',
    contact: '13800138000',
    license_number: 'LIC001',
    status: 'pending',
    org_code_image: 'https://via.placeholder.com/800x400',
  }
  originalProfile.value = { ...profile.value }
}

const handleSave = () => {
  profileRef.value.validate((valid) => {
    if (valid) {
      // 保存逻辑
      console.log('保存企业信息:', profile.value)
      isEditing.value = false
      originalProfile.value = { ...profile.value }
      // 模拟提交后审核状态更新
      profile.value.status = 'pending'
    }
  })
}

const handleCancel = () => {
  isEditing.value = false
  profile.value = { ...originalProfile.value }
}

const handlePreview = (file) => {
  console.log('预览文件:', file)
}

const handleRemove = (file, fileList) => {
  console.log('移除文件:', file, fileList)
}

const beforeRemove = (file, fileList) => {
  return true
}

const handleExceed = (files, fileList) => {
  console.log('超出文件限制:', files, fileList)
}
</script>

<style scoped>
/* 企业信息管理特定样式 */
</style>
