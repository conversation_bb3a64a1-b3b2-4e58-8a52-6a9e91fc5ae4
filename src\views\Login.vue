<template>
  <div
    class="min-h-screen flex items-center justify-center bg-gradient-to-r from-blue-100 to-blue-300"
  >
    <div class="flex w-full max-w-4xl bg-white rounded-2xl shadow-lg overflow-hidden">
      <!-- 左侧品牌宣传 -->
      <div
        class="hidden md:flex flex-col justify-center items-center w-1/2 bg-gradient-to-br from-blue-500 to-blue-700 text-white p-10"
      >
        <img src="/favicon.ico" alt="logo" class="w-16 h-16 mb-4" />
        <h2 class="text-3xl font-bold mb-2">DFHDemo</h2>
        <p class="text-lg opacity-80 mb-6">高效管理中介机构与经纪人，助力业务增长</p>
        <ul class="space-y-2 text-base opacity-90">
          <li>• 机构与经纪人一体化管理</li>
          <li>• 权限分明，数据安全</li>
          <li>• 统计分析，决策有据</li>
        </ul>
      </div>
      <!-- 右侧登录表单 -->
      <div class="w-full md:w-1/2 flex flex-col justify-center p-8 md:p-12">
        <div class="flex flex-col items-center mb-8">
          <img src="/favicon.ico" alt="logo" class="w-12 h-12 mb-2" />
          <h2 class="text-2xl font-bold text-gray-800">账号登录</h2>
        </div>
        <el-form ref="formRef" :model="form" :rules="rules" class="space-y-6">
          <el-form-item prop="username">
            <el-input
              v-model="form.username"
              placeholder="请输入用户名"
              :prefix-icon="User"
              size="large"
            />
          </el-form-item>
          <el-form-item prop="password">
            <el-input
              v-model="form.password"
              type="password"
              placeholder="请输入密码"
              :prefix-icon="Lock"
              size="large"
              @keyup.enter="handleLogin"
            />
          </el-form-item>
          <el-button
            type="primary"
            class="w-full !h-12 !text-lg"
            :loading="loading"
            @click="handleLogin"
            size="large"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form>
        <div class="flex justify-between mt-6">
          <el-button type="text" @click="goRegister">注册账号</el-button>
          <el-button type="text" @click="goHome">返回首页</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User, Lock } from '@element-plus/icons-vue'
import { login } from '@/api/auth'

const router = useRouter()
const formRef = ref(null)
const loading = ref(false)
const form = ref({
  username: '',
  password: '',
})

const rules = {
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
}

async function handleLogin() {
  if (loading.value) return
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    loading.value = true
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    const res = await login(form.value.username, form.value.password)
    if (!res?.token) {
      ElMessage.error('登录失败：未获取到token')
      return
    }
    localStorage.setItem('token', res.token)
    localStorage.setItem('user', JSON.stringify(res.user))
    ElMessage.success('登录成功')
    switch (res.user?.role) {
      case 'super_admin':
        router.push('/admin')
        break
      case 'agency_admin':
        router.push('/agency')
        break
      case 'broker':
        router.push('/broker')
        break
      default:
        router.push('/admin')
    }
  } catch (error) {
    ElMessage.error('登录失败：' + (error.response?.data?.message || '请检查账号密码'))
  } finally {
    loading.value = false
  }
}

function goRegister() {
  router.push('/register')
}
function goHome() {
  router.push('/')
}
</script>

<style scoped>
/***** 美化登录页，仿阿里云风格 *****/
:deep(.el-input__wrapper) {
  background: #f4f8fb;
  border-radius: 8px;
  border: 1px solid #e0e6ed;
}
:deep(.el-input__inner) {
  font-size: 16px;
}
:deep(.el-form-item) {
  margin-bottom: 24px;
}
:deep(.el-button--primary) {
  background: linear-gradient(90deg, #409eff 0%, #66b1ff 100%);
  border: none;
}
:deep(.el-button--text) {
  color: #409eff;
  font-size: 15px;
}
</style>
