<template>
  <div class="rich-text-editor">
    <div ref="editorRef" class="editor-container"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import Quill from 'quill'
import 'quill/dist/quill.snow.css'

// 定义props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '请输入内容...'
  },
  height: {
    type: String,
    default: '300px'
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

// 定义emits
const emit = defineEmits(['update:modelValue', 'change'])

// 响应式数据
const editorRef = ref()
let quill = null

// 图片上传处理函数
const imageHandler = () => {
  const input = document.createElement('input')
  input.setAttribute('type', 'file')
  input.setAttribute('accept', 'image/*')
  input.click()

  input.onchange = () => {
    const file = input.files[0]
    if (file) {
      // 检查文件大小（限制为5MB）
      if (file.size > 5 * 1024 * 1024) {
        alert('图片大小不能超过5MB')
        return
      }

      // 检查文件类型
      if (!file.type.startsWith('image/')) {
        alert('请选择图片文件')
        return
      }

      // 转换为base64
      const reader = new FileReader()
      reader.onload = (e) => {
        const base64 = e.target.result
        
        // 获取当前光标位置
        const range = quill.getSelection()
        const index = range ? range.index : quill.getLength()
        
        // 插入图片
        quill.insertEmbed(index, 'image', base64)
        
        // 移动光标到图片后面
        quill.setSelection(index + 1)
      }
      reader.readAsDataURL(file)
    }
  }
}

// Quill配置
const quillOptions = {
  theme: 'snow',
  placeholder: props.placeholder,
  modules: {
    toolbar: {
      container: [
        [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
        [{ 'font': [] }],
        [{ 'size': ['small', false, 'large', 'huge'] }],
        ['bold', 'italic', 'underline', 'strike'],
        [{ 'color': [] }, { 'background': [] }],
        [{ 'script': 'sub' }, { 'script': 'super' }],
        [{ 'list': 'ordered' }, { 'list': 'bullet' }],
        [{ 'indent': '-1' }, { 'indent': '+1' }],
        [{ 'align': [] }],
        ['blockquote', 'code-block'],
        ['link', 'image', 'video'],
        ['clean']
      ],
      handlers: {
        image: imageHandler
      }
    },
    history: {
      delay: 1000,
      maxStack: 50,
      userOnly: true
    }
  }
}

// 初始化编辑器
const initEditor = () => {
  if (editorRef.value) {
    quill = new Quill(editorRef.value, quillOptions)
    
    // 设置编辑器高度
    const editorContainer = editorRef.value.querySelector('.ql-editor')
    if (editorContainer) {
      editorContainer.style.minHeight = props.height
    }
    
    // 设置初始内容
    if (props.modelValue) {
      quill.root.innerHTML = props.modelValue
    }
    
    // 监听内容变化
    quill.on('text-change', () => {
      const html = quill.root.innerHTML
      const isEmpty = quill.getText().trim().length === 0
      const content = isEmpty ? '' : html
      
      emit('update:modelValue', content)
      emit('change', content)
    })
    
    // 设置禁用状态
    if (props.disabled) {
      quill.disable()
    }
    
    // 支持粘贴Word内容并保持格式
    quill.clipboard.addMatcher(Node.ELEMENT_NODE, (node, delta) => {
      // 保持基本的格式
      if (node.tagName === 'P' || node.tagName === 'DIV') {
        return delta
      }
      if (node.tagName === 'STRONG' || node.tagName === 'B') {
        return delta.compose(new Quill.import('delta')().retain(delta.length(), { bold: true }))
      }
      if (node.tagName === 'EM' || node.tagName === 'I') {
        return delta.compose(new Quill.import('delta')().retain(delta.length(), { italic: true }))
      }
      if (node.tagName === 'U') {
        return delta.compose(new Quill.import('delta')().retain(delta.length(), { underline: true }))
      }
      return delta
    })
  }
}

// 监听modelValue变化
watch(() => props.modelValue, (newValue) => {
  if (quill && newValue !== quill.root.innerHTML) {
    quill.root.innerHTML = newValue || ''
  }
})

// 监听disabled状态
watch(() => props.disabled, (newValue) => {
  if (quill) {
    if (newValue) {
      quill.disable()
    } else {
      quill.enable()
    }
  }
})

// 生命周期
onMounted(() => {
  initEditor()
})

onBeforeUnmount(() => {
  if (quill) {
    quill = null
  }
})

// 暴露方法给父组件
defineExpose({
  getQuill: () => quill,
  getHTML: () => quill ? quill.root.innerHTML : '',
  getText: () => quill ? quill.getText() : '',
  setHTML: (html) => {
    if (quill) {
      quill.root.innerHTML = html
    }
  },
  focus: () => {
    if (quill) {
      quill.focus()
    }
  },
  blur: () => {
    if (quill) {
      quill.blur()
    }
  }
})
</script>

<style scoped>
.rich-text-editor {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: white;
}

.rich-text-editor:hover {
  border-color: #c0c4cc;
}

.rich-text-editor:focus-within {
  border-color: #409eff;
}

:deep(.ql-toolbar) {
  border-bottom: 1px solid #e4e7ed;
  border-top: none;
  border-left: none;
  border-right: none;
}

:deep(.ql-container) {
  border: none;
  font-size: 14px;
}

:deep(.ql-editor) {
  line-height: 1.6;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

:deep(.ql-editor.ql-blank::before) {
  color: #c0c4cc;
  font-style: normal;
}

:deep(.ql-snow .ql-tooltip) {
  z-index: 9999;
}

/* 图片样式 */
:deep(.ql-editor img) {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 10px auto;
}

/* 表格样式 */
:deep(.ql-editor table) {
  border-collapse: collapse;
  width: 100%;
  margin: 10px 0;
}

:deep(.ql-editor table td, .ql-editor table th) {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

:deep(.ql-editor table th) {
  background-color: #f5f5f5;
  font-weight: bold;
}

/* 代码块样式 */
:deep(.ql-editor pre.ql-syntax) {
  background-color: #f5f5f5;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
  margin: 10px 0;
  overflow-x: auto;
}

/* 引用样式 */
:deep(.ql-editor blockquote) {
  border-left: 4px solid #409eff;
  padding-left: 16px;
  margin: 10px 0;
  color: #666;
  background-color: #f9f9f9;
  padding: 10px 16px;
  border-radius: 4px;
}
</style>
