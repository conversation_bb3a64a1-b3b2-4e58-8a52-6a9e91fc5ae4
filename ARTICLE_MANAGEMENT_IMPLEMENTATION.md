# 文章管理功能实现

## 🎯 实现目标

1. 移除文章分类管理的批量删除功能
2. 实现超级管理员的文章管理功能
3. 保持与文章分类管理一致的主题和样式

## ✅ 主要实现内容

### 1. 移除文章分类管理的批量删除功能

#### 1.1 移除的内容
- ✅ 移除批量删除按钮
- ✅ 移除表格选择列
- ✅ 移除相关变量和方法
- ✅ 移除批量删除API导入

#### 1.2 修改后的界面
```vue
<!-- 修改前：有批量删除功能 -->
<div class="mb-4 flex items-center space-x-2">
  <el-button type="primary" @click="handleAdd">新增分类</el-button>
  <el-button type="danger" :disabled="selectedCategories.length === 0">
    批量删除 ({{ selectedCategories.length }})
  </el-button>
  <el-button @click="fetchCategories">刷新</el-button>
</div>

<el-table @selection-change="handleSelectionChange">
  <el-table-column type="selection" width="55" />
  <!-- 其他列 -->
</el-table>

<!-- 修改后：简化的界面 -->
<div class="mb-4 flex items-center space-x-2">
  <el-button type="primary" @click="handleAdd">新增分类</el-button>
  <el-button @click="fetchCategories">刷新</el-button>
</div>

<el-table>
  <!-- 移除选择列，直接显示数据列 -->
</el-table>
```

### 2. 文章管理功能实现

#### 2.1 API接口封装 (`src/api/admin/articles.js`)
```javascript
// 获取文章列表（支持分页和筛选）
export function getArticleList(params = {}) {
  return request.get('/admin/articles', { params })
}

// 创建文章
export function createArticle(data) {
  return request.post('/admin/articles', data)
}

// 更新文章
export function updateArticle(id, data) {
  return request.put(`/admin/articles/${id}`, data)
}

// 删除文章
export function deleteArticle(id) {
  return request.delete(`/admin/articles/${id}`)
}

// 获取文章详情
export function getArticleDetail(id) {
  return request.get(`/admin/articles/${id}`)
}
```

#### 2.2 API数据格式
```json
// 列表请求参数
{
  "page": 1,
  "category_id": 1,
  "keyword": "搜索关键词"
}

// 列表响应
{
  "current_page": 1,
  "data": [
    {
      "id": 1,
      "title": "文章标题",
      "author": "作者",
      "summary": "摘要",
      "views": 156,
      "published_at": "2025-01-20 10:00:00",
      "category": {
        "id": 1,
        "name": "分类名称"
      },
      "created_at": "2025-01-20 10:00:00",
      "updated_at": "2025-01-20 10:00:00"
    }
  ],
  "total": 10
}

// 创建/更新请求
{
  "title": "文章标题",
  "content": "文章内容",
  "category_id": 1,
  "author": "作者",
  "summary": "摘要",
  "published_at": "2025-01-20 10:00:00"
}
```

#### 2.3 页面功能特点

##### 搜索和筛选功能
```vue
<div class="mb-4 flex items-center space-x-4">
  <!-- 关键词搜索 -->
  <el-input
    v-model="searchKeyword"
    placeholder="搜索文章标题或作者"
    style="width: 300px"
    clearable
    @keyup.enter="handleSearch"
  >
    <template #prefix>
      <el-icon><Search /></el-icon>
    </template>
  </el-input>
  
  <!-- 分类筛选 -->
  <el-select
    v-model="selectedCategoryId"
    placeholder="选择分类"
    style="width: 200px"
    clearable
    @change="handleCategoryChange"
  >
    <el-option
      v-for="category in categories"
      :key="category.id"
      :label="category.name"
      :value="category.id"
    />
  </el-select>
  
  <el-button type="primary" @click="handleSearch">搜索</el-button>
  <el-button @click="handleReset">重置</el-button>
</div>
```

##### 文章列表展示
```vue
<el-table :data="articles" border style="width: 100%" v-loading="loading">
  <el-table-column prop="id" label="ID" width="80" />
  
  <!-- 可点击的文章标题 -->
  <el-table-column prop="title" label="文章标题" min-width="200">
    <template #default="{ row }">
      <div class="font-medium text-blue-600 cursor-pointer hover:text-blue-800" @click="handleView(row)">
        {{ row.title }}
      </div>
    </template>
  </el-table-column>

  <!-- 作者信息 -->
  <el-table-column prop="author" label="作者" width="120">
    <template #default="{ row }">
      <div class="font-medium">{{ row.author }}</div>
    </template>
  </el-table-column>

  <!-- 分类标签 -->
  <el-table-column prop="category" label="分类" width="120">
    <template #default="{ row }">
      <el-tag size="small" type="info">{{ row.category?.name || '未分类' }}</el-tag>
    </template>
  </el-table-column>

  <!-- 浏览量 -->
  <el-table-column prop="views" label="浏览量" width="100">
    <template #default="{ row }">
      <span class="text-gray-600">{{ row.views || 0 }}</span>
    </template>
  </el-table-column>

  <!-- 时间信息 -->
  <el-table-column prop="published_at" label="发布时间" width="180" />
  <el-table-column prop="created_at" label="创建时间" width="180" />

  <!-- 操作按钮 -->
  <el-table-column label="操作" width="200" fixed="right">
    <template #default="{ row }">
      <el-button size="small" type="info" @click="handleView(row)">查看</el-button>
      <el-button size="small" type="primary" @click="handleEdit(row)">编辑</el-button>
      <el-button size="small" type="danger" @click="handleDelete(row)">删除</el-button>
    </template>
  </el-table-column>
</el-table>
```

##### 新增/编辑表单
```vue
<el-form ref="formRef" :model="form" :rules="formRules" label-width="100px">
  <!-- 文章标题 -->
  <el-form-item label="文章标题" prop="title">
    <el-input v-model="form.title" placeholder="请输入文章标题" maxlength="200" show-word-limit />
  </el-form-item>

  <!-- 作者 -->
  <el-form-item label="作者" prop="author">
    <el-input v-model="form.author" placeholder="请输入作者姓名" maxlength="50" show-word-limit />
  </el-form-item>

  <!-- 分类选择 -->
  <el-form-item label="分类" prop="category_id">
    <el-select v-model="form.category_id" placeholder="请选择分类" style="width: 100%">
      <el-option v-for="category in categories" :key="category.id" :label="category.name" :value="category.id" />
    </el-select>
  </el-form-item>

  <!-- 摘要 -->
  <el-form-item label="摘要" prop="summary">
    <el-input v-model="form.summary" type="textarea" :rows="3" placeholder="请输入文章摘要" maxlength="500" show-word-limit />
  </el-form-item>

  <!-- 发布时间 -->
  <el-form-item label="发布时间" prop="published_at">
    <el-date-picker
      v-model="form.published_at"
      type="datetime"
      placeholder="选择发布时间"
      style="width: 100%"
      format="YYYY-MM-DD HH:mm:ss"
      value-format="YYYY-MM-DD HH:mm:ss"
    />
  </el-form-item>

  <!-- 文章内容 -->
  <el-form-item label="文章内容" prop="content">
    <el-input v-model="form.content" type="textarea" :rows="10" placeholder="请输入文章内容" />
  </el-form-item>
</el-form>
```

##### 文章查看对话框
```vue
<el-dialog v-model="viewDialogVisible" title="查看文章" width="800px">
  <div v-if="viewArticle">
    <!-- 文章标题和基本信息 -->
    <div class="mb-4">
      <h3 class="text-xl font-bold mb-2">{{ viewArticle.title }}</h3>
      <div class="text-sm text-gray-600 mb-4">
        <span>作者：{{ viewArticle.author }}</span>
        <span class="ml-4">分类：{{ viewArticle.category?.name || '未分类' }}</span>
        <span class="ml-4">发布时间：{{ formatDate(viewArticle.published_at) }}</span>
        <span class="ml-4">浏览量：{{ viewArticle.views || 0 }}</span>
      </div>
    </div>
    
    <!-- 摘要 -->
    <div v-if="viewArticle.summary" class="mb-4">
      <h4 class="font-medium mb-2">摘要：</h4>
      <p class="text-gray-700 bg-gray-50 p-3 rounded">{{ viewArticle.summary }}</p>
    </div>
    
    <!-- 内容 -->
    <div>
      <h4 class="font-medium mb-2">内容：</h4>
      <div class="text-gray-700 bg-gray-50 p-4 rounded max-h-96 overflow-y-auto whitespace-pre-wrap">
        {{ viewArticle.content }}
      </div>
    </div>
  </div>
</el-dialog>
```

#### 2.4 核心功能实现

##### 搜索和筛选
```javascript
// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  fetchArticles()
}

// 分类筛选处理
const handleCategoryChange = () => {
  currentPage.value = 1
  fetchArticles()
}

// 重置筛选条件
const handleReset = () => {
  searchKeyword.value = ''
  selectedCategoryId.value = null
  currentPage.value = 1
  fetchArticles()
}
```

##### 数据获取
```javascript
const fetchArticles = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      page_size: pageSize.value
    }
    
    // 添加搜索条件
    if (searchKeyword.value) {
      params.keyword = searchKeyword.value
    }
    if (selectedCategoryId.value) {
      params.category_id = selectedCategoryId.value
    }
    
    const response = await getArticleList(params)
    
    articles.value = response.data || []
    total.value = response.total || 0
    currentPage.value = response.current_page || 1
  } catch (error) {
    ElMessage.error('获取文章列表失败')
    // 使用模拟数据作为降级
    loadMockData()
  } finally {
    loading.value = false
  }
}
```

##### 表单验证
```javascript
const formRules = {
  title: [
    { required: true, message: '请输入文章标题', trigger: 'blur' },
    { min: 2, max: 200, message: '标题长度在 2 到 200 个字符', trigger: 'blur' }
  ],
  author: [
    { required: true, message: '请输入作者姓名', trigger: 'blur' },
    { min: 2, max: 50, message: '作者姓名长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  category_id: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  summary: [
    { max: 500, message: '摘要长度不能超过 500 个字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入文章内容', trigger: 'blur' }
  ],
  published_at: [
    { required: true, message: '请选择发布时间', trigger: 'change' }
  ]
}
```

## 🎨 样式设计特点

### 1. 与文章分类管理保持一致
- **页面布局**: 相同的 `bg-white p-6 rounded-lg shadow-md` 布局
- **标题样式**: 统一的 `text-2xl font-bold mb-4` 样式
- **按钮组**: 相同的操作按钮布局和间距
- **表格样式**: 统一的边框表格和列设计
- **分页组件**: 相同的分页布局和样式
- **对话框**: 统一的表单样式和验证

### 2. 增强的用户体验
- **搜索功能**: 支持关键词搜索和分类筛选
- **可点击标题**: 文章标题可点击查看详情
- **状态标签**: 分类使用标签显示
- **浏览量显示**: 直观的数据展示
- **详情查看**: 专门的查看对话框
- **表单验证**: 完善的验证规则和错误提示

### 3. 响应式设计
- **表格适配**: 固定右侧操作列，其他列自适应
- **对话框**: 合适的宽度和居中显示
- **搜索栏**: 在不同屏幕尺寸下自动调整

## 🧪 测试验证

### 1. 访问地址
- **文章分类管理**: `http://localhost:5174/admin/article-categories`
- **文章管理**: `http://localhost:5174/admin/articles`
- **需要登录**: 使用管理员账号登录后访问

### 2. 功能测试
- [ ] 文章列表正常加载
- [ ] 搜索功能正常
- [ ] 分类筛选功能正常
- [ ] 新增文章功能正常
- [ ] 编辑文章功能正常
- [ ] 查看文章功能正常
- [ ] 删除文章功能正常
- [ ] 分页功能正常
- [ ] 表单验证正常

### 3. 样式测试
- [ ] 页面布局与分类管理一致
- [ ] 搜索栏样式美观
- [ ] 表格样式统一
- [ ] 对话框样式美观
- [ ] 响应式效果良好

## ✅ 实现状态

- [x] 移除分类管理批量删除功能
- [x] API接口封装
- [x] 文章管理页面开发
- [x] 搜索和筛选功能
- [x] 表单验证
- [x] 查看功能
- [x] 编辑功能
- [x] 删除功能
- [x] 分页功能
- [x] 错误处理
- [x] 样式统一

---

**开发状态**: ✅ 完成  
**分类管理优化**: ✅ 完成  
**文章管理**: ✅ 完成  
**测试地址**: `http://localhost:5174/admin/articles`
