<template>
  <div class="bg-white p-6 rounded-lg shadow-md">
    <div class="max-w-3xl mx-auto">
      <h2 class="text-2xl font-bold mb-6">机构入驻申请</h2>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="160px"
        :disabled="!canEdit"
        class="mb-6"
      >
        <el-form-item label="企业名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入企业名称"></el-input>
        </el-form-item>
        <el-form-item label="统一社会信用代码" prop="org_code">
          <el-input v-model="form.org_code" placeholder="请输入统一社会信用代码"></el-input>
        </el-form-item>
        <el-form-item label="注册地址" prop="address">
          <el-input v-model="form.address" placeholder="请输入注册地址"></el-input>
        </el-form-item>
        <el-form-item label="法人姓名" prop="legal_name">
          <el-input v-model="form.legal_name" placeholder="请输入法人姓名"></el-input>
        </el-form-item>
        <el-form-item label="联系电话" prop="contact">
          <el-input v-model="form.contact" placeholder="请输入联系电话"></el-input>
        </el-form-item>
        <el-form-item label="备案证明号" prop="license_number">
          <el-input v-model="form.license_number" placeholder="请输入备案证明号"></el-input>
        </el-form-item>
        <el-form-item label="组织机构代码证" prop="org_code_image">
          <el-upload
            class="upload-demo"
            action="/api/upload"
            :show-file-list="false"
            :before-upload="beforeUpload"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :disabled="!canEdit"
            :headers="uploadHeaders"
            :data="uploadData"
            accept="image/*"
          >
            <el-button type="primary" :disabled="!canEdit">上传图片</el-button>
            <template #tip>
              <div class="text-gray-400 mt-2">请上传清晰的组织机构代码证图片，支持jpg/png格式</div>
            </template>
          </el-upload>
          <div v-if="form.org_code_image" class="mt-2">
            <img :src="form.org_code_image" alt="组织机构代码证" class="h-24 rounded border" />
          </div>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSubmit" :disabled="!canEdit">提交申请</el-button>
        </el-form-item>
      </el-form>
      <el-alert
        v-if="!canEdit && status !== 'passed'"
        :title="statusMessage"
        type="warning"
        show-icon
        class="mb-4"
      />
      <el-alert
        v-if="status === 'rejected' && reject_reason"
        :title="'驳回原因：' + reject_reason"
        type="error"
        show-icon
        class="mb-4"
      />
      <el-result
        v-if="status === 'pending'"
        icon="clock"
        title="等待审核"
        sub-title="您的申请已提交，请耐心等待管理员审核。"
      />
      <el-result
        v-if="status === 'passed'"
        icon="success"
        title="已通过审核"
        sub-title="您的机构资料已通过审核，可正常使用系统功能。"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, inject } from 'vue'
import { ElMessage } from 'element-plus'
import service from '@/utils/request'

const formRef = ref(null)
const form = ref({
  name: '',
  org_code: '',
  org_code_image: '',
  address: '',
  legal_name: '',
  contact: '',
  license_number: '',
})
const status = ref('unfilled')
const reject_reason = ref('')
const statusMessage = ref('')
const canEdit = ref(true)

// 注入父组件提供的共享数据和方法
const sharedAgencyInfo = inject('agencyInfo')
const parentFetchAgencyInfo = inject('fetchAgencyInfo')

const uploadHeaders = computed(() => {
  const token = localStorage.getItem('token')
  return {
    Authorization: token ? `Bearer ${token}` : '',
    Accept: 'application/json, text/plain, */*',
  }
})

const uploadData = {
  type: 'agency_cert',
  folder: 'agency',
}

const rules = {
  name: [{ required: true, message: '请输入企业名称', trigger: 'blur' }],
  org_code: [
    { required: true, message: '请输入统一社会信用代码', trigger: 'blur' },
    {
      pattern: /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/,
      message: '请输入正确的统一社会信用代码',
      trigger: 'blur',
    },
  ],
  address: [{ required: true, message: '请输入注册地址', trigger: 'blur' }],
  legal_name: [{ required: true, message: '请输入法人姓名', trigger: 'blur' }],
  contact: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
  ],
  license_number: [{ required: true, message: '请输入备案证明号', trigger: 'blur' }],
  org_code_image: [{ required: true, message: '请上传组织机构代码证图片', trigger: 'change' }],
}

function beforeUpload(file) {
  const isImage = file.type.startsWith('image/')
  if (!isImage) {
    ElMessage.error('只能上传图片文件')
    return false
  }
  return true
}

function handleUploadSuccess(res) {
  if (res && res.url) {
    form.value.org_code_image = res.url
    ElMessage.success(res.message || '上传成功')
  } else {
    ElMessage.error('上传失败：未获取到文件URL')
  }
}

function handleUploadError(error) {
  console.error('上传失败:', error)
  ElMessage.error('上传失败：' + (error.message || '请稍后重试'))
}

async function handleSubmit() {
  await formRef.value.validate()
  try {
    const res = await service.put('/agency/profile', form.value)
    const data = res?.data || res
    ElMessage.success(data.message || '提交成功，等待审核')
    // 使用父组件提供的方法刷新状态
    await parentFetchAgencyInfo()
    updateLocalData()
  } catch (e) {
    ElMessage.error(e?.response?.data?.message || '提交失败')
  }
}

// 从共享数据更新本地状态
function updateLocalData() {
  if (sharedAgencyInfo.value) {
    const data = sharedAgencyInfo.value
    status.value = data?.status || 'unfilled'
    reject_reason.value = data?.reject_reason || ''
    statusMessage.value = data?.message || ''
    if (data.agency) {
      form.value = {
        name: data.agency.name || '',
        org_code: data.agency.org_code || '',
        org_code_image: data.agency.org_code_image || '',
        address: data.agency.address || '',
        legal_name: data.agency.legal_name || '',
        contact: data.agency.contact || '',
        license_number: data.agency.license_number || '',
      }
    }
    canEdit.value = status.value === 'unfilled' || status.value === 'rejected'
  }
}

onMounted(() => {
  // 如果父组件已经获取了数据，直接使用
  if (sharedAgencyInfo.value) {
    updateLocalData()
  } else {
    // 否则请求一次数据
    parentFetchAgencyInfo().then(() => {
      updateLocalData()
    })
  }
})
</script>

<style scoped>
.upload-demo {
  display: flex;
  align-items: center;
}
.upload-demo .el-button {
  margin-right: 16px;
}
</style>
