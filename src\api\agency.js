import request from '@/utils/request'

// 获取机构列表
export function getAgencyList(params) {
    return request.get('/admin/agencies', { params })
}

// 获取机构详情
export function getAgencyDetail(id) {
    return request.get(`/admin/agencies/${id}`)
}

// 更新机构信息
export function updateAgency(id, data) {
    return request.put(`/admin/agencies/${id}`, data)
}

// 删除机构（软删除）
export function deleteAgency(id) {
    return request.delete(`/admin/agencies/${id}`)
}

// 审核机构（统一的审核API）
export function verifyAgency(id, status, reject_reason = '') {
    return request.post(`/admin/agencies/${id}/verify`, {
        status,
        reject_reason
    })
}

// 以下两个方法保留用于向后兼容
// 审核通过机构
export function approveAgency(id) {
    return verifyAgency(id, 'approved')
}

// 驳回机构申请
export function rejectAgency(id, data) {
    return verifyAgency(id, 'rejected', data.reject_reason)
}

// 获取机构信息（针对机构管理员自己）
export function getAgencyInfo() {
    return request.get('/agency/info')
}

// 更新机构资料（针对机构管理员自己）
export function updateAgencyProfile(data) {
    return request.put('/agency/profile', data)
}

// 经纪人管理相关API
// 获取经纪人列表
export function getBrokerList(params) {
    return request.get('/agency/brokers', { params })
}

// 添加经纪人
export function addBroker(data) {
    return request.post('/agency/brokers', data, {
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    })
}