{"name": "vue3temp", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/vite": "^4.1.5", "autoprefixer": "^10.4.21", "axios": "^1.10.0", "echarts": "^5.6.0", "element-plus": "^2.10.4", "pinia": "^3.0.3", "qrcodejs2": "^0.0.2", "quill": "^1.3.7", "quill-image-resize-module": "^3.0.0", "quill-image-uploader": "^1.3.0", "tailwindcss": "^4.1.5", "vue": "^3.5.17", "vue-quill-editor": "^3.0.6", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "prettier": "3.5.3", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2"}}