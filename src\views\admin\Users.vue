<template>
  <div class="bg-white p-6 rounded-lg shadow-md">
    <h2 class="text-2xl font-bold mb-4">账号管理</h2>

    <!-- 筛选条件 -->
    <div class="filter-container mb-4">
      <el-select v-model="filters.role" placeholder="选择角色" clearable @change="handleFilterChange" class="filter-select">
        <el-option label="全部角色" value=""></el-option>
        <el-option v-for="(label, value) in USER_ROLE_LABELS" :key="value" :label="label" :value="value"></el-option>
      </el-select>

      <el-select v-model="filters.status" placeholder="选择状态" clearable @change="handleFilterChange" class="filter-select">
        <el-option label="全部状态" value=""></el-option>
        <el-option v-for="(label, value) in USER_STATUS_LABELS" :key="value" :label="label" :value="value"></el-option>
      </el-select>

      <el-input v-model="filters.keyword" placeholder="搜索手机号" clearable @keyup.enter="handleFilterChange" @clear="handleFilterChange" class="filter-input">
        <template #append>
          <el-button @click="handleFilterChange" :icon="Search"></el-button>
        </template>
      </el-input>
    </div>

    <el-table :data="users" border style="width: 100%; table-layout: fixed;" v-loading="loading">
      <el-table-column prop="id" label="ID" width="80" fixed="left"></el-table-column>
      <el-table-column prop="phone" label="手机号" min-width="150"></el-table-column>
      <el-table-column prop="role" label="角色" min-width="120">
        <template #default="scope">
          <el-tag :type="getRoleTagType(scope.row.role)">
            {{ USER_ROLE_LABELS[scope.row.role] || scope.row.role }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" min-width="100">
        <template #default="scope">
          <el-tag :type="getStatusTagType(scope.row.status)">
            {{ USER_STATUS_LABELS[scope.row.status] || scope.row.status }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="created_at" label="创建时间" min-width="180">
        <template #default="scope">
          {{ formatDateTime(scope.row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120" fixed="right">
        <template #default="scope">
          <el-button size="small" type="primary" @click="handleChangePassword(scope.row)">修改密码</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination v-model:current-page="currentPage" :page-size="pageSize" :total="total" layout="total, prev, pager, next" class="mt-4"
      @current-change="handlePageChange" />

    <!-- 修改密码对话框 -->
    <el-dialog v-model="passwordDialogVisible" title="修改密码" width="400px" :before-close="handleClosePasswordDialog">
      <el-form ref="passwordFormRef" :model="passwordForm" :rules="passwordRules" label-width="80px">
        <el-form-item label="用户" prop="phone">
          <el-input v-model="passwordForm.phone" disabled></el-input>
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input v-model="passwordForm.newPassword" type="password" placeholder="请输入新密码" show-password></el-input>
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="passwordForm.confirmPassword" type="password" placeholder="请再次输入新密码" show-password></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClosePasswordDialog">取消</el-button>
          <el-button type="primary" @click="handleConfirmChangePassword" :loading="passwordLoading">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import {
  getUserList,
  changeUserPassword,
  USER_ROLE_LABELS,
  USER_STATUS_LABELS
} from '@/api/admin/users'

const users = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const loading = ref(false)

// 筛选条件
const filters = ref({
  role: '',
  status: '',
  keyword: ''
})

// 修改密码相关
const passwordDialogVisible = ref(false)
const passwordLoading = ref(false)
const passwordFormRef = ref(null)
const passwordForm = ref({
  userId: null,
  phone: '',
  newPassword: '',
  confirmPassword: ''
})

// 密码表单验证规则
const passwordRules = {
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度应为6-20位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.value.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

onMounted(() => {
  fetchUsers()
})

const fetchUsers = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      per_page: pageSize.value,
      ...filters.value
    }

    const response = await getUserList(params)
    users.value = response.data || []
    total.value = response.total || 0
  } catch (error) {
    console.error('获取用户列表失败:', error)
    let errorMessage = '获取用户列表失败'

    if (error.response) {
      errorMessage += '：' + (error.response.data?.message || `HTTP ${error.response.status}`)
    } else if (error.message) {
      errorMessage += '：' + error.message
    } else {
      errorMessage += '：网络连接失败，请检查网络或稍后重试'
    }

    ElMessage.error(errorMessage)
  } finally {
    loading.value = false
  }
}

const handleChangePassword = (row) => {
  passwordForm.value = {
    userId: row.id,
    phone: row.phone,
    newPassword: '',
    confirmPassword: ''
  }
  passwordDialogVisible.value = true
}

const handleClosePasswordDialog = () => {
  passwordDialogVisible.value = false
  passwordForm.value = {
    userId: null,
    phone: '',
    newPassword: '',
    confirmPassword: ''
  }
  if (passwordFormRef.value) {
    passwordFormRef.value.resetFields()
  }
}

const handleConfirmChangePassword = async () => {
  try {
    const valid = await passwordFormRef.value.validate()
    if (!valid) return

    passwordLoading.value = true
    await changeUserPassword(passwordForm.value.userId, passwordForm.value.newPassword)
    ElMessage.success('密码修改成功')
    handleClosePasswordDialog()
  } catch (error) {
    console.error('密码修改失败:', error)
    let errorMessage = '密码修改失败'

    if (error.response) {
      // 服务器返回了错误响应
      errorMessage += '：' + (error.response.data?.message || `HTTP ${error.response.status}`)
    } else if (error.message) {
      // 网络错误或其他错误
      errorMessage += '：' + error.message
    } else {
      // 未知错误
      errorMessage += '：未知错误，请稍后重试'
    }

    ElMessage.error(errorMessage)
  } finally {
    passwordLoading.value = false
  }
}

const handlePageChange = (page) => {
  currentPage.value = page
  fetchUsers()
}

const handleFilterChange = () => {
  currentPage.value = 1
  fetchUsers()
}

// 获取角色标签类型
const getRoleTagType = (role) => {
  const typeMap = {
    'super_admin': 'danger',
    'agency_admin': 'warning',
    'broker': 'info'
  }
  return typeMap[role] || 'info'
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  const typeMap = {
    'approved': 'success',
    'pending': 'warning',
    'rejected': 'danger'
  }
  return typeMap[status] || 'info'
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<style scoped>
/* 账号管理特定样式 */
.el-table {
  width: 100% !important;
}

.el-table__body-wrapper {
  overflow-x: auto;
}

/* 确保容器占满宽度 */
.bg-white {
  width: 100%;
  box-sizing: border-box;
}

/* 筛选条件样式 */
.filter-container {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  align-items: center;
}

.filter-select {
  width: 150px;
}

.filter-input {
  width: 250px;
}

/* 对话框样式优化 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
