<template>
  <div class="agencies-container bg-white p-5 rounded-lg shadow">
    <!-- 标题区域 -->
    <div class="flex justify-between items-center mb-5">
      <h2 class="text-2xl font-bold">中介机构管理</h2>
    </div>

    <!-- 搜索和过滤区域 -->
    <div class="mb-5 bg-gray-50 p-4 rounded-lg">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <el-input
          v-model="searchQuery"
          placeholder="搜索名称/机构代码/地址/法人姓名"
          clearable
          @clear="handleSearch"
          @keyup.enter="handleSearch"
        >
          <template #append>
            <el-button @click="handleSearch">
              <el-icon><Search /></el-icon>
            </el-button>
          </template>
        </el-input>

        <el-select v-model="statusFilter" placeholder="审核状态" clearable @change="handleSearch">
          <el-option label="全部" value=""></el-option>
          <el-option label="已通过" value="已通过"></el-option>
          <el-option label="已驳回" value="已驳回"></el-option>
        </el-select>
      </div>

      <div class="flex flex-wrap gap-2">
        <el-tag
          v-if="searchQuery"
          closable
          @close="
            () => {
              searchQuery = ''
              handleSearch()
            }
          "
        >
          关键词: {{ searchQuery }}
        </el-tag>
        <el-tag
          v-if="statusFilter"
          closable
          @close="
            () => {
              statusFilter = ''
              handleSearch()
            }
          "
        >
          状态: {{ statusFilter }}
        </el-tag>
      </div>
    </div>

    <!-- 表格区域 -->
    <el-table
      :data="filteredAgencies"
      border
      style="width: 100%"
      v-loading="loading"
      empty-text="暂无数据"
      :header-cell-style="{ background: '#f5f7fa' }"
    >
      <el-table-column prop="id" label="ID" width="60" fixed align="center"></el-table-column>
      <el-table-column
        prop="name"
        label="企业名称"
        min-width="180"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="org_code"
        label="统一社会信用代码"
        min-width="180"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="address"
        label="注册地址"
        min-width="200"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="legal_name"
        label="法人姓名"
        min-width="100"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="contact"
        label="联系电话"
        min-width="120"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="license_number"
        label="备案证明号"
        min-width="140"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column label="组织机构代码证" width="120" align="center">
        <template #default="{ row }">
          <el-image
            v-if="row.org_code_image"
            :src="row.org_code_image"
            :preview-src-list="[row.org_code_image]"
            fit="cover"
            style="width: 80px; height: 60px"
            :preview-teleported="true"
            :append-to-body="true"
            :z-index="9999"
            class="cursor-pointer rounded border border-gray-200"
          >
            <template #error>
              <div class="image-error">无图片</div>
            </template>
          </el-image>
          <span v-else class="text-gray-400">无图片</span>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="审核状态" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)" effect="light" round>{{ row.status }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150" fixed="right" align="center">
        <template #default="scope">
          <el-button size="small" @click="handleView(scope.row)" type="info" plain>查看</el-button>
          <el-button size="small" @click="handleEdit(scope.row)" type="primary" plain
            >审核</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <div class="flex justify-center mt-5">
      <el-pagination
        v-model:current-page="currentPage"
        :page-size="pageSize"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100]"
        @current-change="handlePageChange"
        @size-change="handleSizeChange"
        background
      />
    </div>

    <!-- 审核对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="审核中介机构"
      width="50%"
      append-to-body
      :z-index="2000"
      destroy-on-close
    >
      <el-form :model="form" :rules="rules" ref="formRef" label-width="160px">
        <el-form-item label="企业名称" prop="name">
          <el-input v-model="form.name" disabled></el-input>
        </el-form-item>
        <el-form-item label="统一社会信用代码" prop="org_code">
          <el-input v-model="form.org_code" disabled></el-input>
        </el-form-item>
        <el-form-item label="注册地址" prop="address">
          <el-input v-model="form.address" disabled></el-input>
        </el-form-item>
        <el-form-item label="法人姓名" prop="legal_name">
          <el-input v-model="form.legal_name" disabled></el-input>
        </el-form-item>
        <el-form-item label="联系电话" prop="contact">
          <el-input v-model="form.contact" disabled></el-input>
        </el-form-item>
        <el-form-item label="备案证明号" prop="license_number">
          <el-input v-model="form.license_number" disabled></el-input>
        </el-form-item>
        <el-form-item label="组织机构代码证">
          <el-image
            v-if="form.org_code_image"
            :src="form.org_code_image"
            :preview-src-list="[form.org_code_image]"
            style="width: 200px; height: 150px"
            fit="contain"
            :preview-teleported="true"
            :append-to-body="true"
            :z-index="9999"
            class="border border-gray-200 rounded"
          >
            <template #error>
              <div class="image-error">无法加载图片</div>
            </template>
          </el-image>
          <span v-else class="text-gray-400">未上传图片</span>
        </el-form-item>
        <el-form-item label="审核状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择审核状态">
            <el-option label="已通过" value="已通过"></el-option>
            <el-option label="已驳回" value="已驳回"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="驳回原因"
          v-if="form.status === '已驳回' || form.reject_reason"
          prop="reject_reason"
        >
          <el-input
            type="textarea"
            v-model="form.reject_reason"
            placeholder="请输入驳回原因"
            rows="3"
            :disabled="form.status !== '已驳回'"
          ></el-input>
          <div
            v-if="form.status !== '已驳回' && form.reject_reason"
            class="text-gray-500 text-sm mt-1"
          >
            <span class="text-orange-500">注意：</span
            >此机构曾被驳回，上次驳回原因如上所示。选择"已通过"将清除此驳回原因。
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex justify-end gap-2">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="loading">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import service from '@/utils/request'

const router = useRouter()
const agencies = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const dialogVisible = ref(false)
const loading = ref(false)
const searchQuery = ref('')
const statusFilter = ref('')

// 添加页面大小变化处理函数
const handleSizeChange = (size) => {
  pageSize.value = size
  fetchAgencies()
}

const form = ref({
  id: null,
  name: '',
  org_code: '',
  org_code_image: '',
  address: '',
  legal_name: '',
  contact: '',
  license_number: '',
  status: '',
  reject_reason: '',
})
const formRef = ref(null)
const rules = {
  status: [{ required: false, message: '请选择审核状态', trigger: 'change' }],
  reject_reason: [
    {
      required: true,
      message: '请输入驳回原因',
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (form.value.status === '已驳回' && (!value || value.trim() === '')) {
          callback(new Error('请输入驳回原因'))
        } else {
          callback()
        }
      },
    },
  ],
}

// 根据过滤条件筛选机构
const filteredAgencies = computed(() => {
  return agencies.value
})

// 获取状态标签类型
const getStatusType = (status) => {
  switch (status) {
    case '已通过':
    case 'approved':
      return 'success'
    case '待审核':
    case 'pending':
      return 'warning'
    case '已驳回':
    case 'rejected':
      return 'danger'
    default:
      return 'info'
  }
}

// 格式化状态显示
const formatStatus = (status) => {
  switch (status) {
    case 'approved':
      return '已通过'
    case 'pending':
      return '待审核'
    case 'rejected':
      return '已驳回'
    default:
      return status
  }
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  fetchAgencies()
}

onMounted(() => {
  fetchAgencies()
})

const fetchAgencies = async () => {
  loading.value = true

  try {
    // 构建API请求参数
    const params = {
      page: currentPage.value,
      per_page: pageSize.value,
    }

    // 添加搜索条件
    if (searchQuery.value) {
      params.search = searchQuery.value
    }

    // 添加状态过滤
    if (statusFilter.value) {
      // 将中文状态转换为英文状态
      const statusMap = {
        待审核: 'pending',
        已通过: 'approved',
        已驳回: 'rejected',
      }
      params.status = statusMap[statusFilter.value] || statusFilter.value
    }

    // 发起API请求
    const res = await service.get('/admin/agencies', { params })

    // 处理返回数据
    if (res.data) {
      agencies.value = res.data.map((item) => ({
        ...item,
        status: formatStatus(item.status), // 将英文状态转换为中文显示
      }))
      total.value = res.total
      currentPage.value = res.current_page
    } else {
      agencies.value = []
      total.value = 0
    }
  } catch (error) {
    ElMessage.error('获取机构列表失败：' + (error.message || '未知错误'))
    agencies.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

const handleEdit = (row) => {
  form.value = {
    ...row,
    reject_reason: row.reject_reason || '',
  }
  // 确保表单中的状态与原机构状态一致
  if (row.status === '待审核') {
    // 由于已经移除了"待审核"选项，如果原状态是待审核，则默认不选择任何状态
    form.value.status = ''
  } else {
    // 其他情况保持原状态
    form.value.status = row.status
  }
  dialogVisible.value = true
}

const handleSubmit = async () => {
  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    // 如果没有选择状态，提示用户
    if (!form.value.status) {
      ElMessage.warning('请选择审核状态')
      return
    }

    loading.value = true

    // 构建请求数据
    const requestData = {
      status:
        form.value.status === '已通过'
          ? 'approved'
          : form.value.status === '待审核'
            ? 'pending'
            : 'rejected',
    }

    // 如果是驳回状态，添加驳回原因
    if (requestData.status === 'rejected') {
      requestData.reject_reason = form.value.reject_reason
    }

    // 发送审核请求
    await service.put(`/admin/agencies/${form.value.id}/review`, requestData)

    ElMessage.success('审核状态更新成功')
    dialogVisible.value = false

    // 刷新列表
    fetchAgencies()
  } catch (error) {
    ElMessage.error('审核失败：' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

const handleView = (row) => {
  router.push(`/admin/agencies/${row.id}`)
}

const handlePageChange = (page) => {
  currentPage.value = page
  fetchAgencies()
}
</script>

<style scoped>
.image-error {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: #f5f7fa;
  color: #909399;
  font-size: 14px;
}

.agencies-container {
  width: 100%;
}

:deep(.el-image-viewer__wrapper) {
  z-index: 9999 !important;
}

:deep(.el-image-viewer__mask) {
  z-index: 9998 !important;
}

:deep(.el-table) {
  width: 100% !important;
}

:deep(.el-table__body) {
  width: 100% !important;
}

:deep(.el-table__header) {
  width: 100% !important;
}

:deep(.el-table .cell) {
  padding: 8px;
}
</style>
