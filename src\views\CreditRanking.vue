<template>
  <div class="credit-ranking">

    <!-- 头部 - 固定在顶部 -->
    <header class="fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 shadow-md">
      <!-- 返回首页按钮 -->
      <div class="absolute top-6 right-6">
        <button @click="goHome"
          class="px-5 py-2.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center space-x-2 shadow-sm">
          <i class="fa fa-home text-sm"></i>
          <span class="text-sm font-medium">返回首页</span>
        </button>
      </div>

      <div class="container mx-auto px-6 py-8">
        <div class="text-center">
          <h1 class="text-4xl font-bold text-gray-800 flex items-center justify-center mb-3">
            <i class="fa fa-balance-scale mr-3 text-blue-600 text-3xl"></i>信用档案红黑榜
          </h1>
          <p class="text-gray-600 text-lg font-medium tracking-wide">
            公开透明 · 奖惩分明 · 共建诚信社会
          </p>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <div class="bg-gray-50 pt-32 pb-8">
      <div class="container mx-auto px-6">

        <!-- 导航标签 -->
        <div class="flex flex-wrap justify-center gap-2 mb-8">
          <button @click="setActiveTab('statistics')" :class="['px-6 py-3 rounded-lg font-medium transition-all duration-300',
                     activeTab === 'statistics' ? 'bg-blue-600 text-white shadow-lg' : 'bg-white text-gray-700 hover:bg-gray-100']">
            <i class="fa fa-chart-line mr-2"></i>统计概览
          </button>
          <button @click="setActiveTab('red-agency')" :class="['px-6 py-3 rounded-lg font-medium transition-all duration-300',
                     activeTab === 'red-agency' ? 'bg-red-500 text-white shadow-lg' : 'bg-white text-gray-700 hover:bg-gray-100']">
            <i class="fa fa-building mr-2"></i>企业红榜
          </button>
          <button @click="setActiveTab('black-agency')" :class="['px-6 py-3 rounded-lg font-medium transition-all duration-300',
                     activeTab === 'black-agency' ? 'bg-gray-800 text-white shadow-lg' : 'bg-white text-gray-700 hover:bg-gray-100']">
            <i class="fa fa-building mr-2"></i>企业黑榜
          </button>
          <button @click="setActiveTab('red-broker')" :class="['px-6 py-3 rounded-lg font-medium transition-all duration-300',
                     activeTab === 'red-broker' ? 'bg-red-500 text-white shadow-lg' : 'bg-white text-gray-700 hover:bg-gray-100']">
            <i class="fa fa-user mr-2"></i>个人红榜
          </button>
          <button @click="setActiveTab('black-broker')" :class="['px-6 py-3 rounded-lg font-medium transition-all duration-300',
                     activeTab === 'black-broker' ? 'bg-gray-800 text-white shadow-lg' : 'bg-white text-gray-700 hover:bg-gray-100']">
            <i class="fa fa-user mr-2"></i>个人黑榜
          </button>
        </div>

        <!-- 统计概览 -->
        <section v-if="activeTab === 'statistics'" class="space-y-6">
          <!-- 统计卡片 -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div class="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-red-100 text-red-600">
                  <i class="fa fa-building text-xl"></i>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600">企业红榜</p>
                  <p class="text-2xl font-bold text-gray-900">{{ statistics.totals?.agency_red_total || 0 }}</p>
                </div>
              </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-gray-100 text-gray-600">
                  <i class="fa fa-building text-xl"></i>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600">企业黑榜</p>
                  <p class="text-2xl font-bold text-gray-900">{{ statistics.totals?.agency_black_total || 0 }}</p>
                </div>
              </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-red-100 text-red-600">
                  <i class="fa fa-user text-xl"></i>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600">个人红榜</p>
                  <p class="text-2xl font-bold text-gray-900">{{ statistics.totals?.broker_red_total || 0 }}</p>
                </div>
              </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-gray-100 text-gray-600">
                  <i class="fa fa-user text-xl"></i>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600">个人黑榜</p>
                  <p class="text-2xl font-bold text-gray-900">{{ statistics.totals?.broker_black_total || 0 }}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- 趋势图表 -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
            <div class="bg-white rounded-xl shadow-lg overflow-hidden">
              <div class="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-4">
                <h3 class="text-lg font-bold flex items-center">
                  <i class="fa fa-building mr-2"></i>企业信用档案趋势
                </h3>
              </div>
              <div class="p-4">
                <div ref="agencyTrendChart" class="h-80"></div>
              </div>
            </div>

            <div class="bg-white rounded-xl shadow-lg overflow-hidden">
              <div class="bg-gradient-to-r from-green-500 to-green-600 text-white p-4">
                <h3 class="text-lg font-bold flex items-center">
                  <i class="fa fa-user mr-2"></i>个人信用档案趋势
                </h3>
              </div>
              <div class="p-4">
                <div ref="brokerTrendChart" class="h-80"></div>
              </div>
            </div>
          </div>
        </section>

        <!-- 企业红榜 -->
        <section v-if="activeTab === 'red-agency'" class="bg-white rounded-xl shadow-lg overflow-hidden">
          <div class="bg-gradient-to-r from-red-500 to-red-600 text-white p-4">
            <h3 class="text-lg font-bold flex items-center">
              <i class="fa fa-building mr-2"></i>企业红榜
            </h3>
          </div>
          <div class="p-4">
            <div v-if="loading" class="flex items-center justify-center py-8">
              <i class="fa fa-spinner fa-spin mr-2"></i>
              <span>加载中...</span>
            </div>
            <div v-else-if="redArchives.length === 0" class="text-center py-8 text-gray-500">
              <i class="fa fa-inbox text-4xl mb-2"></i>
              <p>暂无数据</p>
            </div>
            <div v-else class="space-y-3 max-h-[400px] overflow-y-auto pr-1">
              <div class="space-y-2">
                <div v-for="archive in redArchives" :key="archive.id" @click="showDetail(archive)"
                  class="p-3 border rounded-lg flex flex-col md:flex-row md:items-center justify-between hover:bg-gray-50 hover:border-red-300 cursor-pointer transition-all duration-200 group">
                  <div class="mb-2 md:mb-0">
                    <div class="font-medium text-gray-900">{{ archive.entity_name }}</div>
                    <div class="text-sm text-gray-600">{{ archive.title }}</div>
                  </div>
                  <div class="flex items-center space-x-4">
                    <span class="text-sm text-gray-500">{{ formatDate(archive.created_at) }}</span>
                    <i class="fa fa-angle-right text-gray-400 transition-transform group-hover:translate-x-1"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="mt-5 text-center">
              <button
                class="px-6 py-2 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-lg hover:from-red-600 hover:to-red-700 transition-all font-medium">
                <i class="fa fa-list-ul mr-1"></i>更多信息
              </button>
            </div>
          </div>
        </section>

        <!-- 企业黑榜 -->
        <section v-if="activeTab === 'black-agency'" class="bg-white rounded-xl shadow-lg overflow-hidden">
          <div class="bg-gradient-to-r from-gray-700 to-gray-800 text-white p-4">
            <h3 class="text-lg font-bold flex items-center">
              <i class="fa fa-building mr-2"></i>企业黑榜
            </h3>
          </div>
          <div class="p-4">
            <div v-if="loading" class="flex items-center justify-center py-8">
              <i class="fa fa-spinner fa-spin mr-2"></i>
              <span>加载中...</span>
            </div>
            <div v-else-if="blackArchives.length === 0" class="text-center py-8 text-gray-500">
              <i class="fa fa-inbox text-4xl mb-2"></i>
              <p>暂无数据</p>
            </div>
            <div v-else class="space-y-3 max-h-[400px] overflow-y-auto pr-1">
              <div class="space-y-2">
                <div v-for="archive in blackArchives" :key="archive.id" @click="showDetail(archive)"
                  class="p-3 border rounded-lg flex flex-col md:flex-row md:items-center justify-between hover:bg-gray-50 hover:border-gray-400 cursor-pointer transition-all duration-200 group">
                  <div class="mb-2 md:mb-0">
                    <div class="font-medium text-gray-900">{{ archive.entity_name }}</div>
                    <div class="text-sm text-gray-600">{{ archive.title }}</div>
                  </div>
                  <div class="flex items-center space-x-4">
                    <span class="text-sm text-gray-500">{{ formatDate(archive.created_at) }}</span>
                    <i class="fa fa-angle-right text-gray-400 transition-transform group-hover:translate-x-1"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="mt-5 text-center">
              <button
                class="px-6 py-2 bg-gradient-to-r from-gray-700 to-gray-800 text-white rounded-lg hover:from-gray-800 hover:to-gray-900 transition-all font-medium">
                <i class="fa fa-list-ul mr-1"></i>更多信息
              </button>
            </div>
          </div>
        </section>

        <!-- 个人红榜 -->
        <section v-if="activeTab === 'red-broker'" class="bg-white rounded-xl shadow-lg overflow-hidden">
          <div class="bg-gradient-to-r from-green-500 to-green-600 text-white p-4">
            <h3 class="text-lg font-bold flex items-center">
              <i class="fa fa-user mr-2"></i>个人红榜
            </h3>
          </div>
          <div class="p-4">
            <div v-if="loading" class="flex items-center justify-center py-8">
              <i class="fa fa-spinner fa-spin mr-2"></i>
              <span>加载中...</span>
            </div>
            <div v-else-if="redArchives.length === 0" class="text-center py-8 text-gray-500">
              <i class="fa fa-inbox text-4xl mb-2"></i>
              <p>暂无数据</p>
            </div>
            <div v-else class="space-y-3 max-h-[400px] overflow-y-auto pr-1">
              <div class="space-y-2">
                <div v-for="archive in redArchives" :key="archive.id" @click="showDetail(archive)"
                  class="p-3 border rounded-lg flex flex-col md:flex-row md:items-center justify-between hover:bg-gray-50 hover:border-green-300 cursor-pointer transition-all duration-200 group">
                  <div class="mb-2 md:mb-0">
                    <div class="font-medium text-gray-900">{{ archive.entity_name }}</div>
                    <div class="text-sm text-gray-600">{{ archive.title }}</div>
                  </div>
                  <div class="flex items-center space-x-4">
                    <span class="text-sm text-gray-500">{{ formatDate(archive.created_at) }}</span>
                    <i class="fa fa-angle-right text-gray-400 transition-transform group-hover:translate-x-1"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="mt-5 text-center">
              <button
                class="px-6 py-2 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg hover:from-green-600 hover:to-green-700 transition-all font-medium">
                <i class="fa fa-list-ul mr-1"></i>更多信息
              </button>
            </div>
          </div>
        </section>

        <!-- 个人黑榜 -->
        <section v-if="activeTab === 'black-broker'" class="bg-white rounded-xl shadow-lg overflow-hidden">
          <div class="bg-gradient-to-r from-gray-700 to-gray-800 text-white p-4">
            <h3 class="text-lg font-bold flex items-center">
              <i class="fa fa-user mr-2"></i>个人黑榜
            </h3>
          </div>
          <div class="p-4">
            <div v-if="loading" class="flex items-center justify-center py-8">
              <i class="fa fa-spinner fa-spin mr-2"></i>
              <span>加载中...</span>
            </div>
            <div v-else-if="blackArchives.length === 0" class="text-center py-8 text-gray-500">
              <i class="fa fa-inbox text-4xl mb-2"></i>
              <p>暂无数据</p>
            </div>
            <div v-else class="space-y-3 max-h-[400px] overflow-y-auto pr-1">
              <div class="space-y-2">
                <div v-for="archive in blackArchives" :key="archive.id" @click="showDetail(archive)"
                  class="p-3 border rounded-lg flex flex-col md:flex-row md:items-center justify-between hover:bg-gray-50 hover:border-gray-400 cursor-pointer transition-all duration-200 group">
                  <div class="mb-2 md:mb-0">
                    <div class="font-medium text-gray-900">{{ archive.entity_name }}</div>
                    <div class="text-sm text-gray-600">{{ archive.title }}</div>
                  </div>
                  <div class="flex items-center space-x-4">
                    <span class="text-sm text-gray-500">{{ formatDate(archive.created_at) }}</span>
                    <i class="fa fa-angle-right text-gray-400 transition-transform group-hover:translate-x-1"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="mt-5 text-center">
              <button
                class="px-6 py-2 bg-gradient-to-r from-gray-700 to-gray-800 text-white rounded-lg hover:from-gray-800 hover:to-gray-900 transition-all font-medium">
                <i class="fa fa-list-ul mr-1"></i>更多信息
              </button>
            </div>
          </div>
        </section>

        <!-- 详情弹窗 -->
        <el-dialog v-model="detailVisible" :title="detailData.title" width="900px" class="detail-dialog" :close-on-click-modal="false"
          :close-on-press-escape="false" :show-close="false">
          <div class="detail-dialog-container">
            <!-- 关闭按钮 -->
            <button @click="closeDetail" class="dialog-close-btn">
              <i class="fa fa-times"></i>
            </button>

            <!-- 头部信息卡片 - 固定 -->
            <div class="detail-header-card">
              <div class="header-background" :class="detailData.type === 'red' ? 'red-bg' : 'black-bg'">
                <div class="header-content">
                  <div class="entity-badge">
                    <i class="fa fa-building" v-if="detailData.entity_type === 'agency'"></i>
                    <i class="fa fa-user" v-else></i>
                    <span>{{ getEntityTypeLabel(detailData.entity_type) }}</span>
                  </div>
                  <h3 class="entity-name">{{ detailData.entity_name || `${getEntityTypeLabel(detailData.entity_type)}${detailData.entity_id}` }}</h3>
                  <div class="entity-meta">
                    <div class="status-badge" :class="detailData.type === 'red' ? 'red-badge' : 'black-badge'">
                      <i class="fa fa-award" v-if="detailData.type === 'red'"></i>
                      <i class="fa fa-exclamation-triangle" v-else></i>
                      <span>{{ detailData.type === 'red' ? '诚信红榜' : '失信黑榜' }}</span>
                    </div>
                    <div class="date-info">
                      <i class="fa fa-calendar-alt"></i>
                      <span>{{ formatDate(detailData.created_at) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 内容标题 - 固定 -->
            <div class="content-header-fixed">
              <i class="fa fa-file-alt"></i>
              <h4>详细说明</h4>
            </div>

            <!-- 详细内容 - 可滚动 -->
            <div class="detail-content-scrollable">
              <div class="content-wrapper">
                <div class="content" v-html="detailData.content"></div>
              </div>
            </div>
          </div>
        </el-dialog>

      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, nextTick, onBeforeUnmount, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import * as echarts from 'echarts'
import {
  getCreditArchivesList,
  getCreditArchiveDetail,
  getCreditArchiveStatistics,
  ENTITY_TYPE_LABELS
} from '@/api/cms/creditArchives'

// 路由
const router = useRouter()

// 响应式数据
const loading = ref(false)
const activeTab = ref('statistics')
const detailVisible = ref(false)
const detailData = ref({})
const statistics = ref({})
const redArchives = ref([])
const blackArchives = ref([])
const redPage = ref(1)
const blackPage = ref(1)

// ECharts实例
const agencyTrendChart = ref(null)
const brokerTrendChart = ref(null)
let agencyChartInstance = null
let brokerChartInstance = null

// 计算属性
const currentType = computed(() => {
  if (activeTab.value === 'statistics') return null
  return activeTab.value.includes('red') ? 'red' : 'black'
})

const currentEntityType = computed(() => {
  if (activeTab.value === 'statistics') return null
  return activeTab.value.includes('agency') ? 'agency' : 'broker'
})

// 方法
const goHome = () => {
  router.push('/')
}

const setActiveTab = (tab) => {
  activeTab.value = tab
  if (tab === 'statistics') {
    loadStatistics()
  } else {
    loadArchives()
  }
}

const getEntityTypeLabel = (type) => {
  return ENTITY_TYPE_LABELS[type] || type
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

// 加载档案列表
const loadArchives = async () => {
  if (!currentType.value || !currentEntityType.value) return

  try {
    loading.value = true
    const params = {
      type: currentType.value,
      entity_type: currentEntityType.value,
      per_page: 10
    }

    const response = await getCreditArchivesList(params)

    // 根据当前标签页更新对应的数据
    if (activeTab.value === 'red-agency') {
      redArchives.value = response.data || []
    } else if (activeTab.value === 'black-agency') {
      blackArchives.value = response.data || []
    } else if (activeTab.value === 'red-broker') {
      redArchives.value = response.data || []
    } else if (activeTab.value === 'black-broker') {
      blackArchives.value = response.data || []
    }
  } catch (error) {
    console.error('加载档案列表失败:', error)
    ElMessage.error('加载档案列表失败')
  } finally {
    loading.value = false
  }
}

// 加载更多红榜
const loadMoreRed = async () => {
  try {
    loading.value = true
    redPage.value++
    const params = {
      type: 'red',
      entity_type: currentEntityType.value,
      page: redPage.value,
      per_page: 10
    }

    const response = await getCreditArchivesList(params)
    redArchives.value.push(...(response.data || []))
  } catch (error) {
    console.error('加载更多红榜失败:', error)
    ElMessage.error('加载更多失败')
  } finally {
    loading.value = false
  }
}

// 加载更多黑榜
const loadMoreBlack = async () => {
  try {
    loading.value = true
    blackPage.value++
    const params = {
      type: 'black',
      entity_type: currentEntityType.value,
      page: blackPage.value,
      per_page: 10
    }

    const response = await getCreditArchivesList(params)
    blackArchives.value.push(...(response.data || []))
  } catch (error) {
    console.error('加载更多黑榜失败:', error)
    ElMessage.error('加载更多失败')
  } finally {
    loading.value = false
  }
}

// 显示详情
const showDetail = async (item) => {
  try {
    const response = await getCreditArchiveDetail(item.id)
    detailData.value = response
    detailVisible.value = true
    // 阻止背景滚动
    document.body.style.overflow = 'hidden'
  } catch (error) {
    console.error('获取详情失败:', error)
    ElMessage.error('获取详情失败')
  }
}

// 关闭详情弹窗
const closeDetail = () => {
  detailVisible.value = false
  // 恢复背景滚动
  document.body.style.overflow = 'auto'
}

// 监听弹窗状态变化
watch(detailVisible, (newValue) => {
  if (!newValue) {
    // 弹窗关闭时恢复背景滚动
    document.body.style.overflow = 'auto'
  }
})

// 加载统计数据
const loadStatistics = async () => {
  try {
    const response = await getCreditArchiveStatistics()
    statistics.value = response

    // 渲染图表
    nextTick(() => {
      renderCharts()
    })
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 渲染图表
const renderCharts = () => {
  renderAgencyTrendChart()
  renderBrokerTrendChart()
}

// 渲染企业趋势图表
const renderAgencyTrendChart = () => {
  if (!agencyTrendChart.value) return

  // 销毁已存在的实例
  if (agencyChartInstance) {
    agencyChartInstance.dispose()
  }

  agencyChartInstance = echarts.init(agencyTrendChart.value)

  // 处理数据
  const redTrend = statistics.value.red_trend?.agency || []
  const blackTrend = statistics.value.black_trend?.agency || []

  // 获取所有月份
  const months = [...new Set([
    ...redTrend.map(item => item.year_month),
    ...blackTrend.map(item => item.year_month)
  ])].sort()

  // 构建数据
  const redData = months.map(month => {
    const item = redTrend.find(d => d.year_month === month)
    return item ? item.count : 0
  })

  const blackData = months.map(month => {
    const item = blackTrend.find(d => d.year_month === month)
    return item ? item.count : 0
  })

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e1e8ed',
      borderWidth: 1,
      textStyle: {
        color: '#333',
        fontSize: 12
      },
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      },
      formatter: function (params) {
        let result = `<div style="font-weight: bold; margin-bottom: 5px;">${params[0].axisValue}</div>`
        params.forEach(param => {
          const color = param.color
          result += `<div style="display: flex; align-items: center; margin: 2px 0;">
            <span style="display: inline-block; width: 10px; height: 10px; background: ${color}; border-radius: 50%; margin-right: 8px;"></span>
            <span style="flex: 1;">${param.seriesName}</span>
            <span style="font-weight: bold; color: ${color};">${param.value}</span>
          </div>`
        })
        return result
      }
    },
    legend: {
      data: ['企业红榜', '企业黑榜'],
      top: 10,
      right: 20,
      textStyle: {
        color: '#666',
        fontSize: 12
      },
      itemWidth: 12,
      itemHeight: 8,
      itemGap: 15
    },
    grid: {
      left: '8%',
      right: '8%',
      top: '15%',
      bottom: '12%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: months.map(month => {
        const [, monthNum] = month.split('-')
        return `${monthNum}月`
      }),
      axisLine: {
        lineStyle: {
          color: '#e1e8ed'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#666',
        fontSize: 11
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#f5f5f5',
          type: 'dashed'
        }
      }
    },
    yAxis: {
      type: 'value',
      minInterval: 1,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#666',
        fontSize: 11
      },
      splitLine: {
        lineStyle: {
          color: '#f5f5f5',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: '企业红榜',
        type: 'line',
        data: redData,
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#ff6b6b',
          borderWidth: 2,
          borderColor: '#fff'
        },
        lineStyle: {
          color: '#ff6b6b',
          width: 3
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: 'rgba(255, 107, 107, 0.3)'
            }, {
              offset: 1, color: 'rgba(255, 107, 107, 0.05)'
            }]
          }
        },
        emphasis: {
          itemStyle: {
            color: '#ff6b6b',
            borderColor: '#fff',
            borderWidth: 3,
            shadowBlur: 10,
            shadowColor: 'rgba(255, 107, 107, 0.5)'
          }
        }
      },
      {
        name: '企业黑榜',
        type: 'line',
        data: blackData,
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#4ecdc4',
          borderWidth: 2,
          borderColor: '#fff'
        },
        lineStyle: {
          color: '#4ecdc4',
          width: 3
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: 'rgba(78, 205, 196, 0.3)'
            }, {
              offset: 1, color: 'rgba(78, 205, 196, 0.05)'
            }]
          }
        },
        emphasis: {
          itemStyle: {
            color: '#4ecdc4',
            borderColor: '#fff',
            borderWidth: 3,
            shadowBlur: 10,
            shadowColor: 'rgba(78, 205, 196, 0.5)'
          }
        }
      }
    ]
  }

  agencyChartInstance.setOption(option)
}

// 渲染个人趋势图表
const renderBrokerTrendChart = () => {
  if (!brokerTrendChart.value) return

  // 销毁已存在的实例
  if (brokerChartInstance) {
    brokerChartInstance.dispose()
  }

  brokerChartInstance = echarts.init(brokerTrendChart.value)

  // 处理数据
  const redTrend = statistics.value.red_trend?.broker || []
  const blackTrend = statistics.value.black_trend?.broker || []

  // 获取所有月份
  const months = [...new Set([
    ...redTrend.map(item => item.year_month),
    ...blackTrend.map(item => item.year_month)
  ])].sort()

  // 构建数据
  const redData = months.map(month => {
    const item = redTrend.find(d => d.year_month === month)
    return item ? item.count : 0
  })

  const blackData = months.map(month => {
    const item = blackTrend.find(d => d.year_month === month)
    return item ? item.count : 0
  })

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e1e8ed',
      borderWidth: 1,
      textStyle: {
        color: '#333',
        fontSize: 12
      },
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      },
      formatter: function (params) {
        let result = `<div style="font-weight: bold; margin-bottom: 5px;">${params[0].axisValue}</div>`
        params.forEach(param => {
          const color = param.color
          result += `<div style="display: flex; align-items: center; margin: 2px 0;">
            <span style="display: inline-block; width: 10px; height: 10px; background: ${color}; border-radius: 50%; margin-right: 8px;"></span>
            <span style="flex: 1;">${param.seriesName}</span>
            <span style="font-weight: bold; color: ${color};">${param.value}</span>
          </div>`
        })
        return result
      }
    },
    legend: {
      data: ['个人红榜', '个人黑榜'],
      top: 10,
      right: 20,
      textStyle: {
        color: '#666',
        fontSize: 12
      },
      itemWidth: 12,
      itemHeight: 8,
      itemGap: 15
    },
    grid: {
      left: '8%',
      right: '8%',
      top: '15%',
      bottom: '12%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: months.map(month => {
        const [, monthNum] = month.split('-')
        return `${monthNum}月`
      }),
      axisLine: {
        lineStyle: {
          color: '#e1e8ed'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#666',
        fontSize: 11
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#f5f5f5',
          type: 'dashed'
        }
      }
    },
    yAxis: {
      type: 'value',
      minInterval: 1,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#666',
        fontSize: 11
      },
      splitLine: {
        lineStyle: {
          color: '#f5f5f5',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: '个人红榜',
        type: 'line',
        data: redData,
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#ff9f43',
          borderWidth: 2,
          borderColor: '#fff'
        },
        lineStyle: {
          color: '#ff9f43',
          width: 3
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: 'rgba(255, 159, 67, 0.3)'
            }, {
              offset: 1, color: 'rgba(255, 159, 67, 0.05)'
            }]
          }
        },
        emphasis: {
          itemStyle: {
            color: '#ff9f43',
            borderColor: '#fff',
            borderWidth: 3,
            shadowBlur: 10,
            shadowColor: 'rgba(255, 159, 67, 0.5)'
          }
        }
      },
      {
        name: '个人黑榜',
        type: 'line',
        data: blackData,
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#5f27cd',
          borderWidth: 2,
          borderColor: '#fff'
        },
        lineStyle: {
          color: '#5f27cd',
          width: 3
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: 'rgba(95, 39, 205, 0.3)'
            }, {
              offset: 1, color: 'rgba(95, 39, 205, 0.05)'
            }]
          }
        },
        emphasis: {
          itemStyle: {
            color: '#5f27cd',
            borderColor: '#fff',
            borderWidth: 3,
            shadowBlur: 10,
            shadowColor: 'rgba(95, 39, 205, 0.5)'
          }
        }
      }
    ]
  }

  brokerChartInstance.setOption(option)
}

// 生命周期
onMounted(() => {
  // 默认加载统计数据
  loadStatistics()
})

onBeforeUnmount(() => {
  // 销毁图表实例
  if (agencyChartInstance) {
    agencyChartInstance.dispose()
    agencyChartInstance = null
  }
  if (brokerChartInstance) {
    brokerChartInstance.dispose()
    brokerChartInstance = null
  }
  // 恢复背景滚动
  document.body.style.overflow = 'auto'
})
</script>

<style scoped>
.credit-ranking {
  position: relative;
}

/* 容器 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 10;
}

/* 头部 */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40px 0;
  text-align: center;
}

.header-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 10px;
}

.logo i {
  font-size: 48px;
}

.logo h1 {
  font-size: 36px;
  font-weight: bold;
  margin: 0;
}

.subtitle {
  font-size: 16px;
  opacity: 0.9;
}

/* 导航标签 */
.nav-tabs {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.tabs {
  display: flex;
  justify-content: center;
  gap: 0;
}

.tab-item {
  padding: 20px 30px;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #666;
}

.tab-item:hover {
  background: #f8f9fa;
  color: #333;
}

.tab-item.active {
  color: #409eff;
  border-bottom-color: #409eff;
  background: #f0f8ff;
}

/* 图表 */
.charts {
  padding: 40px 0;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 30px;
}

.chart-card {
  background: white;
  border-radius: 20px;
  padding: 25px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.chart-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px 20px 0 0;
}

.chart-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.chart-card h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
  text-align: center;
  position: relative;
  padding-bottom: 10px;
}

.chart-card h3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 2px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 1px;
}

.chart-container {
  height: 400px;
  width: 100%;
  border-radius: 12px;
  background: rgba(248, 249, 250, 0.5);
  padding: 10px;
}

/* 档案列表 */
.archives-section {
  padding: 40px 0;
}

.section-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 30px;
}

.list-section {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.section-header {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #f0f0f0;
}

.section-header h2 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 24px;
}

.list-section.red .section-header h2 {
  color: #e74c3c;
}

.list-section.black .section-header h2 {
  color: #34495e;
}

.count {
  font-size: 14px;
  font-weight: normal;
  color: #666;
}

.archive-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.archive-item {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
}

.list-section.red .archive-item {
  border-left-color: #e74c3c;
}

.list-section.black .archive-item {
  border-left-color: #34495e;
}

.archive-item:hover {
  background: #e8f4fd;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.archive-content h4 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 16px;
}

.archive-content p {
  margin: 0 0 10px 0;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.archive-date {
  font-size: 12px;
  color: #999;
}

.load-more {
  text-align: center;
}

/* 详情弹窗 */
:deep(.detail-dialog) {
  border-radius: 16px;
  overflow: hidden;
}

:deep(.detail-dialog .el-dialog__header) {
  display: none;
}

:deep(.detail-dialog .el-dialog__body) {
  padding: 0;
  height: 80vh;
  overflow: hidden;
}

:deep(.detail-dialog .el-dialog__footer) {
  display: none;
}

/* 弹窗容器 */
.detail-dialog-container {
  position: relative;
  height: 80vh;
  display: flex;
  flex-direction: column;
}

/* 右上角关闭按钮 */
.dialog-close-btn {
  position: absolute;
  top: 16px;
  right: 16px;
  z-index: 100;
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.dialog-close-btn:hover {
  background: rgba(0, 0, 0, 0.7);
  transform: scale(1.1);
}

/* 头部信息卡片 - 固定 */
.detail-header-card {
  flex-shrink: 0;
  margin: -20px -20px 0 -20px;
}

.header-background {
  position: relative;
  padding: 32px 32px 24px;
  color: white;
  overflow: hidden;
}

.header-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.1;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.red-bg {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  box-shadow: 0 8px 32px rgba(231, 76, 60, 0.3);
}

.black-bg {
  background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
  box-shadow: 0 8px 32px rgba(52, 73, 94, 0.3);
}

.header-content {
  position: relative;
  z-index: 1;
}

.entity-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 16px;
}

.entity-name {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 20px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.entity-meta {
  display: flex;
  align-items: center;
  gap: 24px;
  flex-wrap: wrap;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border-radius: 25px;
  font-weight: 600;
  font-size: 15px;
  backdrop-filter: blur(10px);
}

.red-badge {
  background: rgba(255, 255, 255, 0.25);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.black-badge {
  background: rgba(255, 255, 255, 0.15);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.date-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 15px;
  opacity: 0.9;
}

/* 内容标题 - 固定 */
.content-header-fixed {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 24px 32px 16px;
  background: #ffffff;
  border-bottom: 2px solid #f8f9fa;
}

.content-header-fixed i {
  color: #3498db;
  font-size: 20px;
}

.content-header-fixed h4 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
}

/* 详细内容 - 可滚动区域 */
.detail-content-scrollable {
  flex: 1;
  overflow-y: auto;
  background: #ffffff;
}

/* 自定义滚动条 */
.detail-content-scrollable::-webkit-scrollbar {
  width: 8px;
}

.detail-content-scrollable::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.detail-content-scrollable::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.detail-content-scrollable::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.content-wrapper {
  background: #f8f9fa;
  margin: 24px 32px 32px;
  border-radius: 12px;
  padding: 24px;
  border-left: 4px solid #3498db;
}

.content {
  line-height: 1.8;
  color: #555;
  font-size: 16px;
}

.content p {
  margin-bottom: 16px;
}

.content h1,
.content h2,
.content h3,
.content h4,
.content h5,
.content h6 {
  color: #2c3e50;
  margin-top: 24px;
  margin-bottom: 16px;
}

.content ul,
.content ol {
  padding-left: 24px;
  margin-bottom: 16px;
}

.content li {
  margin-bottom: 8px;
}

/* 底部 */
.footer {
  background: #2c3e50;
  color: white;
  padding: 40px 0 20px;
  margin-top: 60px;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
}

.footer-info h3 {
  margin: 0 0 10px 0;
  font-size: 20px;
}

.footer-info p {
  margin: 0;
  color: #bdc3c7;
  font-size: 14px;
}

.footer-links {
  display: flex;
  gap: 20px;
}

.footer-links a {
  color: #bdc3c7;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: white;
}

.copyright {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #34495e;
  color: #95a5a6;
  font-size: 12px;
}

/* 响应式 */
@media (max-width: 768px) {
  .container {
    padding: 0 15px;
  }

  .logo h1 {
    font-size: 24px;
  }

  .tabs {
    flex-wrap: wrap;
  }

  .tab-item {
    padding: 15px 20px;
    font-size: 14px;
  }

  .charts-grid {
    grid-template-columns: 1fr;
  }

  .archive-list {
    grid-template-columns: 1fr;
  }

  .footer-content {
    flex-direction: column;
    gap: 20px;
  }
}

/* 新增头部样式 */
.header {
  padding: 60px 0 !important;
  position: relative !important;
}

.header-actions {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 20;
}

.home-button {
  background: rgba(255, 255, 255, 0.2) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  color: white !important;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.home-button:hover {
  background: rgba(255, 255, 255, 0.3) !important;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.header-content {
  text-align: center !important;
}

.logo {
  flex-direction: column !important;
  margin-bottom: 20px !important;
}

.justice-scale {
  position: relative;
  margin-bottom: 15px;
}

.justice-scale i {
  color: #ffd700 !important;
  filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.5));
  animation: glow 3s ease-in-out infinite;
}

.scale-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 80px;
  background: radial-gradient(circle, rgba(255, 215, 0, 0.2) 0%, transparent 70%);
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

.logo h1 {
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3) !important;
}

.subtitle {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  gap: 15px !important;
  font-size: 18px !important;
}

.subtitle-text {
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.header-badges {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.badge {
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.badge-justice {
  background: rgba(255, 107, 107, 0.2);
  color: #fff;
}

.badge-transparent {
  background: rgba(78, 205, 196, 0.2);
  color: #fff;
}

.badge-authority {
  background: rgba(255, 159, 67, 0.2);
  color: #fff;
}

.badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}
</style>
