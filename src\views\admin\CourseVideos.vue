<template>
  <div class="bg-white p-6 rounded-lg shadow-md">
    <div class="header-section mb-4">
      <div class="flex items-center gap-4">
        <el-button @click="goBack" :icon="ArrowLeft">返回课程列表</el-button>
        <div>
          <h2 class="text-2xl font-bold">视频管理</h2>
          <p class="text-gray-600" v-if="courseInfo">课程：{{ courseInfo.title }}</p>
        </div>
      </div>
    </div>

    <!-- 操作栏 -->
    <div class="action-bar mb-4">
      <el-button type="primary" @click="handleAdd" :icon="Plus">添加视频</el-button>
      <el-input v-model="searchKeyword" placeholder="搜索视频标题" clearable @keyup.enter="handleSearch" @clear="handleSearch" class="search-input">
        <template #append>
          <el-button @click="handleSearch" :icon="Search"></el-button>
        </template>
      </el-input>
    </div>

    <el-table :data="videos" border style="width: 100%; table-layout: fixed;" v-loading="loading">
      <el-table-column prop="id" label="ID" width="80" fixed="left"></el-table-column>
      <el-table-column prop="order" label="排序" width="80" sortable></el-table-column>
      <el-table-column prop="title" label="视频标题" min-width="200" show-overflow-tooltip></el-table-column>
      <el-table-column prop="description" label="视频描述" min-width="200" show-overflow-tooltip></el-table-column>
      <el-table-column prop="duration" label="时长" width="100">
        <template #default="scope">
          {{ formatDuration(scope.row.duration) }}
        </template>
      </el-table-column>
      <el-table-column prop="created_at" label="创建时间" min-width="180">
        <template #default="scope">
          {{ formatDateTime(scope.row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button size="small" @click="handleView(scope.row)" :icon="View">查看</el-button>
          <el-button size="small" type="primary" @click="handleEdit(scope.row)" :icon="Edit">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDelete(scope.row)" :icon="Delete">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination v-model:current-page="currentPage" :page-size="pageSize" :total="total" layout="total, prev, pager, next" class="mt-4"
      @current-change="handlePageChange" />
    <!-- 添加/编辑视频对话框 -->
    <el-dialog v-model="dialogVisible" :title="form.id ? '编辑视频' : '添加视频'" width="70%" :before-close="handleCloseDialog">
      <!-- 视频预览区域（仅编辑时显示） -->
      <div v-if="form.id && form.video_url" class="video-preview-section mb-4">
        <h4 class="preview-title">当前视频预览</h4>
        <div class="video-container">
          <video :src="getFullVideoUrl(form.video_url)" controls class="preview-video" @error="handleVideoError">
            您的浏览器不支持视频播放
          </video>
        </div>
      </div>

      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="视频标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入视频标题" maxlength="100" show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="视频描述" prop="description">
          <el-input type="textarea" v-model="form.description" placeholder="请输入视频描述" :rows="3" maxlength="500" show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="视频来源" prop="videoSource">
          <el-radio-group v-model="form.videoSource" @change="handleVideoSourceChange">
            <el-radio value="url">视频URL</el-radio>
            <el-radio value="file">上传文件</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item v-if="form.videoSource === 'url'" label="视频URL" prop="video_url">
          <el-input v-model="form.video_url" placeholder="请输入视频URL"></el-input>
        </el-form-item>

        <el-form-item v-if="form.videoSource === 'file'" label="上传视频" prop="video_file">
          <el-upload ref="uploadRef" class="upload-demo" :action="uploadAction" :before-upload="beforeUpload" :on-change="handleFileChange"
            :on-remove="handleFileRemove" :file-list="fileList" :limit="1" :on-exceed="handleExceed" :auto-upload="false" accept="video/*">
            <el-button size="small" type="primary" :icon="Upload">选择视频文件</el-button>
            <template #tip>
              <div class="el-upload__tip">
                支持 MP4、AVI、MOV 等格式，文件大小不超过 500MB
              </div>
            </template>
          </el-upload>
          <div v-if="uploadProgress > 0 && uploadProgress < 100" class="upload-progress">
            <el-progress :percentage="uploadProgress" :show-text="true"></el-progress>
          </div>
        </el-form-item>
        <el-form-item v-if="form.videoSource === 'url'" label="时长（秒）" prop="duration">
          <el-input-number v-model="form.duration" placeholder="请输入视频时长" :min="0"></el-input-number>
          <div class="text-gray-500 text-sm mt-1">文件上传时会自动获取时长</div>
        </el-form-item>
        <el-form-item label="排序" prop="order">
          <el-input-number v-model="form.order" placeholder="请输入排序" :min="0"></el-input-number>
        </el-form-item>

      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Plus, Search, View, Edit, Delete, Upload } from '@element-plus/icons-vue'
import {
  getCourseVideoList,
  getVideoDetail,
  createCourseVideo,
  updateVideo,
  deleteVideo
} from '@/api/admin/videos'
import { getCourseDetail } from '@/api/admin/courses'

const router = useRouter()
const route = useRoute()
const courseId = ref(parseInt(route.params.id))

const videos = ref([])
const courseInfo = ref(null)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const loading = ref(false)
const submitLoading = ref(false)
const searchKeyword = ref('')

// 对话框相关
const dialogVisible = ref(false)
const form = ref({
  id: null,
  title: '',
  description: '',
  video_url: '',
  videoSource: 'url', // 'url' 或 'file'
  video_file: null,
  duration: 0,
  order: 1
})
const formRef = ref(null)
const uploadRef = ref(null)

// 上传相关
const fileList = ref([])
const uploadProgress = ref(0)
const uploadAction = '#' // 占位符，实际不使用

// 表单验证规则
const rules = {
  title: [{ required: true, message: '请输入视频标题', trigger: 'blur' }],
  videoSource: [{ required: true, message: '请选择视频来源', trigger: 'change' }],
  video_url: [
    {
      validator: (rule, value, callback) => {
        if (form.value.videoSource === 'url' && !value) {
          callback(new Error('请输入视频URL'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  video_file: [
    {
      validator: (rule, value, callback) => {
        if (form.value.videoSource === 'file') {
          // 检查本地fileList
          if (fileList.value && fileList.value.length > 0) {
            callback()
            return
          }

          // 检查上传组件的文件列表
          const uploadComponent = uploadRef.value
          if (uploadComponent && uploadComponent.uploadFiles && uploadComponent.uploadFiles.length > 0) {
            callback()
            return
          }

          callback(new Error('请选择视频文件'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  duration: [
    {
      validator: (rule, value, callback) => {
        if (form.value.videoSource === 'url' && !value) {
          callback(new Error('请输入视频时长'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  order: [{ required: true, message: '请输入排序序号', trigger: 'blur' }]
}

onMounted(() => {
  fetchCourseInfo()
  fetchVideos()
})

const fetchCourseInfo = async () => {
  try {
    const response = await getCourseDetail(courseId.value)
    courseInfo.value = response
  } catch (error) {
    console.error('获取课程信息失败:', error)
  }
}

const fetchVideos = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      per_page: pageSize.value
    }

    if (searchKeyword.value.trim()) {
      params.keyword = searchKeyword.value.trim()
    }

    const response = await getCourseVideoList(courseId.value, params)
    videos.value = response.data || []
    total.value = response.total || 0
  } catch (error) {
    console.error('获取视频列表失败:', error)
    let errorMessage = '获取视频列表失败'

    if (error.response) {
      errorMessage += '：' + (error.response.data?.message || `HTTP ${error.response.status}`)
    } else if (error.message) {
      errorMessage += '：' + error.message
    } else {
      errorMessage += '：网络连接失败，请检查网络或稍后重试'
    }

    ElMessage.error(errorMessage)
  } finally {
    loading.value = false
  }
}

const goBack = () => {
  router.push('/admin/courses')
}

const handleAdd = () => {
  form.value = {
    id: null,
    title: '',
    description: '',
    video_url: '',
    videoSource: 'url',
    video_file: null,
    duration: null,
    order: videos.value.length + 1
  }
  fileList.value = []
  uploadProgress.value = 0
  dialogVisible.value = true
}

const handleEdit = async (row) => {
  try {
    loading.value = true
    const response = await getVideoDetail(row.id)
    form.value = {
      ...response,
      videoSource: response.video_url ? 'url' : 'file',
      video_file: null
    }
    fileList.value = []
    uploadProgress.value = 0
    dialogVisible.value = true
  } catch (error) {
    ElMessage.error('获取视频详情失败：' + (error.response?.data?.message || error.message))
  } finally {
    loading.value = false
  }
}

const handleView = async (row) => {
  try {
    const response = await getVideoDetail(row.id)
    const fullVideoUrl = getFullVideoUrl(response.video_url)

    ElMessageBox.alert(
      `<div style="max-width: 600px;">
        <div style="margin-bottom: 16px;">
          <video
            controls
            style="width: 100%; max-height: 300px; border-radius: 8px; background: #000;"
            poster=""
          >
            <source src="${fullVideoUrl}" type="video/mp4">
            您的浏览器不支持视频播放
          </video>
        </div>
        <div style="background: #f5f7fa; padding: 16px; border-radius: 8px; line-height: 1.6;">
          <p style="margin: 8px 0;"><strong>视频标题：</strong>${response.title}</p>
          <p style="margin: 8px 0;"><strong>视频描述：</strong>${response.description || '暂无描述'}</p>
          <p style="margin: 8px 0;"><strong>视频URL：</strong><span style="word-break: break-all; color: #409eff;">${response.video_url}</span></p>
          <p style="margin: 8px 0;"><strong>完整地址：</strong><span style="word-break: break-all; color: #67c23a;">${fullVideoUrl}</span></p>
          <p style="margin: 8px 0;"><strong>视频时长：</strong>${formatDuration(response.duration)}</p>
          <p style="margin: 8px 0;"><strong>排序序号：</strong>${response.order}</p>
          <p style="margin: 8px 0;"><strong>创建时间：</strong>${formatDateTime(response.created_at)}</p>
          <p style="margin: 8px 0;"><strong>更新时间：</strong>${formatDateTime(response.updated_at)}</p>
        </div>
      </div>`,
      '视频详情预览',
      {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '确定',
        customStyle: {
          width: '700px'
        }
      }
    )
  } catch (error) {
    ElMessage.error('获取视频详情失败：' + (error.response?.data?.message || error.message))
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除视频"${row.title}"吗？此操作不可恢复！`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    loading.value = true
    await deleteVideo(row.id)
    ElMessage.success('删除成功')
    fetchVideos()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败：' + (error.response?.data?.message || error.message))
    }
  } finally {
    loading.value = false
  }
}

const handleSubmit = async () => {
  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    submitLoading.value = true

    let videoData

    if (form.value.videoSource === 'file') {
      // 使用FormData上传文件
      videoData = new FormData()
      videoData.append('title', form.value.title)
      videoData.append('description', form.value.description || '')
      videoData.append('order', form.value.order)

      // 文件上传时不发送duration，让后台自动获取
      // 添加文件
      if (fileList.value && fileList.value.length > 0) {
        videoData.append('video_file', fileList.value[0].raw)
      } else {
        // 备用方案：从上传组件获取
        const uploadComponent = uploadRef.value
        if (uploadComponent && uploadComponent.uploadFiles && uploadComponent.uploadFiles.length > 0) {
          videoData.append('video_file', uploadComponent.uploadFiles[0].raw)
        }
      }
    } else {
      // 使用JSON数据提交URL
      videoData = {
        title: form.value.title,
        description: form.value.description,
        video_url: form.value.video_url,
        duration: form.value.duration,
        order: form.value.order
      }
    }

    if (form.value.id) {
      // 编辑视频
      await updateVideo(form.value.id, videoData)
      ElMessage.success('视频更新成功')
    } else {
      // 创建视频
      await createCourseVideo(courseId.value, videoData)
      ElMessage.success('视频创建成功')
    }

    dialogVisible.value = false
    fetchVideos()
  } catch (error) {
    console.error('提交视频失败:', error)
    let errorMessage = form.value.id ? '更新视频失败' : '创建视频失败'

    if (error.response) {
      errorMessage += '：' + (error.response.data?.message || `HTTP ${error.response.status}`)
    } else if (error.message) {
      errorMessage += '：' + error.message
    } else {
      errorMessage += '：未知错误，请稍后重试'
    }

    ElMessage.error(errorMessage)
  } finally {
    submitLoading.value = false
  }
}

const handlePageChange = (page) => {
  currentPage.value = page
  fetchVideos()
}

const handleSearch = () => {
  currentPage.value = 1
  fetchVideos()
}

const handleCloseDialog = () => {
  dialogVisible.value = false
  form.value = {
    id: null,
    title: '',
    description: '',
    video_url: '',
    videoSource: 'url',
    video_file: null,
    duration: null,
    order: 1
  }
  fileList.value = []
  uploadProgress.value = 0
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 视频来源切换处理
const handleVideoSourceChange = (value) => {
  // 清空相关字段
  if (value === 'url') {
    form.value.video_file = null
    fileList.value = []
    uploadProgress.value = 0
  } else {
    form.value.video_url = ''
  }

  // 重新验证表单
  if (formRef.value) {
    formRef.value.clearValidate(['video_url', 'video_file'])
  }
}

// 上传前检查
const beforeUpload = (file) => {
  const isVideo = file.type.startsWith('video/')
  const isLt500M = file.size / 1024 / 1024 < 500

  if (!isVideo) {
    ElMessage.error('只能上传视频文件!')
    return false
  }
  if (!isLt500M) {
    ElMessage.error('视频文件大小不能超过 500MB!')
    return false
  }

  return false // 阻止自动上传，我们手动处理
}

// 文件变化处理
const handleFileChange = (file, fileListParam) => {
  // 更新本地fileList
  fileList.value = fileListParam

  // 触发表单验证
  if (formRef.value) {
    formRef.value.validateField('video_file')
  }
}

// 文件移除处理
const handleFileRemove = (file, fileList) => {
  console.log('文件移除:', file, fileList)
  // 触发表单验证
  if (formRef.value) {
    formRef.value.validateField('video_file')
  }
}





// 文件数量超出限制
const handleExceed = () => {
  ElMessage.warning('最多只能上传一个视频文件')
}

// 视频播放错误处理
const handleVideoError = (event) => {
  console.error('视频播放错误:', event)
  ElMessage.warning('视频加载失败，请检查视频文件是否存在')
}

// 格式化时长（秒转换为时分秒）
const formatDuration = (seconds) => {
  if (!seconds) return '0秒'

  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  if (hours > 0) {
    return `${hours}时${minutes}分${secs}秒`
  } else if (minutes > 0) {
    return `${minutes}分${secs}秒`
  } else {
    return `${secs}秒`
  }
}

// 获取完整的视频URL
const getFullVideoUrl = (videoUrl) => {
  if (!videoUrl) return ''

  // 如果已经是完整URL，直接返回
  if (videoUrl.startsWith('http://') || videoUrl.startsWith('https://')) {
    return videoUrl
  }

  // 如果是相对路径，添加当前域名
  const baseUrl = window.location.origin
  return videoUrl.startsWith('/') ? `${baseUrl}${videoUrl}` : `${baseUrl}/${videoUrl}`
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<style scoped>
/* 视频管理特定样式 */
.header-section {
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 16px;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.search-input {
  width: 300px;
}

.el-table {
  width: 100% !important;
}

.el-table__body-wrapper {
  overflow-x: auto;
}

/* 确保容器占满宽度 */
.bg-white {
  width: 100%;
  box-sizing: border-box;
}

/* 对话框样式优化 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 上传组件样式 */
.upload-demo {
  width: 100%;
}

.upload-progress {
  margin-top: 10px;
}

.el-upload__tip {
  color: #909399;
  font-size: 12px;
  line-height: 1.5;
}

/* 视频预览样式 */
.video-preview-section {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.preview-title {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
}

.preview-title::before {
  content: '🎬';
  margin-right: 8px;
}

.video-container {
  background: #000;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.preview-video {
  width: 100%;
  max-height: 300px;
  display: block;
  background: #000;
}

/* 对话框内容优化 */
.el-dialog__body {
  padding: 20px;
}

/* 表单项间距优化 */
.el-form-item {
  margin-bottom: 20px;
}

/* 视频详情预览样式优化 */
.video-detail-preview {
  max-width: 600px;
}

.video-detail-preview video {
  width: 100%;
  max-height: 300px;
  border-radius: 8px;
  background: #000;
  margin-bottom: 16px;
}

.video-info-panel {
  background: #f5f7fa;
  padding: 16px;
  border-radius: 8px;
  line-height: 1.6;
}

.video-info-panel p {
  margin: 8px 0;
}

.video-url {
  word-break: break-all;
  color: #409eff;
}

.video-full-url {
  word-break: break-all;
  color: #67c23a;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-bar {
    flex-direction: column;
    align-items: stretch;
  }

  .search-input {
    width: 100%;
  }
}
</style>
