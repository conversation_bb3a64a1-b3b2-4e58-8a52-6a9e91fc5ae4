<template>
  <div class="bg-white p-6 rounded-lg shadow-md">
    <h2 class="text-2xl font-bold mb-4">课程视频管理</h2>
    <el-button type="primary" class="mb-4" @click="handleAdd">添加视频</el-button>
    <el-table :data="videos" border style="width: 100%">
      <el-table-column prop="id" label="ID" width="80"></el-table-column>
      <el-table-column prop="title" label="视频标题" width="300"></el-table-column>
      <el-table-column prop="description" label="视频介绍" width="400"></el-table-column>
      <el-table-column prop="duration" label="时长（秒）" width="100"></el-table-column>
      <el-table-column prop="order" label="排序" width="80"></el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button size="small" @click="handleView(scope.row)">查看</el-button>
          <el-button size="small" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          <el-button size="small" type="warning" @click="handleMoveUp(scope.row)">上移</el-button>
          <el-button size="small" type="warning" @click="handleMoveDown(scope.row)">下移</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="currentPage"
      :page-size="pageSize"
      :total="total"
      layout="total, prev, pager, next"
      class="mt-4"
      @current-change="handlePageChange"
    />
    <el-dialog v-model="dialogVisible" title="添加/编辑视频" width="50%">
      <el-form :model="form" :rules="rules" ref="formRef">
        <el-form-item label="视频标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入视频标题"></el-input>
        </el-form-item>
        <el-form-item label="视频介绍" prop="description">
          <el-input
            type="textarea"
            v-model="form.description"
            placeholder="请输入视频介绍"
            rows="3"
          ></el-input>
        </el-form-item>
        <el-form-item label="视频地址" prop="video_url">
          <el-input v-model="form.video_url" placeholder="请输入视频地址"></el-input>
        </el-form-item>
        <el-form-item label="时长（秒）" prop="duration">
          <el-input-number
            v-model="form.duration"
            placeholder="请输入视频时长"
            :min="0"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="排序" prop="order">
          <el-input-number v-model="form.order" placeholder="请输入排序" :min="0"></el-input-number>
        </el-form-item>
        <el-form-item label="上传视频">
          <el-upload
            class="upload-demo"
            action="#"
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :before-remove="beforeRemove"
            multiple
            :limit="1"
            :on-exceed="handleExceed"
            :file-list="fileList"
          >
            <el-button size="small" type="primary">点击上传</el-button>
            <template #tip>
              <div class="el-upload__tip">只能上传视频文件，且不超过500mb</div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const videos = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const dialogVisible = ref(false)
const form = ref({ id: null, title: '', description: '', video_url: '', duration: 0, order: 0 })
const formRef = ref(null)
const fileList = ref([])
const rules = {
  title: [{ required: true, message: '请输入视频标题', trigger: 'blur' }],
  description: [{ required: true, message: '请输入视频介绍', trigger: 'blur' }],
  video_url: [{ required: true, message: '请输入视频地址', trigger: 'blur' }],
  duration: [{ required: true, message: '请输入视频时长', trigger: 'blur' }],
  order: [{ required: true, message: '请输入排序', trigger: 'blur' }],
}

onMounted(() => {
  fetchVideos()
})

const fetchVideos = () => {
  // 模拟数据
  videos.value = [
    {
      id: 1,
      title: '房产中介基础知识视频1',
      description: '介绍房产中介的基本概念和流程',
      duration: 300,
      order: 1,
    },
    {
      id: 2,
      title: '房产中介基础知识视频2',
      description: '深入探讨房产中介的技巧和策略',
      duration: 450,
      order: 2,
    },
    // 更多数据...
  ]
  total.value = videos.value.length
}

const handleAdd = () => {
  form.value = { id: null, title: '', description: '', video_url: '', duration: 0, order: 0 }
  fileList.value = []
  dialogVisible.value = true
}

const handleEdit = (row) => {
  form.value = { ...row }
  fileList.value = []
  dialogVisible.value = true
}

const handleView = (row) => {
  // 查看逻辑
  console.log('查看视频:', row)
}

const handleDelete = (row) => {
  // 删除逻辑
  console.log('删除视频:', row)
}

const handleMoveUp = (row) => {
  // 上移逻辑
  console.log('上移视频:', row)
}

const handleMoveDown = (row) => {
  // 下移逻辑
  console.log('下移视频:', row)
}

const handleSubmit = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      // 提交逻辑
      console.log('提交视频:', form.value)
      dialogVisible.value = false
    }
  })
}

const handlePageChange = (page) => {
  currentPage.value = page
  fetchVideos()
}

const handlePreview = (file) => {
  console.log('预览文件:', file)
}

const handleRemove = (file, fileList) => {
  console.log('移除文件:', file, fileList)
}

const beforeRemove = (file, fileList) => {
  return true
}

const handleExceed = (files, fileList) => {
  console.log('超出文件限制:', files, fileList)
}
</script>

<style scoped>
/* 课程视频管理特定样式 */
</style>
