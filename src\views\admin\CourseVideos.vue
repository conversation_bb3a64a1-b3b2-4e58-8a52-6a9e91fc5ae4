<template>
  <div class="bg-white p-6 rounded-lg shadow-md">
    <div class="header-section mb-4">
      <div class="flex items-center gap-4">
        <el-button @click="goBack" :icon="ArrowLeft">返回课程列表</el-button>
        <div>
          <h2 class="text-2xl font-bold">视频管理</h2>
          <p class="text-gray-600" v-if="courseInfo">课程：{{ courseInfo.title }}</p>
        </div>
      </div>
    </div>

    <!-- 操作栏 -->
    <div class="action-bar mb-4">
      <el-button type="primary" @click="handleAdd" :icon="Plus">添加视频</el-button>
      <el-input v-model="searchKeyword" placeholder="搜索视频标题" clearable @keyup.enter="handleSearch" @clear="handleSearch" class="search-input">
        <template #append>
          <el-button @click="handleSearch" :icon="Search"></el-button>
        </template>
      </el-input>
    </div>

    <el-table :data="videos" border style="width: 100%; table-layout: fixed;" v-loading="loading">
      <el-table-column prop="id" label="ID" width="80" fixed="left"></el-table-column>
      <el-table-column prop="order" label="排序" width="80" sortable></el-table-column>
      <el-table-column prop="title" label="视频标题" min-width="200" show-overflow-tooltip></el-table-column>
      <el-table-column prop="description" label="视频描述" min-width="200" show-overflow-tooltip></el-table-column>
      <el-table-column prop="duration" label="时长" width="100">
        <template #default="scope">
          {{ formatDuration(scope.row.duration) }}
        </template>
      </el-table-column>
      <el-table-column prop="created_at" label="创建时间" min-width="180">
        <template #default="scope">
          {{ formatDateTime(scope.row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button size="small" @click="handleView(scope.row)" :icon="View">查看</el-button>
          <el-button size="small" type="primary" @click="handleEdit(scope.row)" :icon="Edit">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDelete(scope.row)" :icon="Delete">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination v-model:current-page="currentPage" :page-size="pageSize" :total="total" layout="total, prev, pager, next" class="mt-4"
      @current-change="handlePageChange" />
    <el-dialog v-model="dialogVisible" title="添加/编辑视频" width="50%">
      <el-form :model="form" :rules="rules" ref="formRef">
        <el-form-item label="视频标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入视频标题"></el-input>
        </el-form-item>
        <el-form-item label="视频介绍" prop="description">
          <el-input type="textarea" v-model="form.description" placeholder="请输入视频介绍" rows="3"></el-input>
        </el-form-item>
        <el-form-item label="视频来源" prop="videoSource">
          <el-radio-group v-model="form.videoSource" @change="handleVideoSourceChange">
            <el-radio value="url">视频URL</el-radio>
            <el-radio value="file">上传文件</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item v-if="form.videoSource === 'url'" label="视频URL" prop="video_url">
          <el-input v-model="form.video_url" placeholder="请输入视频URL"></el-input>
        </el-form-item>

        <el-form-item v-if="form.videoSource === 'file'" label="上传视频" prop="video_file">
          <el-upload ref="uploadRef" class="upload-demo" :action="uploadAction" :before-upload="beforeUpload" :on-success="handleUploadSuccess"
            :on-error="handleUploadError" :on-progress="handleUploadProgress" :file-list="fileList" :limit="1" :on-exceed="handleExceed" :auto-upload="false"
            accept="video/*">
            <el-button size="small" type="primary" :icon="Upload">选择视频文件</el-button>
            <template #tip>
              <div class="el-upload__tip">
                支持 MP4、AVI、MOV 等格式，文件大小不超过 500MB
              </div>
            </template>
          </el-upload>
          <div v-if="uploadProgress > 0 && uploadProgress < 100" class="upload-progress">
            <el-progress :percentage="uploadProgress" :show-text="true"></el-progress>
          </div>
        </el-form-item>
        <el-form-item label="时长（秒）" prop="duration">
          <el-input-number v-model="form.duration" placeholder="请输入视频时长" :min="0"></el-input-number>
        </el-form-item>
        <el-form-item label="排序" prop="order">
          <el-input-number v-model="form.order" placeholder="请输入排序" :min="0"></el-input-number>
        </el-form-item>

      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Plus, Search, View, Edit, Delete, Upload } from '@element-plus/icons-vue'
import {
  getCourseVideoList,
  getVideoDetail,
  createCourseVideo,
  updateVideo,
  deleteVideo
} from '@/api/admin/videos'
import { getCourseDetail } from '@/api/admin/courses'

const router = useRouter()
const route = useRoute()
const courseId = ref(parseInt(route.params.id))

const videos = ref([])
const courseInfo = ref(null)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const loading = ref(false)
const submitLoading = ref(false)
const searchKeyword = ref('')

// 对话框相关
const dialogVisible = ref(false)
const form = ref({
  id: null,
  title: '',
  description: '',
  video_url: '',
  videoSource: 'url', // 'url' 或 'file'
  video_file: null,
  duration: 0,
  order: 1
})
const formRef = ref(null)
const uploadRef = ref(null)

// 上传相关
const fileList = ref([])
const uploadProgress = ref(0)
const uploadAction = '#' // 占位符，实际不使用

// 表单验证规则
const rules = {
  title: [{ required: true, message: '请输入视频标题', trigger: 'blur' }],
  videoSource: [{ required: true, message: '请选择视频来源', trigger: 'change' }],
  video_url: [
    {
      validator: (rule, value, callback) => {
        if (form.value.videoSource === 'url' && !value) {
          callback(new Error('请输入视频URL'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  video_file: [
    {
      validator: (rule, value, callback) => {
        if (form.value.videoSource === 'file' && fileList.value.length === 0) {
          callback(new Error('请选择视频文件'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  duration: [{ required: true, message: '请输入视频时长', trigger: 'blur' }],
  order: [{ required: true, message: '请输入排序序号', trigger: 'blur' }]
}

onMounted(() => {
  fetchCourseInfo()
  fetchVideos()
})

const fetchCourseInfo = async () => {
  try {
    const response = await getCourseDetail(courseId.value)
    courseInfo.value = response
  } catch (error) {
    console.error('获取课程信息失败:', error)
  }
}

const fetchVideos = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      per_page: pageSize.value
    }

    if (searchKeyword.value.trim()) {
      params.keyword = searchKeyword.value.trim()
    }

    const response = await getCourseVideoList(courseId.value, params)
    videos.value = response.data || []
    total.value = response.total || 0
  } catch (error) {
    console.error('获取视频列表失败:', error)
    let errorMessage = '获取视频列表失败'

    if (error.response) {
      errorMessage += '：' + (error.response.data?.message || `HTTP ${error.response.status}`)
    } else if (error.message) {
      errorMessage += '：' + error.message
    } else {
      errorMessage += '：网络连接失败，请检查网络或稍后重试'
    }

    ElMessage.error(errorMessage)
  } finally {
    loading.value = false
  }
}

const goBack = () => {
  router.push('/admin/courses')
}

const handleAdd = () => {
  form.value = {
    id: null,
    title: '',
    description: '',
    video_url: '',
    videoSource: 'url',
    video_file: null,
    duration: 0,
    order: videos.value.length + 1
  }
  fileList.value = []
  uploadProgress.value = 0
  dialogVisible.value = true
}

const handleEdit = async (row) => {
  try {
    loading.value = true
    const response = await getVideoDetail(row.id)
    form.value = {
      ...response,
      videoSource: response.video_url ? 'url' : 'file',
      video_file: null
    }
    fileList.value = []
    uploadProgress.value = 0
    dialogVisible.value = true
  } catch (error) {
    ElMessage.error('获取视频详情失败：' + (error.response?.data?.message || error.message))
  } finally {
    loading.value = false
  }
}

const handleView = async (row) => {
  try {
    const response = await getVideoDetail(row.id)
    ElMessageBox.alert(
      `<div>
        <p><strong>视频标题：</strong>${response.title}</p>
        <p><strong>视频描述：</strong>${response.description || '暂无描述'}</p>
        <p><strong>视频URL：</strong>${response.video_url}</p>
        <p><strong>视频时长：</strong>${formatDuration(response.duration)}</p>
        <p><strong>排序序号：</strong>${response.order}</p>
        <p><strong>创建时间：</strong>${formatDateTime(response.created_at)}</p>
      </div>`,
      '视频详情',
      {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '确定'
      }
    )
  } catch (error) {
    ElMessage.error('获取视频详情失败：' + (error.response?.data?.message || error.message))
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除视频"${row.title}"吗？此操作不可恢复！`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    loading.value = true
    await deleteVideo(row.id)
    ElMessage.success('删除成功')
    fetchVideos()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败：' + (error.response?.data?.message || error.message))
    }
  } finally {
    loading.value = false
  }
}

const handleSubmit = async () => {
  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    submitLoading.value = true

    let videoData

    if (form.value.videoSource === 'file') {
      // 使用FormData上传文件
      videoData = new FormData()
      videoData.append('title', form.value.title)
      videoData.append('description', form.value.description || '')
      videoData.append('order', form.value.order)

      if (form.value.duration) {
        videoData.append('duration', form.value.duration)
      }

      // 添加文件
      if (fileList.value.length > 0) {
        videoData.append('video_file', fileList.value[0].raw)
      }
    } else {
      // 使用JSON数据提交URL
      videoData = {
        title: form.value.title,
        description: form.value.description,
        video_url: form.value.video_url,
        duration: form.value.duration,
        order: form.value.order
      }
    }

    if (form.value.id) {
      // 编辑视频
      await updateVideo(form.value.id, videoData)
      ElMessage.success('视频更新成功')
    } else {
      // 创建视频
      await createCourseVideo(courseId.value, videoData)
      ElMessage.success('视频创建成功')
    }

    dialogVisible.value = false
    fetchVideos()
  } catch (error) {
    console.error('提交视频失败:', error)
    let errorMessage = form.value.id ? '更新视频失败' : '创建视频失败'

    if (error.response) {
      errorMessage += '：' + (error.response.data?.message || `HTTP ${error.response.status}`)
    } else if (error.message) {
      errorMessage += '：' + error.message
    } else {
      errorMessage += '：未知错误，请稍后重试'
    }

    ElMessage.error(errorMessage)
  } finally {
    submitLoading.value = false
  }
}

const handlePageChange = (page) => {
  currentPage.value = page
  fetchVideos()
}

const handleSearch = () => {
  currentPage.value = 1
  fetchVideos()
}

const handleCloseDialog = () => {
  dialogVisible.value = false
  form.value = {
    id: null,
    title: '',
    description: '',
    video_url: '',
    videoSource: 'url',
    video_file: null,
    duration: 0,
    order: 1
  }
  fileList.value = []
  uploadProgress.value = 0
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 视频来源切换处理
const handleVideoSourceChange = (value) => {
  // 清空相关字段
  if (value === 'url') {
    form.value.video_file = null
    fileList.value = []
    uploadProgress.value = 0
  } else {
    form.value.video_url = ''
  }

  // 重新验证表单
  if (formRef.value) {
    formRef.value.clearValidate(['video_url', 'video_file'])
  }
}

// 上传前检查
const beforeUpload = (file) => {
  const isVideo = file.type.startsWith('video/')
  const isLt500M = file.size / 1024 / 1024 < 500

  if (!isVideo) {
    ElMessage.error('只能上传视频文件!')
    return false
  }
  if (!isLt500M) {
    ElMessage.error('视频文件大小不能超过 500MB!')
    return false
  }

  // 如果上传文件时没有设置时长，尝试获取视频时长
  if (!form.value.duration) {
    getVideoDuration(file)
  }

  return false // 阻止自动上传，我们手动处理
}

// 获取视频时长
const getVideoDuration = (file) => {
  const video = document.createElement('video')
  video.preload = 'metadata'
  video.onloadedmetadata = () => {
    window.URL.revokeObjectURL(video.src)
    form.value.duration = Math.round(video.duration)
  }
  video.src = URL.createObjectURL(file)
}

// 文件选择成功
const handleUploadSuccess = (response, file) => {
  ElMessage.success('文件上传成功')
  uploadProgress.value = 100
}

// 文件上传失败
const handleUploadError = (error) => {
  ElMessage.error('文件上传失败：' + error.message)
  uploadProgress.value = 0
}

// 上传进度
const handleUploadProgress = (event) => {
  uploadProgress.value = Math.round(event.percent)
}

// 文件数量超出限制
const handleExceed = () => {
  ElMessage.warning('最多只能上传一个视频文件')
}

// 格式化时长（秒转换为时分秒）
const formatDuration = (seconds) => {
  if (!seconds) return '0秒'

  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  if (hours > 0) {
    return `${hours}时${minutes}分${secs}秒`
  } else if (minutes > 0) {
    return `${minutes}分${secs}秒`
  } else {
    return `${secs}秒`
  }
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<style scoped>
/* 视频管理特定样式 */
.header-section {
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 16px;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.search-input {
  width: 300px;
}

.el-table {
  width: 100% !important;
}

.el-table__body-wrapper {
  overflow-x: auto;
}

/* 确保容器占满宽度 */
.bg-white {
  width: 100%;
  box-sizing: border-box;
}

/* 对话框样式优化 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 上传组件样式 */
.upload-demo {
  width: 100%;
}

.upload-progress {
  margin-top: 10px;
}

.el-upload__tip {
  color: #909399;
  font-size: 12px;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-bar {
    flex-direction: column;
    align-items: stretch;
  }

  .search-input {
    width: 100%;
  }
}
</style>
