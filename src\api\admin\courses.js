import request from '@/utils/request'

/**
 * 课程管理相关API接口
 */

/**
 * 获取课程列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.per_page - 每页数量
 * @param {string} params.keyword - 搜索关键词
 * @returns {Promise}
 */
export function getCourseList(params = {}) {
  return request.get('/admin/courses', { params })
}

/**
 * 获取课程详情
 * @param {number} id - 课程ID
 * @returns {Promise}
 */
export function getCourseDetail(id) {
  return request.get(`/admin/courses/${id}`)
}

/**
 * 创建课程
 * @param {Object} data - 课程数据
 * @param {string} data.title - 课程标题
 * @param {string} data.description - 课程描述
 * @returns {Promise}
 */
export function createCourse(data) {
  return request.post('/admin/courses', data)
}

/**
 * 更新课程
 * @param {number} id - 课程ID
 * @param {Object} data - 课程数据
 * @param {string} data.title - 课程标题
 * @param {string} data.description - 课程描述
 * @returns {Promise}
 */
export function updateCourse(id, data) {
  return request.put(`/admin/courses/${id}`, data)
}

/**
 * 删除课程
 * @param {number} id - 课程ID
 * @returns {Promise}
 */
export function deleteCourse(id) {
  return request.delete(`/admin/courses/${id}`)
}
