# CMS Banner API 集成实现

## 🎯 实现目标

将CMS前台的banner轮播从模拟数据改为使用真实的API数据，实现与后台管理的数据同步。

## 📋 实现内容

### 1. API 接口封装 (`src/api/cms/banners.js`)

#### 1.1 核心接口

```javascript
/**
 * 获取前台横幅列表
 * 获取所有横幅，按排序显示，无需登录权限
 */
export function getCmsBanners() {
  return request.get('/cms/banners')
}
```

#### 1.2 数据处理函数

```javascript
/**
 * 处理Banner图片URL
 * 利用Vite代理配置处理图片路径
 */
export function formatBannerImageUrl(imageUrl) {
  if (!imageUrl) return ''

  // 如果已经是完整的URL，直接返回
  if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
    return imageUrl
  }

  // 相对路径由Vite代理处理
  return imageUrl
}

/**
 * 处理Banner数据
 * 将API返回的数据格式化为前台需要的格式
 */
export function formatBannersData(banners) {
  return banners.map((banner) => ({
    id: banner.id,
    title: banner.description || '信息平台',
    description: banner.description || '',
    subtitle: banner.description || '',
    image: formatBannerImageUrl(banner.image_url),
    image_url: formatBannerImageUrl(banner.image_url),
    link_url: banner.link_url || '#',
    order: banner.order || 0,
  }))
}
```

### 2. CMS首页集成 (`src/views/cms/CmsHome.vue`)

#### 2.1 导入API函数

```javascript
import { getCmsBanners, formatBannersData } from '@/api/cms/banners'
```

#### 2.2 更新数据获取逻辑

```javascript
// 修改前：使用模拟API
const fetchData = async () => {
  const [categoriesRes, hotCategoriesRes, bannersRes] = await Promise.all([
    getCategories(),
    getHotCategories(),
    getBanners(), // 模拟API
  ])

  banners.value = bannersRes.data || []
}

// 修改后：使用真实API
const fetchData = async () => {
  const [categoriesRes, hotCategoriesRes, bannersRes] = await Promise.all([
    getCategories(),
    getHotCategories(),
    getCmsBanners(), // 真实API
  ])

  // 处理banner数据
  const rawBanners = bannersRes.data || bannersRes || []
  banners.value = formatBannersData(rawBanners)
}
```

#### 2.3 更新模拟数据

```javascript
// 确保模拟数据格式与API一致
const mockBanners = [
  {
    id: 1,
    image_url: 'https://via.placeholder.com/1200x300/dc2626/ffffff?text=服务数字化转型',
    link_url: 'https://example.com/digital-government',
    description: '服务弘扬群行 教育家精神',
    order: 1,
  },
  {
    id: 2,
    image_url: 'https://via.placeholder.com/1200x300/1e40af/ffffff?text=优化营商环境',
    link_url: 'https://example.com/business-environment',
    description: '优化营商环境 激发市场活力',
    order: 2,
  },
]

banners.value = formatBannersData(mockBanners)
```

### 3. API 数据格式

#### 3.1 API 响应格式

```json
[
  {
    "id": 1,
    "image_url": "/storage/banners/banner_1753326855_HO9qMuu8mR.png",
    "link_url": "https://www.baidu.com",
    "description": "这是佃户屯系统",
    "order": 0
  }
]
```

#### 3.2 前台使用格式

```javascript
{
  id: 1,
  title: "这是佃户屯系统",
  description: "这是佃户屯系统",
  subtitle: "这是佃户屯系统",
  image: "/storage/banners/banner_1753326855_HO9qMuu8mR.png",
  image_url: "/storage/banners/banner_1753326855_HO9qMuu8mR.png",
  link_url: "https://www.baidu.com",
  order: 0
}
```

### 4. 代理配置

#### 4.1 Vite 代理设置 (`vite.config.js`)

```javascript
export default defineConfig({
  server: {
    proxy: {
      '/api': {
        target: 'http://127.0.0.1:8000/',
        changeOrigin: true,
      },
      '/storage': {
        target: 'http://127.0.0.1:8000/',
        changeOrigin: true,
      },
    },
  },
})
```

#### 4.2 图片访问路径

- **API返回**: `/storage/banners/banner_xxx.png`
- **前台访问**: `http://localhost:5174/storage/banners/banner_xxx.png`
- **实际请求**: `http://127.0.0.1:8000/storage/banners/banner_xxx.png`

## 🔧 技术实现要点

### 1. 数据格式转换

```javascript
// API数据 → 前台数据的映射关系
const formatBannersData = (banners) => {
  return banners.map((banner) => ({
    // 兼容现有轮播组件的字段名
    id: banner.id,
    title: banner.description, // 轮播标题
    description: banner.description, // 轮播描述
    subtitle: banner.description, // 轮播副标题
    image: banner.image_url, // 轮播图片
    image_url: banner.image_url, // 备用图片字段
    link_url: banner.link_url, // 点击跳转链接
    order: banner.order, // 排序
  }))
}
```

### 2. 错误处理

```javascript
try {
  const bannersRes = await getCmsBanners()
  const rawBanners = bannersRes.data || bannersRes || []
  banners.value = formatBannersData(rawBanners)
} catch (error) {
  console.error('获取数据失败:', error)
  // 降级到模拟数据
  loadMockData()
}
```

### 3. 图片路径处理

```javascript
// 利用Vite代理，无需手动拼接完整URL
export function formatBannerImageUrl(imageUrl) {
  if (!imageUrl) return ''

  // 完整URL直接返回
  if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
    return imageUrl
  }

  // 相对路径由Vite代理自动处理
  return imageUrl
}
```

## 🎨 前台展示效果

### 1. 轮播组件

```vue
<el-carousel height="300px" :interval="5000" arrow="hover" indicator-position="inside">
  <el-carousel-item v-for="banner in banners" :key="banner.id">
    <div class="relative w-full h-full bg-gradient-to-r from-red-600 to-orange-500">
      <div class="absolute inset-0 flex items-center justify-center">
        <div class="text-center text-white">
          <h2 class="text-4xl font-bold mb-4">{{ banner.title }}</h2>
          <p class="text-xl">{{ banner.description }}</p>
        </div>
      </div>
      <div class="absolute bottom-0 left-0 text-white text-sm px-4 py-2 bg-black bg-opacity-50">
        {{ banner.subtitle }}
      </div>
    </div>
  </el-carousel-item>
</el-carousel>
```

### 2. 数据流向

```
后台管理 → 创建/编辑Banner → 保存到数据库
    ↓
API接口 → /cms/banners → 返回Banner列表
    ↓
前台CMS → 获取数据 → 格式化处理 → 轮播展示
```

## 🧪 测试验证

### 1. API 测试

```bash
# 直接测试API接口
curl -X 'GET' \
  'http://127.0.0.1:8000/api/cms/banners' \
  -H 'accept: application/json'
```

### 2. 前台测试

1. **访问CMS首页**: `http://localhost:5174/cms`
2. **检查轮播**: 应该显示后台管理中创建的banner
3. **图片显示**: 确认图片能正常加载
4. **点击跳转**: 确认点击banner能跳转到指定链接

### 3. 数据同步测试

1. **后台创建**: 在 `http://localhost:5174/admin/banners` 创建新banner
2. **前台刷新**: 刷新 `http://localhost:5174/cms` 页面
3. **验证同步**: 确认新banner出现在轮播中

## ✅ 实现效果

### 1. 数据同步

- ✅ 前台banner数据来自真实API
- ✅ 与后台管理数据实时同步
- ✅ 支持图片、链接、描述等完整信息

### 2. 用户体验

- ✅ 轮播正常工作，支持自动播放
- ✅ 图片正常显示，支持点击跳转
- ✅ API失败时自动降级到模拟数据

### 3. 技术特性

- ✅ 代码结构清晰，易于维护
- ✅ 错误处理完善，用户体验友好
- ✅ 数据格式转换灵活，兼容性好

---

**实现状态**: ✅ 完成  
**API接口**: `GET /cms/banners`  
**测试地址**: `http://localhost:5174/cms`  
**数据来源**: 真实API + 模拟数据降级
