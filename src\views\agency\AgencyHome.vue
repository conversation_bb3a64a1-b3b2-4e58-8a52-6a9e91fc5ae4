<template>
  <div class="p-4">
    <el-skeleton :loading="loading" animated :rows="2">
      <template v-if="status !== 'passed'">
        <el-result
          :icon="statusIcon[status] || 'info'"
          :title="statusTitle[status] || '提示'"
          :sub-title="message"
        >
          <template #extra>
            <el-button
              v-if="status === 'unfilled' || status === 'rejected'"
              type="primary"
              @click="$router.push('/agency/apply-join')"
            >
              去填写/修改资料
            </el-button>
            <el-button v-if="status === 'pending'" disabled type="primary">等待审核</el-button>
          </template>
        </el-result>
        <div v-if="status === 'rejected' && reject_reason" class="mt-4 text-red-500">
          驳回原因：{{ reject_reason }}
        </div>
      </template>
      <template v-else>
        <!-- 审核通过，显示原有内容 -->
        <h2 class="text-2xl font-bold mb-4">机构首页</h2>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <el-card shadow="hover">
            <div class="flex items-center">
              <el-icon class="text-2xl text-blue-500 mr-2"><OfficeBuilding /></el-icon>
              <div>
                <div class="text-gray-500 text-sm">机构审核状态</div>
                <div class="text-lg font-bold">
                  <el-tag :type="statusType[agency.status]">{{
                    statusText[agency.status] || agency.status || '-'
                  }}</el-tag>
                </div>
              </div>
            </div>
          </el-card>
          <el-card shadow="hover">
            <div class="flex items-center">
              <el-icon class="text-2xl text-green-500 mr-2"><User /></el-icon>
              <div>
                <div class="text-gray-500 text-sm">经纪人总数</div>
                <div class="text-lg font-bold">{{ stats.total }}</div>
              </div>
            </div>
          </el-card>
          <el-card shadow="hover">
            <div class="flex items-center">
              <el-icon class="text-2xl text-blue-500 mr-2"><CircleCheckFilled /></el-icon>
              <div>
                <div class="text-gray-500 text-sm">审核通过</div>
                <div class="text-lg font-bold">{{ stats.passed }}</div>
              </div>
            </div>
          </el-card>
          <el-card shadow="hover">
            <div class="flex items-center">
              <el-icon class="text-2xl text-yellow-500 mr-2"><Clock /></el-icon>
              <div>
                <div class="text-gray-500 text-sm">审核中</div>
                <div class="text-lg font-bold">{{ stats.pending }}</div>
              </div>
            </div>
          </el-card>
          <el-card shadow="hover">
            <div class="flex items-center">
              <el-icon class="text-2xl text-red-500 mr-2"><CircleCloseFilled /></el-icon>
              <div>
                <div class="text-gray-500 text-sm">审核被拒绝</div>
                <div class="text-lg font-bold">{{ stats.rejected }}</div>
              </div>
            </div>
          </el-card>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <el-card shadow="hover" class="cursor-pointer" @click="$router.push('/agency/brokers')">
            <div class="flex items-center">
              <el-icon class="text-2xl text-blue-500 mr-2"><UserFilled /></el-icon>
              <div>
                <div class="text-lg font-bold">经纪人管理</div>
                <div class="text-gray-500 text-sm">查看/管理旗下经纪人</div>
              </div>
            </div>
          </el-card>
          <el-card shadow="hover" class="cursor-pointer" @click="$router.push('/agency/profile')">
            <div class="flex items-center">
              <el-icon class="text-2xl text-green-500 mr-2"><Tickets /></el-icon>
              <div>
                <div class="text-lg font-bold">机构信息</div>
                <div class="text-gray-500 text-sm">查看/编辑机构资料</div>
              </div>
            </div>
          </el-card>
          <el-card
            shadow="hover"
            class="cursor-pointer"
            @click="$router.push('/agency/apply-join')"
          >
            <div class="flex items-center">
              <el-icon class="text-2xl text-orange-500 mr-2"><Document /></el-icon>
              <div>
                <div class="text-lg font-bold">入驻申请</div>
                <div class="text-gray-500 text-sm">查看/补充入驻申请</div>
              </div>
            </div>
          </el-card>
        </div>
      </template>
    </el-skeleton>
  </div>
</template>

<script setup>
import { ref, onMounted, inject } from 'vue'
import service from '@/utils/request'
import { ElMessage } from 'element-plus'
import {
  OfficeBuilding,
  User,
  CircleCheckFilled,
  Clock,
  CircleCloseFilled,
  UserFilled,
  Tickets,
  Document,
} from '@element-plus/icons-vue'

const agency = ref({})
const stats = ref({ total: 0, passed: 0, pending: 0, rejected: 0 })
const loading = ref(false)

const status = ref('passed')
const message = ref('')
const reject_reason = ref('')

// 注入父组件提供的共享数据和方法
const sharedAgencyInfo = inject('agencyInfo')
const parentFetchAgencyInfo = inject('fetchAgencyInfo')

const statusIcon = {
  unfilled: 'edit',
  pending: 'clock',
  rejected: 'close',
  passed: 'success',
}
const statusTitle = {
  unfilled: '未填写资料',
  pending: '审核中',
  rejected: '审核被拒绝',
  passed: '已通过',
}

const statusText = {
  passed: '已通过',
  pending: '审核中',
  rejected: '被拒绝',
}
const statusType = {
  passed: 'success',
  pending: 'warning',
  rejected: 'danger',
}

// 从共享数据更新本地状态
function updateLocalData() {
  if (sharedAgencyInfo.value) {
    const data = sharedAgencyInfo.value
    status.value = data?.status || 'passed'
    message.value = data?.message || ''
    reject_reason.value = data?.reject_reason || ''
    agency.value = data?.agency || {}
    stats.value = data?.stats || { total: 0, passed: 0, pending: 0, rejected: 0 }
  }
}

onMounted(() => {
  loading.value = true
  // 如果父组件已经获取了数据，直接使用
  if (sharedAgencyInfo.value) {
    updateLocalData()
    loading.value = false
  } else {
    // 否则请求一次数据
    parentFetchAgencyInfo()
      .then(() => {
        updateLocalData()
        loading.value = false
      })
      .catch(() => {
        loading.value = false
      })
  }
})
</script>
