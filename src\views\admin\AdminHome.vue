<template>
  <div class="space-y-6">
    <!-- 欢迎信息 -->
    <div class="bg-white p-6 rounded-lg shadow-md">
      <h2 class="text-2xl font-bold text-gray-800">欢迎回来，{{ userInfo.name || '管理员' }}</h2>
      <p class="text-gray-600 mt-2">今天是 {{ currentDate }}，祝您工作愉快！</p>
    </div>

    <!-- 数据概览 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <el-card shadow="hover" :body-style="{ padding: '20px' }">
        <template #header>
          <div class="flex items-center">
            <el-icon class="mr-2"><OfficeBuilding /></el-icon>
            <span>机构总数</span>
          </div>
        </template>
        <div class="text-center">
          <div class="text-3xl font-bold text-blue-600">{{ statistics.agencyCount }}</div>
          <div class="text-gray-500 mt-2">
            较上月 {{ statistics.agencyGrowth > 0 ? '+' : '' }}{{ statistics.agencyGrowth }}%
          </div>
        </div>
      </el-card>

      <el-card shadow="hover" :body-style="{ padding: '20px' }">
        <template #header>
          <div class="flex items-center">
            <el-icon class="mr-2"><User /></el-icon>
            <span>经纪人总数</span>
          </div>
        </template>
        <div class="text-center">
          <div class="text-3xl font-bold text-green-600">{{ statistics.brokerCount }}</div>
          <div class="text-gray-500 mt-2">
            较上月 {{ statistics.brokerGrowth > 0 ? '+' : '' }}{{ statistics.brokerGrowth }}%
          </div>
        </div>
      </el-card>

      <el-card shadow="hover" :body-style="{ padding: '20px' }">
        <template #header>
          <div class="flex items-center">
            <el-icon class="mr-2"><List /></el-icon>
            <span>培训计划数</span>
          </div>
        </template>
        <div class="text-center">
          <div class="text-3xl font-bold text-purple-600">{{ statistics.planCount }}</div>
          <div class="text-gray-500 mt-2">
            较上月 {{ statistics.planGrowth > 0 ? '+' : '' }}{{ statistics.planGrowth }}%
          </div>
        </div>
      </el-card>

      <el-card shadow="hover" :body-style="{ padding: '20px' }">
        <template #header>
          <div class="flex items-center">
            <el-icon class="mr-2"><Money /></el-icon>
            <span>本月收入</span>
          </div>
        </template>
        <div class="text-center">
          <div class="text-3xl font-bold text-orange-600">¥{{ statistics.monthlyIncome }}</div>
          <div class="text-gray-500 mt-2">
            较上月 {{ statistics.incomeGrowth > 0 ? '+' : '' }}{{ statistics.incomeGrowth }}%
          </div>
        </div>
      </el-card>
    </div>

    <!-- 快捷导航 -->
    <div class="bg-white p-6 rounded-lg shadow-md">
      <h3 class="text-lg font-bold mb-4">快捷导航</h3>
      <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        <router-link
          v-for="nav in quickNavs"
          :key="nav.path"
          :to="nav.path"
          class="flex items-center p-4 rounded-lg border hover:shadow-md transition-shadow duration-300"
        >
          <el-icon class="text-2xl mr-3" :class="nav.iconColor">
            <component :is="nav.icon" />
          </el-icon>
          <div>
            <div class="font-medium">{{ nav.title }}</div>
            <div class="text-sm text-gray-500">{{ nav.desc }}</div>
          </div>
        </router-link>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 机构增长趋势 -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h3 class="text-lg font-bold mb-4">机构增长趋势</h3>
        <div ref="agencyChartRef" style="height: 300px"></div>
      </div>

      <!-- 经纪人分布 -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h3 class="text-lg font-bold mb-4">经纪人分布</h3>
        <div ref="brokerChartRef" style="height: 300px"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import {
  House,
  OfficeBuilding,
  User,
  Document,
  Money,
  Setting,
  List,
  DataLine,
  Tickets,
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'

const userInfo = ref(JSON.parse(localStorage.getItem('user') || '{}'))
const currentDate = new Date().toLocaleDateString('zh-CN', {
  year: 'numeric',
  month: 'long',
  day: 'numeric',
  weekday: 'long',
})

// 统计数据
const statistics = ref({
  agencyCount: 128,
  agencyGrowth: 15,
  brokerCount: 1024,
  brokerGrowth: 8,
  planCount: 36,
  planGrowth: 20,
  monthlyIncome: '128,500',
  incomeGrowth: 12,
})

// 快捷导航
const quickNavs = [
  {
    title: '机构管理',
    desc: '管理中介机构信息',
    path: '/admin/agencies',
    icon: OfficeBuilding,
    iconColor: 'text-blue-500',
  },
  {
    title: '经纪人管理',
    desc: '管理经纪人信息',
    path: '/admin/brokers',
    icon: User,
    iconColor: 'text-green-500',
  },
  {
    title: '培训计划',
    desc: '管理培训计划',
    path: '/admin/training-plans',
    icon: List,
    iconColor: 'text-purple-500',
  },
  {
    title: '数据统计',
    desc: '查看统计报表',
    path: '/admin/statistics',
    icon: DataLine,
    iconColor: 'text-orange-500',
  },
  {
    title: '系统设置',
    desc: '配置系统参数',
    path: '/admin/settings',
    icon: Setting,
    iconColor: 'text-gray-500',
  },
  {
    title: '订单管理',
    desc: '管理培训订单',
    path: '/admin/orders',
    icon: Tickets,
    iconColor: 'text-red-500',
  },
]

// 图表DOM引用
const agencyChartRef = ref(null)
const brokerChartRef = ref(null)

// 图表实例
let agencyChart = null
let brokerChart = null

onMounted(() => {
  // 初始化机构增长趋势图表
  agencyChart = echarts.init(agencyChartRef.value)
  agencyChart.setOption({
    tooltip: {
      trigger: 'axis',
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月'],
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        data: [80, 92, 101, 114, 120, 128],
        type: 'line',
        smooth: true,
        areaStyle: {
          opacity: 0.3,
        },
        itemStyle: {
          color: '#3B82F6',
        },
      },
    ],
  })

  // 初始化经纪人分布图表
  brokerChart = echarts.init(brokerChartRef.value)
  brokerChart.setOption({
    tooltip: {
      trigger: 'item',
    },
    legend: {
      orient: 'vertical',
      left: 'left',
    },
    series: [
      {
        type: 'pie',
        radius: '50%',
        data: [
          { value: 500, name: '一级经纪人' },
          { value: 300, name: '二级经纪人' },
          { value: 224, name: '实习经纪人' },
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  })

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    agencyChart?.resize()
    brokerChart?.resize()
  })
})
</script>
