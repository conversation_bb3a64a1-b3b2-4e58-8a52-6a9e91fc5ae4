<template>
  <div class="advanced-rich-text-editor">
    <div ref="editorRef" class="editor-container"></div>

    <!-- 图片上传进度提示 -->
    <el-dialog v-model="uploadDialogVisible" title="图片上传中" width="400px" :close-on-click-modal="false">
      <div class="upload-progress">
        <el-progress :percentage="uploadProgress" :status="uploadStatus"></el-progress>
        <p class="mt-2 text-center text-gray-600">{{ uploadMessage }}</p>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import { ElMessage, ElDialog, ElProgress } from 'element-plus'
import Quill from 'quill'
import 'quill/dist/quill.snow.css'
import { uploadImage } from '@/api/upload'

// 定义props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '请输入内容...'
  },
  height: {
    type: String,
    default: '300px'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  uploadMode: {
    type: String,
    default: 'base64', // 'base64' | 'server'
    validator: (value) => ['base64', 'server'].includes(value)
  }
})

// 定义emits
const emit = defineEmits(['update:modelValue', 'change'])

// 响应式数据
const editorRef = ref()
const uploadDialogVisible = ref(false)
const uploadProgress = ref(0)
const uploadStatus = ref('')
const uploadMessage = ref('')
let quill = null

// 图片上传处理函数
const imageHandler = () => {
  const input = document.createElement('input')
  input.setAttribute('type', 'file')
  input.setAttribute('accept', 'image/*')
  input.setAttribute('multiple', 'true')
  input.click()

  input.onchange = async () => {
    const files = input.files
    if (files && files.length > 0) {
      for (let i = 0; i < files.length; i++) {
        await handleImageUpload(files[i])
      }
    }
  }
}

// 处理单个图片上传
const handleImageUpload = async (file) => {
  // 检查文件大小（限制为10MB）
  if (file.size > 10 * 1024 * 1024) {
    ElMessage.error('图片大小不能超过10MB')
    return
  }

  // 检查文件类型
  if (!file.type.startsWith('image/')) {
    ElMessage.error('请选择图片文件')
    return
  }

  // 获取当前光标位置
  const range = quill.getSelection()
  const index = range ? range.index : quill.getLength()

  if (props.uploadMode === 'server') {
    // 服务器上传模式
    await uploadToServer(file, index)
  } else {
    // Base64模式
    uploadAsBase64(file, index)
  }
}

// 服务器上传
const uploadToServer = async (file, index) => {
  try {
    uploadDialogVisible.value = true
    uploadProgress.value = 0
    uploadStatus.value = ''
    uploadMessage.value = '正在上传图片...'

    // 模拟上传进度
    const progressInterval = setInterval(() => {
      if (uploadProgress.value < 90) {
        uploadProgress.value += 10
      }
    }, 200)

    const response = await uploadImage(file)

    clearInterval(progressInterval)
    uploadProgress.value = 100
    uploadStatus.value = 'success'
    uploadMessage.value = '上传成功！'

    // 插入图片
    const imageUrl = response.data?.url || response.url
    if (imageUrl) {
      quill.insertEmbed(index, 'image', imageUrl)
      quill.setSelection(index + 1)
    }

    setTimeout(() => {
      uploadDialogVisible.value = false
    }, 1000)

  } catch (error) {
    console.error('图片上传失败:', error)
    uploadProgress.value = 100
    uploadStatus.value = 'exception'
    uploadMessage.value = '上传失败，将使用本地预览'

    // 上传失败时降级为base64
    uploadAsBase64(file, index)

    setTimeout(() => {
      uploadDialogVisible.value = false
    }, 2000)
  }
}

// Base64上传
const uploadAsBase64 = (file, index) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    const base64 = e.target.result
    quill.insertEmbed(index, 'image', base64)
    quill.setSelection(index + 1)
  }
  reader.readAsDataURL(file)
}

// 自定义粘贴处理，保持Word格式
const handlePaste = (node, delta) => {
  // 检查delta是否存在
  if (!delta) {
    return delta
  }

  // 处理Word粘贴的特殊格式
  if (node.nodeType === Node.ELEMENT_NODE) {
    const tagName = node.tagName.toLowerCase()
    const Delta = Quill.import('delta')

    // 保持标题格式
    if (['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(tagName)) {
      const level = parseInt(tagName.charAt(1))
      return delta.compose(new Delta().retain(delta.length(), { header: level }))
    }

    // 保持列表格式
    if (tagName === 'ul' || tagName === 'ol') {
      return delta.compose(new Delta().retain(delta.length(), { list: tagName === 'ol' ? 'ordered' : 'bullet' }))
    }

    // 保持文本格式
    if (tagName === 'strong' || tagName === 'b') {
      return delta.compose(new Delta().retain(delta.length(), { bold: true }))
    }

    if (tagName === 'em' || tagName === 'i') {
      return delta.compose(new Delta().retain(delta.length(), { italic: true }))
    }

    if (tagName === 'u') {
      return delta.compose(new Delta().retain(delta.length(), { underline: true }))
    }

    if (tagName === 's' || tagName === 'strike') {
      return delta.compose(new Delta().retain(delta.length(), { strike: true }))
    }

    // 保持链接
    if (tagName === 'a' && node.href) {
      return delta.compose(new Delta().retain(delta.length(), { link: node.href }))
    }

    // 保持代码格式
    if (tagName === 'code') {
      return delta.compose(new Delta().retain(delta.length(), { code: true }))
    }

    // 保持引用格式
    if (tagName === 'blockquote') {
      return delta.compose(new Delta().retain(delta.length(), { blockquote: true }))
    }
  }

  return delta
}

// Quill配置
const quillOptions = {
  theme: 'snow',
  placeholder: props.placeholder,
  modules: {
    toolbar: {
      container: [
        [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
        [{ 'font': [] }],
        [{ 'size': ['small', false, 'large', 'huge'] }],
        ['bold', 'italic', 'underline', 'strike'],
        [{ 'color': [] }, { 'background': [] }],
        [{ 'script': 'sub' }, { 'script': 'super' }],
        [{ 'list': 'ordered' }, { 'list': 'bullet' }],
        [{ 'indent': '-1' }, { 'indent': '+1' }],
        [{ 'direction': 'rtl' }],
        [{ 'align': [] }],
        ['blockquote', 'code-block'],
        ['link', 'image', 'video'],
        ['clean']
      ],
      handlers: {
        image: imageHandler
      }
    },
    history: {
      delay: 1000,
      maxStack: 50,
      userOnly: true
    }
  }
}

// 初始化编辑器
const initEditor = () => {
  if (editorRef.value) {
    quill = new Quill(editorRef.value, quillOptions)

    // 设置编辑器高度
    const editorContainer = editorRef.value.querySelector('.ql-editor')
    if (editorContainer) {
      editorContainer.style.minHeight = props.height
    }

    // 设置初始内容
    if (props.modelValue) {
      quill.root.innerHTML = props.modelValue
    }

    // 监听内容变化
    quill.on('text-change', () => {
      const html = quill.root.innerHTML
      const isEmpty = quill.getText().trim().length === 0
      const content = isEmpty ? '' : html

      emit('update:modelValue', content)
      emit('change', content)
    })

    // 设置禁用状态
    if (props.disabled) {
      quill.disable()
    }

    // 自定义粘贴处理
    quill.clipboard.addMatcher(Node.ELEMENT_NODE, handlePaste)

    // 处理拖拽上传
    const editor = editorRef.value.querySelector('.ql-editor')
    editor.addEventListener('drop', handleDrop)
    editor.addEventListener('dragover', handleDragOver)
  }
}

// 处理拖拽上传
const handleDragOver = (e) => {
  e.preventDefault()
  e.stopPropagation()
}

const handleDrop = async (e) => {
  e.preventDefault()
  e.stopPropagation()

  const files = e.dataTransfer.files
  if (files && files.length > 0) {
    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      if (file.type.startsWith('image/')) {
        await handleImageUpload(file)
      }
    }
  }
}

// 监听modelValue变化
watch(() => props.modelValue, (newValue) => {
  if (quill && newValue !== quill.root.innerHTML) {
    quill.root.innerHTML = newValue || ''
  }
})

// 监听disabled状态
watch(() => props.disabled, (newValue) => {
  if (quill) {
    if (newValue) {
      quill.disable()
    } else {
      quill.enable()
    }
  }
})

// 生命周期
onMounted(() => {
  initEditor()
})

onBeforeUnmount(() => {
  if (quill) {
    const editor = editorRef.value?.querySelector('.ql-editor')
    if (editor) {
      editor.removeEventListener('drop', handleDrop)
      editor.removeEventListener('dragover', handleDragOver)
    }
    quill = null
  }
})

// 暴露方法给父组件
defineExpose({
  getQuill: () => quill,
  getHTML: () => quill ? quill.root.innerHTML : '',
  getText: () => quill ? quill.getText() : '',
  setHTML: (html) => {
    if (quill) {
      quill.root.innerHTML = html
    }
  },
  focus: () => {
    if (quill) {
      quill.focus()
    }
  },
  blur: () => {
    if (quill) {
      quill.blur()
    }
  },
  insertImage: (url) => {
    if (quill) {
      const range = quill.getSelection()
      const index = range ? range.index : quill.getLength()
      quill.insertEmbed(index, 'image', url)
      quill.setSelection(index + 1)
    }
  }
})
</script>

<style scoped>
.advanced-rich-text-editor {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: white;
}

.advanced-rich-text-editor:hover {
  border-color: #c0c4cc;
}

.advanced-rich-text-editor:focus-within {
  border-color: #409eff;
}

.upload-progress {
  padding: 20px;
}

:deep(.ql-toolbar) {
  border-bottom: 1px solid #e4e7ed;
  border-top: none;
  border-left: none;
  border-right: none;
}

:deep(.ql-container) {
  border: none;
  font-size: 14px;
}

:deep(.ql-editor) {
  line-height: 1.6;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
    sans-serif;
}

:deep(.ql-editor.ql-blank::before) {
  color: #c0c4cc;
  font-style: normal;
}

:deep(.ql-snow .ql-tooltip) {
  z-index: 9999;
}

/* 图片样式 */
:deep(.ql-editor img) {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 10px auto;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s ease;
}

:deep(.ql-editor img:hover) {
  transform: scale(1.02);
}

/* 表格样式 */
:deep(.ql-editor table) {
  border-collapse: collapse;
  width: 100%;
  margin: 10px 0;
}

:deep(.ql-editor table td, .ql-editor table th) {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

:deep(.ql-editor table th) {
  background-color: #f5f5f5;
  font-weight: bold;
}

/* 代码块样式 */
:deep(.ql-editor pre.ql-syntax) {
  background-color: #f5f5f5;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
  margin: 10px 0;
  overflow-x: auto;
  font-family: 'Courier New', Courier, monospace;
}

/* 引用样式 */
:deep(.ql-editor blockquote) {
  border-left: 4px solid #409eff;
  padding-left: 16px;
  margin: 10px 0;
  color: #666;
  background-color: #f9f9f9;
  padding: 10px 16px;
  border-radius: 4px;
}

/* 链接样式 */
:deep(.ql-editor a) {
  color: #409eff;
  text-decoration: none;
}

:deep(.ql-editor a:hover) {
  text-decoration: underline;
}
</style>
