<template>
  <div class="article-detail bg-gray-50 min-h-screen">
    <!-- 顶部导航栏 - 固定在顶部 -->
    <header class="bg-white shadow-sm fixed top-0 left-0 right-0 z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- 顶部标题栏 -->
        <div class="flex items-center justify-between py-4 border-b border-gray-200">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-red-600 rounded-full flex items-center justify-center mr-4">
              <span class="text-white font-bold text-lg">政</span>
            </div>
            <div>
              <h1 class="text-2xl font-bold text-gray-900">信息平台</h1>
              <p class="text-sm text-gray-600">Government Information Platform</p>
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <el-button type="primary" plain icon="ArrowLeft" @click="goBack" class="mr-4">
              返回
            </el-button>
          </div>
        </div>

        <!-- 主导航栏 -->
        <nav class="bg-slate-700">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center h-12">
              <router-link to="/cms" class="flex items-center px-6 py-3 text-white hover:bg-slate-600 transition-colors">
                <el-icon class="mr-2">
                  <House />
                </el-icon>
                首页
              </router-link>
            </div>
          </div>
        </nav>
      </div>
    </header>

    <!-- 内容区域 - 添加顶部间距避免被固定导航遮挡 -->
    <div class="pt-32 pb-20">
      <!-- 面包屑导航 - 固定 -->
      <div class="bg-white border-b border-gray-200 sticky top-32 z-30">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item>
              <router-link to="/cms" class="text-blue-600 hover:text-blue-800">
                首页
              </router-link>
            </el-breadcrumb-item>
            <el-breadcrumb-item v-if="article.category">
              <router-link :to="`/cms/category/${article.category.id}`" class="text-blue-600 hover:text-blue-800">
                {{ article.category.name }}
              </router-link>
            </el-breadcrumb-item>
            <el-breadcrumb-item class="text-gray-500">
              文章详情
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 文章标题 - 固定 -->
        <div class="mb-8 flex-shrink-0">
          <h1 v-if="article.title" class="text-3xl font-bold text-gray-900 mb-2">{{ article.title }}</h1>
          <div v-if="article.category" class="flex items-center space-x-4 text-sm text-gray-600">
            <el-tag type="primary" size="small">{{ article.category.name }}</el-tag>
            <span class="flex items-center">
              <el-icon class="mr-1">
                <Calendar />
              </el-icon>
              {{ formatDate(article.published_at || article.created_at) }}
            </span>
            <span class="flex items-center">
              <el-icon class="mr-1">
                <View />
              </el-icon>
              {{ article.views || 0 }} 次浏览
            </span>
            <span v-if="article.author" class="flex items-center">
              <el-icon class="mr-1">
                <User />
              </el-icon>
              {{ article.author }}
            </span>
          </div>
        </div>

        <!-- 文章内容容器 - 可滚动 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden flex-1 flex flex-col min-h-0">
          <!-- 文章内容头部 - 固定 -->
          <div class="border-b-2 border-blue-500 px-6 py-4 bg-gray-50 flex-shrink-0">
            <h2 class="text-xl font-bold text-gray-900 flex items-center">
              <span class="w-1 h-6 bg-blue-500 mr-3"></span>
              文章内容
            </h2>
          </div>

          <!-- 加载状态 -->
          <div v-if="loading" class="flex-1 flex items-center justify-center">
            <div class="text-center">
              <el-icon class="animate-spin text-4xl text-blue-600 mb-4">
                <Loading />
              </el-icon>
              <p class="text-gray-600">加载中...</p>
            </div>
          </div>

          <!-- 文章内容 - 可滚动区域 -->
          <div v-else-if="article" class="flex-1 overflow-y-auto">
            <div class="p-6">
              <!-- 文章摘要 -->
              <div v-if="article.summary" class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-6">
                <p class="text-blue-800 font-medium mb-2">摘要</p>
                <p class="text-blue-700">{{ article.summary }}</p>
              </div>

              <!-- 富文本内容 -->
              <div class="prose prose-lg max-w-none article-content" v-html="article.content"></div>
            </div>
          </div>

          <!-- 文章操作栏 - 固定在底部 -->
          <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex-shrink-0">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-4">
                <el-button type="primary" plain size="small" @click="goBack">
                  <el-icon class="mr-1">
                    <ArrowLeft />
                  </el-icon>
                  返回列表
                </el-button>
                <el-button type="default" plain size="small" @click="shareArticle">
                  <el-icon class="mr-1">
                    <Share />
                  </el-icon>
                  分享
                </el-button>
                <el-button type="default" plain size="small" @click="printArticle">
                  <el-icon class="mr-1">
                    <Printer />
                  </el-icon>
                  打印
                </el-button>
              </div>
              <div class="text-sm text-gray-500">
                最后更新：{{ formatDate(article.updated_at) }}
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>

    <!-- 页脚 - 固定在底部 -->
    <footer class="bg-gray-800 text-white fixed bottom-0 left-0 right-0 z-40">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div class="text-center">
          <p class="text-gray-300 text-sm">© 2025 信息平台. 保留所有权利.</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getArticleDetail } from '@/api/cms'
import { formatBeijingTime } from '@/utils/timezone'
import {
  ArrowLeft,
  Calendar,
  View,
  User,
  Share,
  Printer,
  Loading,
  House
} from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()
const article = ref({})
const loading = ref(false)

// 获取文章详情
const fetchArticle = async () => {
  loading.value = true
  try {
    const articleId = route.params.id
    const response = await getArticleDetail(articleId)

    // 处理API响应
    if (response && typeof response === 'object') {
      article.value = response
      console.log('获取文章详情成功:', article.value)
    } else {
      throw new Error('文章数据格式错误')
    }
  } catch (error) {
    console.error('获取文章详情失败:', error)
    ElMessage.error('获取文章详情失败')
    // 使用模拟数据作为降级
    loadMockArticle()
  } finally {
    loading.value = false
  }
}

// 模拟数据降级
const loadMockArticle = () => {
  const mockArticles = {
    1: {
      id: 1,
      title: '关于进一步优化营商环境的实施意见',
      summary: '为深入贯彻落实党中央、国务院关于优化营商环境的决策部署，进一步激发市场主体活力，推动经济高质量发展，现提出以下实施意见。',
      content: `
          <h2>一、总体要求</h2>
          <p>以习近平新时代中国特色社会主义思想为指导，全面贯彻党的二十大精神，坚持稳中求进工作总基调，完整、准确、全面贯彻新发展理念，加快构建新发展格局，着力推动高质量发展。</p>

          <h2>二、主要目标</h2>
          <p>到2025年，营商环境便利度和竞争力显著提升，市场主体活力充分激发，形成统一开放、竞争有序的现代市场体系。</p>

          <h3>具体指标：</h3>
          <ul>
            <li>企业开办时间压缩至1个工作日以内</li>
            <li>工程建设项目审批时间压缩至60个工作日以内</li>
            <li>不动产登记时间压缩至3个工作日以内</li>
          </ul>

          <h2>三、重点任务</h2>
          <h3>（一）深化行政审批制度改革</h3>
          <p>持续推进简政放权，进一步精简行政许可事项，优化审批流程，提高审批效率。</p>

          <h3>（二）优化服务</h3>
          <p>推进服务标准化、规范化、便民化，实现更多事项"一网通办"、"最多跑一次"。</p>

          <h3>（三）加强事中事后监管</h3>
          <p>建立健全以信用为基础的新型监管机制，推进"双随机、一公开"监管全覆盖。</p>

          <h2>四、保障措施</h2>
          <p>各级政府要高度重视营商环境优化工作，建立健全工作机制，确保各项措施落实到位。</p>
        `,
      author: '市政府办公室',
      category_name: '政策法规',
      views: 1250,
      created_at: '2025-01-15',
      updated_at: '2025-01-15'
    },
    2: {
      id: 2,
      title: '数字政府建设指导意见',
      summary: '加快推进数字政府建设，提升政府治理体系和治理能力现代化水平，更好服务人民群众和市场主体。',
      content: `
          <h2>数字政府建设的重要意义</h2>
          <p>数字政府建设是推进国家治理体系和治理能力现代化的重要举措，是建设网络强国、数字中国的基础性和先导性工程。</p>

          <h2>建设目标</h2>
          <p>到2025年，与政府治理能力现代化相适应的数字政府顶层设计更加完善、统筹协调机制更加健全。</p>
        `,
      author: '数字政府建设办公室',
      category_name: '政策法规',
      views: 980,
      created_at: '2025-01-12',
      updated_at: '2025-01-12'
    }
  }

  const articleId = route.params.id
  article.value = mockArticles[articleId] || mockArticles[1]
}

// 监听路由变化
watch(() => route.params.id, (newId) => {
  if (newId) {
    fetchArticle()
  }
}, { immediate: true })

// 生命周期
onMounted(() => {
  console.log('ArticleDetail组件已挂载，文章ID:', route.params.id)
})

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const beijingTime = formatBeijingTime(dateString, 'date')
  if (beijingTime) {
    return beijingTime
  }
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const goBack = () => {
  // 优先返回到分类页面
  if (article.value.category && article.value.category.id) {
    router.push(`/cms/category/${article.value.category.id}`)
  } else {
    router.back()
  }
}

const shareArticle = () => {
  if (navigator.share) {
    navigator.share({
      title: article.value.title,
      text: article.value.summary,
      url: window.location.href
    })
  } else {
    // 复制链接到剪贴板
    navigator.clipboard.writeText(window.location.href).then(() => {
      ElMessage.success('链接已复制到剪贴板')
    })
  }
}

const printArticle = () => {
  window.print()
}


</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 文章内容样式 */
.article-content {
  line-height: 1.8;
  color: #333;
}

.article-content h1,
.article-content h2,
.article-content h3,
.article-content h4,
.article-content h5,
.article-content h6 {
  margin: 1.5em 0 0.8em 0;
  font-weight: 600;
  color: #1f2937;
}

.article-content h1 {
  font-size: 1.8em;
  border-bottom: 2px solid #3b82f6;
  padding-bottom: 0.5em;
}

.article-content h2 {
  font-size: 1.5em;
  border-left: 4px solid #3b82f6;
  padding-left: 1em;
}

.article-content h3 {
  font-size: 1.3em;
  color: #374151;
}

.article-content p {
  margin: 1em 0;
  text-align: justify;
}

.article-content ul,
.article-content ol {
  margin: 1em 0;
  padding-left: 2em;
}

.article-content li {
  margin: 0.5em 0;
}

.article-content img {
  max-width: 100%;
  height: auto;
  margin: 1em 0;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.article-content a {
  color: #3b82f6;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: border-color 0.2s;
}

.article-content a:hover {
  border-bottom-color: #3b82f6;
}

.article-content blockquote {
  border-left: 4px solid #e5e7eb;
  margin: 1.5em 0;
  padding: 1em 1.5em;
  background-color: #f9fafb;
  font-style: italic;
}

.article-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5em 0;
}

.article-content th,
.article-content td {
  border: 1px solid #e5e7eb;
  padding: 0.75em;
  text-align: left;
}

.article-content th {
  background-color: #f3f4f6;
  font-weight: 600;
}

.article-content code {
  background-color: #f3f4f6;
  padding: 0.2em 0.4em;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}

.article-content pre {
  background-color: #1f2937;
  color: #f9fafb;
  padding: 1em;
  border-radius: 8px;
  overflow-x: auto;
  margin: 1.5em 0;
}

.article-content pre code {
  background: none;
  padding: 0;
  color: inherit;
}

.article-detail {
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

/* 富文本内容样式 */
.prose {
  color: #374151;
  line-height: 1.75;
}

.prose h2 {
  color: #1f2937;
  font-weight: 700;
  font-size: 1.5em;
  margin-top: 2em;
  margin-bottom: 1em;
  line-height: 1.3333333;
}

.prose h3 {
  color: #1f2937;
  font-weight: 600;
  font-size: 1.25em;
  margin-top: 1.6em;
  margin-bottom: 0.6em;
  line-height: 1.6;
}

.prose p {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}

.prose ul {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-left: 1.625em;
}

.prose li {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

@media print {
  .article-detail header,
  .article-detail footer,
  .article-detail nav {
    display: none !important;
  }
}
</style>
