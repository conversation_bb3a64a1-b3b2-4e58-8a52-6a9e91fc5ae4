import request from '@/utils/request'

// Banner管理相关API接口

/**
 * 获取横幅列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @returns {Promise}
 */
export function getBannerList(params = {}) {
  return request.get('/admin/banners', { params })
}

/**
 * 获取横幅详情
 * @param {number} id - 横幅ID
 * @returns {Promise}
 */
export function getBannerDetail(id) {
  return request.get(`/admin/banners/${id}`)
}

/**
 * 创建横幅
 * @param {FormData} formData - 表单数据
 * @param {File} formData.image - 横幅图片
 * @param {string} formData.link_url - 跳转链接
 * @param {string} formData.description - 描述
 * @param {number} formData.order - 排序
 * @returns {Promise}
 */
export function createBanner(formData) {
  return request.post('/admin/banners', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 更新横幅
 * @param {number} id - 横幅ID
 * @param {FormData} formData - 表单数据
 * @param {string} formData._method - HTTP方法覆盖 (PUT)
 * @param {File} [formData.image] - 横幅图片（可选）
 * @param {string} formData.link_url - 跳转链接
 * @param {string} formData.description - 描述
 * @param {number} formData.order - 排序
 * @returns {Promise}
 */
export function updateBanner(id, formData) {
  // 添加方法覆盖
  formData.append('_method', 'PUT')
  
  return request.post(`/admin/banners/${id}`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 删除横幅
 * @param {number} id - 横幅ID
 * @returns {Promise}
 */
export function deleteBanner(id) {
  return request.delete(`/admin/banners/${id}`)
}

/**
 * 批量删除横幅
 * @param {Array<number>} ids - 横幅ID数组
 * @returns {Promise}
 */
export function batchDeleteBanners(ids) {
  return request.post('/admin/banners/batch-delete', { ids })
}

/**
 * 更新横幅排序
 * @param {Array} banners - 横幅排序数据
 * @param {number} banners[].id - 横幅ID
 * @param {number} banners[].order - 新排序
 * @returns {Promise}
 */
export function updateBannerOrder(banners) {
  return request.post('/admin/banners/update-order', { banners })
}
