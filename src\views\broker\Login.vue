<template>
  <div class="flex items-center justify-center min-h-screen bg-gray-100 p-4">
    <div class="bg-white p-6 rounded-lg shadow-md w-full max-w-md">
      <h2 class="text-2xl font-bold mb-4 text-center">经纪人登录</h2>
      <el-form :model="loginForm" :rules="loginRules" ref="loginFormRef" class="space-y-4">
        <el-form-item prop="phone">
          <el-input
            v-model="loginForm.phone"
            placeholder="手机号"
            prefix-icon="el-icon-phone"
          ></el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            placeholder="密码"
            show-password
            prefix-icon="el-icon-lock"
          ></el-input>
        </el-form-item>
        <el-button type="primary" class="w-full" @click="handleLogin">登录</el-button>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const loginForm = ref({ phone: '', password: '' })
const loginFormRef = ref(null)
const loginRules = {
  phone: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
}

const handleLogin = () => {
  loginFormRef.value.validate((valid) => {
    if (valid) {
      // 登录逻辑
      console.log('登录信息:', loginForm.value)
      // 模拟首次登录强制修改密码
      const isFirstLogin = true // 假设是首次登录
      if (isFirstLogin) {
        router.push('/broker/change-password')
      } else {
        router.push('/broker/training-plans')
      }
    }
  })
}
</script>

<style scoped>
/* 登录页面特定样式 */
.el-icon-phone:before {
  content: '\e6d2';
}
.el-icon-lock:before {
  content: '\e6da';
}
</style>
