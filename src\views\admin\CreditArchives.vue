<template>
  <div class="bg-white p-6 rounded-lg shadow-md">
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-2xl font-bold">信用档案管理</h2>
      <el-button type="primary" @click="handleAdd" :icon="Plus">添加档案</el-button>
    </div>

    <!-- 搜索和筛选 -->
    <div class="mb-4 flex gap-4 items-center">
      <el-input v-model="searchKeyword" placeholder="搜索标题或内容" style="width: 300px" clearable @clear="handleSearch" @keyup.enter="handleSearch">
        <template #append>
          <el-button @click="handleSearch" :icon="Search">搜索</el-button>
        </template>
      </el-input>
      <el-select v-model="filterEntityType" placeholder="主体类型" clearable @change="handleSearch" style="width: 120px">
        <el-option label="全部" value=""></el-option>
        <el-option label="企业" value="agency"></el-option>
        <el-option label="个人" value="broker"></el-option>
      </el-select>
      <el-select v-model="filterType" placeholder="档案类型" clearable @change="handleSearch" style="width: 120px">
        <el-option label="全部" value=""></el-option>
        <el-option label="红榜" value="red"></el-option>
        <el-option label="黑榜" value="black"></el-option>
      </el-select>
    </div>

    <!-- 数据表格 -->
    <el-table :data="archives" border style="width: 100%" v-loading="loading" empty-text="暂无数据">
      <el-table-column prop="id" label="ID" width="80" align="center"></el-table-column>
      <el-table-column label="主体类型" min-width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="row.entity_type === 'agency' ? 'primary' : 'success'">
            {{ getEntityTypeLabel(row.entity_type) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="主体名称" min-width="150" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ getEntityName(row) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="档案类型" min-width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="row.type === 'red' ? 'success' : 'danger'">
            {{ getArchiveTypeLabel(row.type) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="标题" min-width="120" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="title-cell">{{ row.title }}</div>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" min-width="160" align="center">
        <template #default="{ row }">
          <span>{{ formatDate(row.created_at) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="200" align="center" fixed="right">
        <template #default="{ row }">
          <el-button size="small" @click="handleView(row)" :icon="View">查看</el-button>
          <el-button size="small" type="primary" @click="handleEdit(row)" :icon="Edit">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDelete(row)" :icon="Delete">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="flex justify-center mt-6">
      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]" :total="total"
        layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange" @current-change="handlePageChange" />
    </div>
    <!-- 添加/编辑对话框 -->
    <el-dialog v-model="dialogVisible" :title="form.id ? '编辑信用档案' : '添加信用档案'" width="60%" :close-on-click-modal="false">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px" v-loading="formLoading">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="主体类型" prop="entity_type">
              <el-select v-model="form.entity_type" placeholder="请选择主体类型" @change="handleEntityTypeChange" style="width: 100%">
                <el-option label="企业" value="agency"></el-option>
                <el-option label="个人" value="broker"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="档案类型" prop="type">
              <el-select v-model="form.type" placeholder="请选择档案类型" style="width: 100%">
                <el-option label="红榜" value="red"></el-option>
                <el-option label="黑榜" value="black"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="主体" prop="entity_id">
          <el-select v-model="form.entity_id" placeholder="请选择主体" :disabled="!form.entity_type" filterable style="width: 100%" :loading="entitiesLoading">
            <el-option v-for="item in entities" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入标题"></el-input>
        </el-form-item>
        <el-form-item label="内容" prop="content">
          <AdvancedRichTextEditor v-model="form.content" placeholder="请输入内容，支持富文本格式、图片上传和Word粘贴" height="400px" upload-mode="base64" />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            {{ form.id ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog v-model="viewDialogVisible" title="信用档案详情" width="50%">
      <div v-if="viewData" class="space-y-4">
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="font-semibold text-gray-700">主体类型：</label>
            <span>{{ getEntityTypeLabel(viewData.entity_type) }}</span>
          </div>
          <div>
            <label class="font-semibold text-gray-700">档案类型：</label>
            <el-tag :type="viewData.type === 'red' ? 'success' : 'danger'">
              {{ getArchiveTypeLabel(viewData.type) }}
            </el-tag>
          </div>
          <div>
            <label class="font-semibold text-gray-700">主体名称：</label>
            <span>{{ getEntityName(viewData) }}</span>
          </div>
          <div>
            <label class="font-semibold text-gray-700">创建时间：</label>
            <span>{{ formatDate(viewData.created_at) }}</span>
          </div>
        </div>
        <div>
          <label class="font-semibold text-gray-700">标题：</label>
          <p class="mt-1">{{ viewData.title }}</p>
        </div>
        <div>
          <label class="font-semibold text-gray-700">内容：</label>
          <div class="mt-1 p-3 bg-gray-50 rounded border rich-content" v-html="viewData.content"></div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="viewDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, View, Edit, Delete } from '@element-plus/icons-vue'
import {
  getCreditArchiveList,
  getCreditArchiveDetail,
  createCreditArchive,
  updateCreditArchive,
  deleteCreditArchive,
  getAgencyOptions,
  getBrokerOptions,
  ENTITY_TYPES,
  ARCHIVE_TYPES
} from '@/api/admin/creditArchives'
import AdvancedRichTextEditor from '@/components/AdvancedRichTextEditor.vue'

// 响应式数据
const loading = ref(false)
const formLoading = ref(false)
const submitLoading = ref(false)
const entitiesLoading = ref(false)

const archives = ref([])
const entities = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 搜索和筛选
const searchKeyword = ref('')
const filterEntityType = ref('')
const filterType = ref('')

// 对话框
const dialogVisible = ref(false)
const viewDialogVisible = ref(false)
const viewData = ref(null)

// 表单
const form = ref({
  id: null,
  entity_type: '',
  entity_id: null,
  type: '',
  title: '',
  content: ''
})
const formRef = ref(null)

// 表单验证规则
const rules = {
  entity_type: [{ required: true, message: '请选择主体类型', trigger: 'change' }],
  entity_id: [{ required: true, message: '请选择主体', trigger: 'change' }],
  type: [{ required: true, message: '请选择档案类型', trigger: 'change' }],
  title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
  content: [{ required: true, message: '请输入内容', trigger: 'blur' }],
}

// 存储实体名称缓存
const entityNameCache = ref(new Map())

// 工具方法
const getEntityTypeLabel = (type) => ENTITY_TYPES[type] || type
const getArchiveTypeLabel = (type) => ARCHIVE_TYPES[type] || type

const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

// 获取实体名称
const getEntityName = (row) => {
  const cacheKey = `${row.entity_type}_${row.entity_id}`
  return entityNameCache.value.get(cacheKey) || `${getEntityTypeLabel(row.entity_type)}${row.entity_id}`
}

// 批量获取实体名称
const fetchEntityNames = async (archives) => {
  const agencyIds = []
  const brokerIds = []

  // 收集需要获取名称的ID
  archives.forEach(archive => {
    const cacheKey = `${archive.entity_type}_${archive.entity_id}`
    if (!entityNameCache.value.has(cacheKey)) {
      if (archive.entity_type === 'agency') {
        agencyIds.push(archive.entity_id)
      } else if (archive.entity_type === 'broker') {
        brokerIds.push(archive.entity_id)
      }
    }
  })

  try {
    // 获取机构名称
    if (agencyIds.length > 0) {
      const agencyResponse = await getAgencyOptions()
      console.log('机构响应:', agencyResponse)
      // 注意：response已经是response.data（由axios拦截器处理）
      const agencies = agencyResponse?.data || agencyResponse || []
      agencies.forEach(agency => {
        const cacheKey = `agency_${agency.id}`
        entityNameCache.value.set(cacheKey, agency.name || agency.company_name || `机构${agency.id}`)
      })
    }

    // 获取经纪人名称
    if (brokerIds.length > 0) {
      const brokerResponse = await getBrokerOptions()
      console.log('经纪人响应:', brokerResponse)
      // 注意：response已经是response.data（由axios拦截器处理）
      const brokers = brokerResponse?.data || brokerResponse || []
      brokers.forEach(broker => {
        const cacheKey = `broker_${broker.id}`
        entityNameCache.value.set(cacheKey, broker.name || `经纪人${broker.id}`)
      })
    }
  } catch (error) {
    console.error('获取实体名称失败:', error)
  }
}

// 获取信用档案列表
const fetchArchives = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      per_page: pageSize.value
    }

    // 添加搜索和筛选条件
    if (searchKeyword.value) {
      params.keyword = searchKeyword.value
    }
    if (filterEntityType.value) {
      params.entity_type = filterEntityType.value
    }
    if (filterType.value) {
      params.type = filterType.value
    }

    console.log('请求参数:', params)
    const response = await getCreditArchiveList(params)
    console.log('API响应:', response)

    // 处理Laravel分页响应格式
    // 注意：response已经是response.data（由axios拦截器处理）
    if (response && response.data) {
      archives.value = response.data || []
      total.value = response.total || 0
      currentPage.value = response.current_page || 1

      // 获取实体名称
      if (archives.value.length > 0) {
        await fetchEntityNames(archives.value)
      }
    } else {
      archives.value = []
      total.value = 0
    }

    console.log('处理后的数据:', { archives: archives.value, total: total.value })
  } catch (error) {
    console.error('获取信用档案列表失败:', error)
    ElMessage.error('获取信用档案列表失败: ' + (error.message || '未知错误'))
    archives.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 获取主体选项（机构或经纪人）
const fetchEntities = async (entityType) => {
  if (!entityType) {
    entities.value = []
    return
  }

  try {
    entitiesLoading.value = true
    let response

    if (entityType === 'agency') {
      response = await getAgencyOptions()
      console.log('机构API响应:', response)

      // 处理机构数据 - 注意：response已经是response.data（由axios拦截器处理）
      let agencyData = []
      if (response?.data && Array.isArray(response.data)) {
        agencyData = response.data
      } else if (Array.isArray(response)) {
        agencyData = response
      }

      entities.value = agencyData.map(item => ({
        id: item.id,
        name: item.name || item.company_name || `机构${item.id}` // 兼容不同的名称字段
      }))

    } else if (entityType === 'broker') {
      response = await getBrokerOptions()
      console.log('经纪人API响应:', response)

      // 处理经纪人数据 - 注意：response已经是response.data（由axios拦截器处理）
      let brokerData = []
      if (response?.data && Array.isArray(response.data)) {
        brokerData = response.data
      } else if (Array.isArray(response)) {
        brokerData = response
      }

      entities.value = brokerData.map(item => ({
        id: item.id,
        name: item.name || `经纪人${item.id}` // 经纪人通常有name字段
      }))
    }

    console.log(`获取到 ${entities.value.length} 个${entityType === 'agency' ? '机构' : '经纪人'}`)
  } catch (error) {
    console.error('获取主体选项失败:', error)
    ElMessage.error('获取主体选项失败: ' + (error.message || '未知错误'))
    entities.value = []
  } finally {
    entitiesLoading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  fetchArchives()
}

// 分页处理
const handlePageChange = (page) => {
  currentPage.value = page
  fetchArchives()
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  fetchArchives()
}

// 主体类型变化处理
const handleEntityTypeChange = (entityType) => {
  form.value.entity_id = null
  entities.value = []
  if (entityType) {
    fetchEntities(entityType)
  }
}

// 添加档案
const handleAdd = () => {
  form.value = {
    id: null,
    entity_type: '',
    entity_id: null,
    type: '',
    title: '',
    content: ''
  }
  entities.value = []
  dialogVisible.value = true
}

// 编辑档案
const handleEdit = async (row) => {
  try {
    formLoading.value = true
    console.log('编辑档案，行数据:', row)

    const response = await getCreditArchiveDetail(row.id)
    console.log('编辑详情API响应:', response)

    // 处理API响应数据
    // 注意：response已经是response.data（由axios拦截器处理）
    const data = response
    if (!data || !data.id) {
      throw new Error('无效的响应数据')
    }

    form.value = {
      id: data.id,
      entity_type: data.entity_type,
      entity_id: data.entity_id,
      type: data.type,
      title: data.title,
      content: data.content
    }

    console.log('设置的表单数据:', form.value)

    // 加载对应的主体选项
    if (data.entity_type) {
      await fetchEntities(data.entity_type)
    }

    dialogVisible.value = true
  } catch (error) {
    console.error('获取档案详情失败:', error)
    ElMessage.error('获取档案详情失败: ' + (error.message || '未知错误'))
  } finally {
    formLoading.value = false
  }
}

// 查看档案详情
const handleView = async (row) => {
  try {
    console.log('查看详情，行数据:', row)
    const response = await getCreditArchiveDetail(row.id)
    console.log('详情API响应:', response)

    // 处理API响应数据
    // 注意：response已经是response.data（由axios拦截器处理）
    viewData.value = response
    if (!viewData.value || !viewData.value.id) {
      throw new Error('无效的响应数据')
    }

    console.log('设置的详情数据:', viewData.value)
    viewDialogVisible.value = true
  } catch (error) {
    console.error('获取档案详情失败:', error)
    ElMessage.error('获取档案详情失败: ' + (error.message || '未知错误'))
  }
}

// 删除档案
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除档案"${row.title}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await deleteCreditArchive(row.id)
    ElMessage.success('删除成功')
    fetchArchives()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除档案失败:', error)
      ElMessage.error('删除档案失败')
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    submitLoading.value = true

    if (form.value.id) {
      // 更新
      await updateCreditArchive(form.value.id, {
        entity_type: form.value.entity_type,
        entity_id: form.value.entity_id,
        type: form.value.type,
        title: form.value.title,
        content: form.value.content
      })
      ElMessage.success('更新成功')
    } else {
      // 创建
      await createCreditArchive({
        entity_type: form.value.entity_type,
        entity_id: form.value.entity_id,
        type: form.value.type,
        title: form.value.title,
        content: form.value.content
      })
      ElMessage.success('创建成功')
    }

    dialogVisible.value = false
    fetchArchives()
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  } finally {
    submitLoading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  dialogVisible.value = false
  form.value = {
    id: null,
    entity_type: '',
    entity_id: null,
    type: '',
    title: '',
    content: ''
  }
  entities.value = []
}

// 初始化
onMounted(() => {
  fetchArchives()
})
</script>

<style scoped>
/* 信用档案管理特定样式 */
.editor-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
}

/* 标题单元格样式 - 自适应省略号 */
.title-cell {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.5;
}

/* 表格自适应优化 */
:deep(.el-table) {
  font-size: 14px;
}

:deep(.el-table .el-table__cell) {
  padding: 8px 0;
}

/* 操作按钮组优化 */
:deep(.el-table .el-button + .el-button) {
  margin-left: 8px;
}

/* 响应式优化 */
@media (max-width: 1200px) {
  :deep(.el-table .el-button) {
    padding: 5px 8px;
    font-size: 12px;
  }
}

@media (min-width: 1400px) {
  .title-cell {
    max-width: 300px; /* 大屏幕下允许更长的标题显示 */
  }
}

/* 富文本内容显示样式 */
:deep(.rich-content) {
  line-height: 1.6;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
    sans-serif;
}

:deep(.rich-content h1) {
  font-size: 2em;
  font-weight: bold;
  margin: 0.67em 0;
}

:deep(.rich-content h2) {
  font-size: 1.5em;
  font-weight: bold;
  margin: 0.75em 0;
}

:deep(.rich-content h3) {
  font-size: 1.17em;
  font-weight: bold;
  margin: 1em 0;
}

:deep(.rich-content h4) {
  font-size: 1em;
  font-weight: bold;
  margin: 1.33em 0;
}

:deep(.rich-content h5) {
  font-size: 0.83em;
  font-weight: bold;
  margin: 1.67em 0;
}

:deep(.rich-content h6) {
  font-size: 0.67em;
  font-weight: bold;
  margin: 2.33em 0;
}

:deep(.rich-content p) {
  margin: 1em 0;
}

:deep(.rich-content ul, .rich-content ol) {
  margin: 1em 0;
  padding-left: 2em;
}

:deep(.rich-content li) {
  margin: 0.5em 0;
}

:deep(.rich-content blockquote) {
  margin: 1em 0;
  padding: 0.5em 1em;
  border-left: 4px solid #ddd;
  background-color: #f9f9f9;
}

:deep(.rich-content pre) {
  background-color: #f4f4f4;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 1em;
  overflow-x: auto;
  font-family: 'Courier New', Courier, monospace;
}

:deep(.rich-content code) {
  background-color: #f4f4f4;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: 'Courier New', Courier, monospace;
}

:deep(.rich-content img) {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.rich-content a) {
  color: #409eff;
  text-decoration: none;
}

:deep(.rich-content a:hover) {
  text-decoration: underline;
}

:deep(.rich-content table) {
  border-collapse: collapse;
  width: 100%;
  margin: 1em 0;
}

:deep(.rich-content th, .rich-content td) {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

:deep(.rich-content th) {
  background-color: #f2f2f2;
  font-weight: bold;
}
</style>
