/**
 * 时区处理工具函数
 * 统一处理东八区时间
 */

/**
 * 获取东八区当前时间
 * @returns {Date} 东八区时间对象
 */
export function getBeijingTime() {
  const now = new Date()
  // 获取UTC时间
  const utc = now.getTime() + (now.getTimezoneOffset() * 60000)
  // 转换为东八区时间（UTC+8）
  const beijingTime = new Date(utc + (8 * 3600000))
  return beijingTime
}

/**
 * 获取东八区当前时间字符串（用于表单默认值）
 * @param {boolean} includeSeconds 是否包含秒
 * @returns {string} 格式化的时间字符串 YYYY-MM-DD HH:mm:ss
 */
export function getBeijingTimeString(includeSeconds = true) {
  const beijingTime = getBeijingTime()
  const year = beijingTime.getFullYear()
  const month = String(beijingTime.getMonth() + 1).padStart(2, '0')
  const day = String(beijingTime.getDate()).padStart(2, '0')
  const hours = String(beijingTime.getHours()).padStart(2, '0')
  const minutes = String(beijingTime.getMinutes()).padStart(2, '0')
  const seconds = String(beijingTime.getSeconds()).padStart(2, '0')
  
  if (includeSeconds) {
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  } else {
    return `${year}-${month}-${day} ${hours}:${minutes}`
  }
}

/**
 * 将UTC时间字符串转换为东八区时间字符串
 * @param {string} utcTimeString UTC时间字符串
 * @returns {string} 东八区时间字符串
 */
export function utcToBeijingTime(utcTimeString) {
  if (!utcTimeString) return ''
  
  try {
    const utcDate = new Date(utcTimeString)
    // 如果已经是东八区时间，直接返回
    if (utcTimeString.includes('+08:00') || utcTimeString.includes('+0800')) {
      return formatBeijingTime(utcDate)
    }
    
    // 转换为东八区时间
    const beijingTime = new Date(utcDate.getTime() + (8 * 3600000))
    return formatBeijingTime(beijingTime)
  } catch (error) {
    console.error('时间转换错误:', error)
    return utcTimeString
  }
}

/**
 * 将东八区时间转换为UTC时间字符串（用于发送到后台）
 * @param {string} beijingTimeString 东八区时间字符串
 * @returns {string} UTC时间字符串
 */
export function beijingTimeToUtc(beijingTimeString) {
  if (!beijingTimeString) return ''
  
  try {
    // 假设输入的是东八区时间
    const beijingDate = new Date(beijingTimeString)
    // 转换为UTC时间
    const utcTime = new Date(beijingDate.getTime() - (8 * 3600000))
    return utcTime.toISOString()
  } catch (error) {
    console.error('时间转换错误:', error)
    return beijingTimeString
  }
}

/**
 * 格式化东八区时间为显示字符串
 * @param {Date|string} time 时间对象或字符串
 * @param {string} format 格式类型 'datetime' | 'date' | 'time'
 * @returns {string} 格式化的时间字符串
 */
export function formatBeijingTime(time, format = 'datetime') {
  if (!time) return ''
  
  try {
    let date
    if (typeof time === 'string') {
      date = new Date(time)
    } else {
      date = time
    }
    
    // 确保是有效的日期
    if (isNaN(date.getTime())) {
      return ''
    }
    
    const options = {
      timeZone: 'Asia/Shanghai', // 东八区
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    }
    
    if (format === 'datetime' || format === 'time') {
      options.hour = '2-digit'
      options.minute = '2-digit'
      options.second = '2-digit'
      options.hour12 = false
    }
    
    if (format === 'date') {
      return date.toLocaleDateString('zh-CN', options)
    } else if (format === 'time') {
      return date.toLocaleTimeString('zh-CN', options)
    } else {
      return date.toLocaleString('zh-CN', options)
    }
  } catch (error) {
    console.error('时间格式化错误:', error)
    return time.toString()
  }
}

/**
 * 获取相对时间描述（如：刚刚、5分钟前、2小时前等）
 * @param {Date|string} time 时间对象或字符串
 * @returns {string} 相对时间描述
 */
export function getRelativeTime(time) {
  if (!time) return ''
  
  try {
    const date = typeof time === 'string' ? new Date(time) : time
    const now = getBeijingTime()
    const diff = now.getTime() - date.getTime()
    
    const seconds = Math.floor(diff / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)
    const days = Math.floor(hours / 24)
    const months = Math.floor(days / 30)
    const years = Math.floor(days / 365)
    
    if (seconds < 60) {
      return '刚刚'
    } else if (minutes < 60) {
      return `${minutes}分钟前`
    } else if (hours < 24) {
      return `${hours}小时前`
    } else if (days < 30) {
      return `${days}天前`
    } else if (months < 12) {
      return `${months}个月前`
    } else {
      return `${years}年前`
    }
  } catch (error) {
    console.error('相对时间计算错误:', error)
    return ''
  }
}

/**
 * 检查时间字符串是否为今天
 * @param {Date|string} time 时间对象或字符串
 * @returns {boolean} 是否为今天
 */
export function isToday(time) {
  if (!time) return false
  
  try {
    const date = typeof time === 'string' ? new Date(time) : time
    const today = getBeijingTime()
    
    return date.getFullYear() === today.getFullYear() &&
           date.getMonth() === today.getMonth() &&
           date.getDate() === today.getDate()
  } catch (error) {
    console.error('日期比较错误:', error)
    return false
  }
}

/**
 * 获取时间范围的开始和结束时间（东八区）
 * @param {string} range 时间范围 'today' | 'yesterday' | 'week' | 'month'
 * @returns {Object} { start: Date, end: Date }
 */
export function getTimeRange(range) {
  const now = getBeijingTime()
  const start = new Date(now)
  const end = new Date(now)
  
  switch (range) {
    case 'today':
      start.setHours(0, 0, 0, 0)
      end.setHours(23, 59, 59, 999)
      break
    case 'yesterday':
      start.setDate(start.getDate() - 1)
      start.setHours(0, 0, 0, 0)
      end.setDate(end.getDate() - 1)
      end.setHours(23, 59, 59, 999)
      break
    case 'week':
      const dayOfWeek = start.getDay()
      const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1
      start.setDate(start.getDate() - daysToMonday)
      start.setHours(0, 0, 0, 0)
      end.setHours(23, 59, 59, 999)
      break
    case 'month':
      start.setDate(1)
      start.setHours(0, 0, 0, 0)
      end.setMonth(end.getMonth() + 1, 0)
      end.setHours(23, 59, 59, 999)
      break
    default:
      break
  }
  
  return { start, end }
}

/**
 * 将Date对象转换为适合ElementPlus DatePicker的格式
 * @param {Date|string} time 时间对象或字符串
 * @returns {Date} 适合DatePicker的Date对象
 */
export function toDatePickerValue(time) {
  if (!time) return null
  
  try {
    if (typeof time === 'string') {
      // 如果是字符串，假设是东八区时间
      return new Date(time)
    }
    return time
  } catch (error) {
    console.error('DatePicker值转换错误:', error)
    return null
  }
}

/**
 * 将DatePicker的值转换为格式化字符串
 * @param {Date} date DatePicker的Date值
 * @returns {string} 格式化的时间字符串
 */
export function fromDatePickerValue(date) {
  if (!date) return ''
  
  try {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  } catch (error) {
    console.error('DatePicker值转换错误:', error)
    return ''
  }
}
