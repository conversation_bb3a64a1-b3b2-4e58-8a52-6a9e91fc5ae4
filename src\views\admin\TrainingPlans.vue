<template>
  <div class="bg-white p-6 rounded-lg shadow-md">
    <h2 class="text-2xl font-bold mb-4">培训计划管理</h2>
    <el-button type="primary" class="mb-4" @click="handleAdd">添加培训计划</el-button>
    <el-table :data="plans" border style="width: 100%">
      <el-table-column prop="id" label="ID" width="80"></el-table-column>
      <el-table-column prop="title" label="培训计划名称" width="300"></el-table-column>
      <el-table-column prop="price" label="培训费用（元）" width="120"></el-table-column>
      <el-table-column prop="deadline" label="缴费截止时间" width="180"></el-table-column>
      <el-table-column prop="created_at" label="创建时间" width="180"></el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button size="small" @click="handleView(scope.row)">查看</el-button>
          <el-button size="small" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="small" type="success" @click="handleCourses(scope.row)"
            >关联课程</el-button
          >
          <el-button size="small" type="warning" @click="handleAssign(scope.row)"
            >下发计划</el-button
          >
          <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="currentPage"
      :page-size="pageSize"
      :total="total"
      layout="total, prev, pager, next"
      class="mt-4"
      @current-change="handlePageChange"
    />
    <el-dialog v-model="dialogVisible" title="添加/编辑培训计划" width="50%">
      <el-form :model="form" :rules="rules" ref="formRef">
        <el-form-item label="培训计划名称" prop="title">
          <el-input v-model="form.title" placeholder="请输入培训计划名称"></el-input>
        </el-form-item>
        <el-form-item label="培训费用（元）" prop="price">
          <el-input-number
            v-model="form.price"
            placeholder="请输入培训费用"
            :min="0"
            :precision="2"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="缴费截止时间" prop="deadline">
          <el-date-picker
            v-model="form.deadline"
            type="datetime"
            placeholder="请选择缴费截止时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          ></el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
    <el-dialog v-model="coursesDialogVisible" title="关联课程" width="50%">
      <el-form :model="coursesForm" ref="coursesFormRef">
        <el-form-item label="选择课程">
          <el-checkbox-group v-model="coursesForm.selectedCourses">
            <el-checkbox v-for="course in allCourses" :key="course.id" :label="course.id">{{
              course.title
            }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="coursesDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleCoursesSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const plans = ref([])
const allCourses = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const dialogVisible = ref(false)
const coursesDialogVisible = ref(false)
const form = ref({ id: null, title: '', price: 0, deadline: '' })
const coursesForm = ref({ id: null, selectedCourses: [] })
const formRef = ref(null)
const coursesFormRef = ref(null)
const rules = {
  title: [{ required: true, message: '请输入培训计划名称', trigger: 'blur' }],
  price: [{ required: true, message: '请输入培训费用', trigger: 'blur' }],
  deadline: [{ required: true, message: '请选择缴费截止时间', trigger: 'change' }],
}

onMounted(() => {
  fetchPlans()
  fetchAllCourses()
})

const fetchPlans = () => {
  // 模拟数据
  plans.value = [
    {
      id: 1,
      title: '2023年度房产中介培训计划',
      price: 500.0,
      deadline: '2023-12-31 23:59:59',
      created_at: '2023-01-01 10:00:00',
    },
    {
      id: 2,
      title: '2023年度房产中介进阶培训',
      price: 800.0,
      deadline: '2023-11-30 23:59:59',
      created_at: '2023-02-01 10:00:00',
    },
    // 更多数据...
  ]
  total.value = plans.value.length
}

const fetchAllCourses = () => {
  // 模拟数据
  allCourses.value = [
    { id: 1, title: '房产中介基础课程' },
    { id: 2, title: '房产中介进阶课程' },
    // 更多数据...
  ]
}

const handleAdd = () => {
  form.value = { id: null, title: '', price: 0, deadline: '' }
  dialogVisible.value = true
}

const handleEdit = (row) => {
  form.value = { ...row }
  dialogVisible.value = true
}

const handleView = (row) => {
  // 查看逻辑
  console.log('查看培训计划:', row)
}

const handleCourses = (row) => {
  coursesForm.value = { id: row.id, selectedCourses: [] }
  coursesDialogVisible.value = true
}

const handleAssign = (row) => {
  // 下发计划逻辑
  console.log('下发培训计划:', row)
}

const handleDelete = (row) => {
  // 删除逻辑
  console.log('删除培训计划:', row)
}

const handleSubmit = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      // 提交逻辑
      console.log('提交培训计划:', form.value)
      dialogVisible.value = false
    }
  })
}

const handleCoursesSubmit = () => {
  // 提交关联课程逻辑
  console.log('提交关联课程:', coursesForm.value)
  coursesDialogVisible.value = false
}

const handlePageChange = (page) => {
  currentPage.value = page
  fetchPlans()
}
</script>

<style scoped>
/* 培训计划管理特定样式 */
</style>
