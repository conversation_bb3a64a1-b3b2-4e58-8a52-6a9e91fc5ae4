import request from '@/utils/request'

/**
 * CMS端Banner相关API接口
 */

/**
 * 获取前台横幅列表
 * 获取所有横幅，按排序显示，无需登录权限
 * @returns {Promise}
 */
export function getCmsBanners() {
  return request.get('/cms/banners')
}

/**
 * 处理Banner图片URL
 * 如果是相对路径，则拼接完整的服务器地址
 * @param {string} imageUrl - 图片URL
 * @returns {string} 完整的图片URL
 */
export function formatBannerImageUrl(imageUrl) {
  if (!imageUrl) return ''

  // 如果已经是完整的URL，直接返回
  if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
    return imageUrl
  }

  // 如果是相对路径（如 /storage/banners/xxx.png），由于Vite代理配置，直接返回
  // Vite会自动将 /storage 请求代理到 http://127.0.0.1:8000/storage
  return imageUrl
}

/**
 * 处理Banner数据
 * 格式化图片URL和其他字段
 * @param {Array} banners - 原始banner数据
 * @returns {Array} 处理后的banner数据
 */
export function formatBannersData(banners) {
  if (!Array.isArray(banners)) return []

  return banners.map(banner => ({
    id: banner.id,
    title: banner.description || '信息平台', // 使用description作为title
    description: banner.description || '',
    subtitle: banner.description || '',
    image: formatBannerImageUrl(banner.image_url),
    image_url: formatBannerImageUrl(banner.image_url),
    link_url: banner.link_url || '#',
    order: banner.order || 0
  }))
}
