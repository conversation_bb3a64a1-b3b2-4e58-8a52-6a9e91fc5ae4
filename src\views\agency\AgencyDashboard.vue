<template>
  <div class="bg-gray-100 min-h-screen">
    <header
      class="bg-blue-600 text-white py-4 px-6 flex justify-between items-center shadow-md fixed top-0 left-0 right-0 z-20 h-16"
    >
      <h1 class="text-2xl font-bold">DFHDemo - 机构管理员</h1>
      <div>
        <LogoutButton />
      </div>
    </header>
    <div class="flex">
      <aside
        class="bg-white w-64 h-screen p-5 shadow-md fixed left-0 top-16 bottom-0 z-10 overflow-y-auto"
      >
        <el-menu
          :default-active="activeMenu"
          class="h-full"
          router
          :unique-opened="true"
          background-color="#f5f7fa"
          text-color="#333"
          active-text-color="#409EFF"
          @select="handleMenu"
        >
          <el-menu-item index="/agency/home" :disabled="isMenuDisabled">首页</el-menu-item>
          <el-menu-item index="/agency/brokers" :disabled="isMenuDisabled">经纪人管理</el-menu-item>
          <el-menu-item index="/agency/profile" :disabled="isMenuDisabled">机构信息</el-menu-item>
          <el-menu-item index="/agency/apply-join" :disabled="!isMenuDisabled"
            >入驻申请</el-menu-item
          >
        </el-menu>
      </aside>
      <main class="flex-1 p-6 overflow-auto ml-64 mt-16" style="height: calc(100vh-4rem)">
        <router-view />
      </main>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, provide, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import LogoutButton from '@/components/LogoutButton.vue'
import service from '@/utils/request'
import { ElMessage } from 'element-plus'

const route = useRoute()
const router = useRouter()
const activeMenu = computed(() => {
  if (route.path.startsWith('/agency/brokers')) return '/agency/brokers'
  if (route.path.startsWith('/agency/profile')) return '/agency/profile'
  if (route.path.startsWith('/agency/apply-join')) return '/agency/apply-join'
  return '/agency'
})

const status = ref('passed')
const loading = ref(false)
const agencyInfo = ref(null)

// 获取机构信息的函数，将其提供给子组件
const fetchAgencyInfo = async () => {
  if (loading.value) return
  loading.value = true
  try {
    const res = await service.get('/agency/info')
    const data = res?.data || res
    status.value = data?.status || 'passed'
    agencyInfo.value = data
    return data
  } catch (e) {
    ElMessage.error('获取机构状态失败')
    return null
  } finally {
    loading.value = false
  }
}

// 提供给子组件的共享状态和方法
provide('agencyInfo', agencyInfo)
provide('fetchAgencyInfo', fetchAgencyInfo)

// 修改isMenuDisabled的计算逻辑
const isMenuDisabled = computed(() => status.value !== 'approved')

// 修改菜单项的禁用逻辑
const handleMenu = (index) => {
  if (isMenuDisabled.value && index !== '/agency/apply-join') {
    ElMessage.warning('请先完成入驻并通过审核后再使用该功能')
    return false
  }
  if (!isMenuDisabled.value && index === '/agency/apply-join') {
    ElMessage.warning('您的机构已通过审核，无需再次申请')
    return false
  }
  router.push(index)
}

// 在挂载时获取一次数据
onMounted(() => {
  fetchAgencyInfo()
})
</script>

<style scoped>
/* 企业管理员特定样式 */
</style>
