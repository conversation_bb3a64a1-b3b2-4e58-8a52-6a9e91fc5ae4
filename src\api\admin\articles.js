import request from '@/utils/request'

/**
 * 文章管理相关API接口
 */

/**
 * 获取文章列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.category_id - 分类ID
 * @param {string} params.keyword - 搜索关键词（标题或作者）
 * @returns {Promise}
 */
export function getArticleList(params = {}) {
  return request.get('/admin/articles', { params })
}

/**
 * 获取文章详情
 * @param {number} id - 文章ID
 * @returns {Promise}
 */
export function getArticleDetail(id) {
  return request.get(`/admin/articles/${id}`)
}

/**
 * 创建文章
 * @param {Object} data - 文章数据
 * @param {string} data.title - 文章标题
 * @param {string} data.content - 文章内容
 * @param {number} data.category_id - 分类ID
 * @param {string} data.author - 作者
 * @param {string} data.summary - 摘要
 * @param {string} data.published_at - 发布时间
 * @returns {Promise}
 */
export function createArticle(data) {
  return request.post('/admin/articles', data)
}

/**
 * 更新文章
 * @param {number} id - 文章ID
 * @param {Object} data - 文章数据
 * @param {string} data.title - 文章标题
 * @param {string} data.content - 文章内容
 * @param {number} data.category_id - 分类ID
 * @param {string} data.author - 作者
 * @param {string} data.summary - 摘要
 * @param {string} data.published_at - 发布时间
 * @returns {Promise}
 */
export function updateArticle(id, data) {
  return request.put(`/admin/articles/${id}`, data)
}

/**
 * 删除文章
 * @param {number} id - 文章ID
 * @returns {Promise}
 */
export function deleteArticle(id) {
  return request.delete(`/admin/articles/${id}`)
}
