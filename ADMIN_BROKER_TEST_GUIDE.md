# 超级管理员经纪人管理功能测试指南

## 测试环境准备

1. 确保后端API服务运行在 `http://127.0.0.1:8000`
2. 确保前端开发服务器运行在 `http://localhost:5174`
3. 准备超级管理员测试账户

## 测试步骤

### 1. 登录测试
1. 访问 `http://localhost:5174`
2. 使用超级管理员账户登录
3. 确保获得有效的认证token
4. 验证跳转到超管首页 `/admin`

### 2. 访问经纪人管理页面
1. 在超管界面侧边栏点击"经纪人管理"
2. 或直接访问 `/admin/brokers`
3. 验证页面正常加载

### 3. 经纪人列表功能测试

#### 3.1 列表加载测试
- **预期结果**: 页面显示经纪人列表表格
- **API可用**: 显示真实数据，包含所有机构的经纪人
- **API不可用**: 显示模拟数据并有错误提示

#### 3.2 数据显示测试
验证表格列：
- ID
- 姓名
- 身份证号
- 手机号
- 证书类型（中文显示：经纪人/助理/培训）
- 证书编号
- 审核状态（标签显示：待审核/已通过/已驳回）
- 所属企业
- 操作按钮

#### 3.3 分页功能测试
- 验证分页组件正常显示
- 测试页码切换功能
- 验证总数显示正确

### 4. 经纪人审核功能测试

#### 4.1 审核通过测试
1. 找到状态为"待审核"的经纪人
2. 点击"审核"按钮
3. 在弹出的确认对话框中选择"通过"
4. **预期结果**: 
   - 显示"审核通过成功"消息
   - 该经纪人状态变为"已通过"（绿色标签）
   - 列表自动刷新

#### 4.2 审核驳回测试
1. 找到状态为"待审核"的经纪人
2. 点击"审核"按钮
3. 在弹出的确认对话框中选择"驳回"
4. 在驳回原因输入框中输入原因（如："资料不完整"）
5. 点击确定
6. **预期结果**:
   - 显示"驳回成功"消息
   - 该经纪人状态变为"已驳回"（红色标签）
   - 列表自动刷新

#### 4.3 审核取消测试
1. 点击"审核"按钮
2. 在确认对话框中点击"取消"或关闭
3. **预期结果**: 无任何操作，对话框关闭

### 5. 经纪人详情功能测试

#### 5.1 详情页面访问
1. 在列表中点击任意经纪人的"查看"按钮
2. **预期结果**: 跳转到经纪人详情页面 `/admin/brokers/{id}`

#### 5.2 详情页面内容验证
验证详情页面显示：
- 经纪人基本信息
- 证书信息
- 所属机构信息
- 审核状态
- 创建和更新时间

#### 5.3 详情页面审核测试
1. 在详情页面，如果经纪人状态为"待审核"
2. 验证显示"审核通过"和"驳回"按钮
3. 测试审核功能（同列表页面审核测试）

### 6. 其他功能按钮测试

#### 6.1 流转记录按钮
1. 点击"流转记录"按钮
2. **预期结果**: 跳转到流转记录页面（功能待实现）

#### 6.2 变更记录按钮
1. 点击"变更记录"按钮
2. **预期结果**: 跳转到变更记录页面（功能待实现）

### 7. 错误处理测试

#### 7.1 网络错误测试
1. 断开网络连接
2. 刷新页面或进行操作
3. **预期结果**: 显示错误提示并使用模拟数据

#### 7.2 认证错误测试
1. 清除浏览器中的token
2. 尝试访问经纪人管理页面
3. **预期结果**: 跳转到登录页面

#### 7.3 权限错误测试
1. 使用非超管账户登录
2. 尝试访问 `/admin/brokers`
3. **预期结果**: 无法访问或显示权限错误

## 测试数据示例

### 模拟经纪人数据
```javascript
{
  id: 1,
  name: "张三",
  id_card: "110101199001011234",
  phone: "13800138000",
  certificate_type: "broker",
  certificate_number: "CERT001",
  status: "pending",
  agency_name: "中介公司A"
}
```

### 审核状态说明
- `pending`: 待审核（橙色warning标签）
- `approved`: 已通过（绿色success标签）
- `rejected`: 已驳回（红色danger标签）

## 常见问题排查

### 1. 401未认证错误
- 检查是否正确登录
- 验证token是否有效
- 确认用户角色为super_admin

### 2. 列表加载失败
- 检查后端服务是否运行
- 验证API接口地址是否正确
- 查看浏览器网络面板的错误信息

### 3. 审核操作失败
- 确认经纪人状态为待审核
- 检查网络连接
- 验证API权限

### 4. 页面跳转异常
- 检查路由配置
- 验证组件是否正确导入
- 查看浏览器控制台错误

## 性能测试建议

1. **大数据量测试**: 测试包含大量经纪人的列表加载性能
2. **并发操作测试**: 同时进行多个审核操作
3. **网络延迟测试**: 在慢网络环境下测试用户体验

## 浏览器兼容性测试

建议在以下浏览器中测试：
- Chrome (最新版本)
- Firefox (最新版本)
- Safari (最新版本)
- Edge (最新版本)

---

**测试完成标准**: 所有功能正常工作，错误处理得当，用户体验良好
