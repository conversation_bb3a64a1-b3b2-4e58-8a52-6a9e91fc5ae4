import { createRouter, createWebHashHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: () => import('../views/Home.vue')
    },
    {
      path: '/credit-ranking',
      name: 'CreditRanking',
      component: () => import('../views/CreditRanking.vue')
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('../views/Register.vue')
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('../views/Login.vue'),
    },
    // CMS 公开页面路由
    {
      path: '/cms',
      name: 'cms',
      component: () => import('../views/cms/CmsHome.vue')
    },
    {
      path: '/cms/test-categories',
      name: 'cms-test-categories',
      component: () => import('../views/cms/TestCategories.vue')
    },
    {
      path: '/cms/category/:id',
      name: 'cms-category-articles',
      component: () => import('../views/cms/CategoryArticles.vue')
    },
    {
      path: '/cms/article/:id',
      name: 'cms-article-detail',
      component: () => import('../views/cms/ArticleDetail.vue')
    },
    // 超级管理员路由
    {
      path: '/admin',
      name: 'admin',
      component: () => import('../views/admin/AdminDashboard.vue'),
      children: [
        {
          path: '',  // 空路径表示默认子路由
          name: 'admin-home',
          component: () => import('../views/admin/AdminHome.vue')
        },
        {
          path: 'agencies',
          name: 'admin-agencies',
          component: () => import('../views/admin/Agencies.vue')
        },
        {
          path: 'agencies/:id',
          name: 'admin-agency-detail',
          component: () => import('../views/admin/AgencyDetail.vue')
        },
        {
          path: 'brokers',
          name: 'admin-brokers',
          component: () => import('../views/admin/Brokers.vue')
        },
        {
          path: 'brokers/:id',
          name: 'admin-broker-detail',
          component: () => import('../views/admin/BrokerDetail.vue')
        },
        {
          path: 'users',
          name: 'admin-users',
          component: () => import('../views/admin/Users.vue')
        },
        {
          path: 'article-categories',
          name: 'admin-article-categories',
          component: () => import('../views/admin/ArticleCategories.vue')
        },
        {
          path: 'articles',
          name: 'admin-articles',
          component: () => import('../views/admin/Articles.vue')
        },
        {
          path: 'courses',
          name: 'admin-courses',
          component: () => import('../views/admin/Courses.vue')
        },
        {
          path: 'courses/:id/videos',
          name: 'admin-course-videos',
          component: () => import('../views/admin/CourseVideos.vue')
        },
        {
          path: 'training-plans',
          name: 'admin-training-plans',
          component: () => import('../views/admin/TrainingPlans.vue')
        },
        {
          path: 'credit-archives',
          name: 'admin-credit-archives',
          component: () => import('../views/admin/CreditArchives.vue')
        },
        {
          path: 'banners',
          name: 'admin-banners',
          component: () => import('../views/admin/Banners.vue')
        },
        {
          path: 'orders',
          name: 'admin-orders',
          component: () => import('../views/admin/Orders.vue')
        },
        {
          path: 'statistics',
          name: 'admin-statistics',
          component: () => import('../views/admin/Statistics.vue')
        }
      ]
    },
    // 企业管理员路由
    {
      path: '/agency',
      component: () => import('../views/agency/AgencyDashboard.vue'),
      children: [
        {
          path: '',
          name: 'agency',
          redirect: '/agency/home'
        },
        {
          path: 'home',
          name: 'agency-home',
          component: () => import('../views/agency/AgencyHome.vue')
        },
        {
          path: 'apply-join',
          name: 'agency-apply-join',
          component: () => import('../views/agency/ApplyJoin.vue')
        },
        {
          path: 'profile',
          name: 'agency-profile',
          component: () => import('../views/agency/Profile.vue')
        },
        {
          path: 'brokers',
          name: 'agency-brokers',
          component: () => import('../views/agency/Brokers.vue')
        },
        {
          path: 'credit-archives',
          name: 'agency-credit-archives',
          component: () => import('../views/agency/CreditArchives.vue')
        }
      ]
    },
    // 经纪人前端路由
    {
      path: '/broker',
      name: 'broker',
      component: () => import('../views/broker/BrokerDashboard.vue'),
      children: [
        {
          path: 'login',
          name: 'broker-login',
          component: () => import('../views/broker/Login.vue')
        },
        {
          path: 'change-password',
          name: 'broker-change-password',
          component: () => import('../views/broker/ChangePassword.vue')
        },
        {
          path: 'training-plans',
          name: 'broker-training-plans',
          component: () => import('../views/broker/TrainingPlans.vue')
        },
        {
          path: 'courses',
          name: 'broker-courses',
          component: () => import('../views/broker/Courses.vue')
        },
        {
          path: 'certificates',
          name: 'broker-certificates',
          component: () => import('../views/broker/Certificates.vue')
        },
        {
          path: 'credit-archives',
          name: 'broker-credit-archives',
          component: () => import('../views/broker/CreditArchives.vue')
        }
      ]
    },
    // CMS 管理系统路由 (使用不同的路径避免冲突)
    {
      path: '/cms-admin',
      name: 'cms-admin',
      component: () => import('../views/cms/CMSDashboard.vue'),
      children: [
        {
          path: 'categories',
          name: 'cms-admin-categories',
          component: () => import('../views/cms/Categories.vue')
        },
        {
          path: 'articles',
          name: 'cms-admin-articles',
          component: () => import('../views/cms/Articles.vue')
        },
        {
          path: 'articles/:id',
          name: 'cms-admin-article-detail',
          component: () => import('../views/cms/ArticleDetail.vue')
        },
        {
          path: 'credit-archives',
          name: 'cms-admin-credit-archives',
          component: () => import('../views/cms/CreditArchives.vue')
        },
        {
          path: 'credit-archives/:id',
          name: 'cms-admin-credit-archive-detail',
          component: () => import('../views/cms/CreditArchiveDetail.vue')
        }
      ]
    }
  ]
})

// 白名单：不需要登录就可以访问的路由
const whiteList = ['/login', '/', '/register', '/cms']

router.beforeEach(async (to, from, next) => {
  // 检查是否在白名单中或者是CMS公开页面
  const isWhitelisted = whiteList.includes(to.path) || to.path.startsWith('/cms')

  if (isWhitelisted) {
    next()
    return
  }

  const token = localStorage.getItem('token')
  const user = JSON.parse(localStorage.getItem('user') || '{}')

  // 没有token，重定向到登录页
  if (!token) {
    if (to.path === '/login') {
      next()
    } else {
      next('/login')
    }
    return
  }

  // 已登录但访问登录页，根据角色重定向到对应首页
  if (to.path === '/login') {
    switch (user.role) {
      case 'super_admin':
        next('/admin')
        break
      case 'agency_admin':
        next('/agency')
        break
      case 'broker':
        next('/broker')
        break
      default:
        next('/admin')
    }
    return
  }

  // 其他情况放行
  next()
})

export default router
