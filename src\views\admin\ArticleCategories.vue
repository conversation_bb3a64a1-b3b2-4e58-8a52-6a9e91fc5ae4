<template>
  <div class="bg-white p-6 rounded-lg shadow-md">
    <h2 class="text-2xl font-bold mb-4">文章分类管理</h2>

    <!-- 操作按钮 -->
    <div class="mb-4 flex items-center space-x-2">
      <el-button type="primary" @click="handleAdd">新增分类</el-button>

      <el-button @click="fetchCategories">刷新</el-button>
    </div>

    <!-- 分类列表 -->
    <el-table :data="categories" border style="width: 100%" v-loading="loading">

      <el-table-column prop="id" label="ID" width="80" />

      <el-table-column prop="name" label="分类名称" min-width="200">
        <template #default="{ row }">
          <div class="font-medium">{{ row.name }}</div>
        </template>
      </el-table-column>

      <el-table-column prop="created_at" label="创建时间" width="180">
        <template #default="{ row }">
          {{ formatDate(row.created_at) }}
        </template>
      </el-table-column>

      <el-table-column prop="updated_at" label="更新时间" width="180">
        <template #default="{ row }">
          {{ formatDate(row.updated_at) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <el-button size="small" type="primary" @click="handleEdit(row)">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination v-model:current-page="currentPage" :page-size="pageSize" :total="total" layout="total, prev, pager, next" class="mt-4"
      @current-change="handleCurrentChange" />

    <!-- 新增/编辑对话框 -->
    <el-dialog v-model="dialogVisible" :title="isEdit ? '编辑分类' : '新增分类'" width="500px" @close="resetForm">
      <el-form ref="formRef" :model="form" :rules="formRules" label-width="100px">
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入分类名称" maxlength="50" show-word-limit />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getArticleCategoryList,
  createArticleCategory,
  updateArticleCategory,
  deleteArticleCategory
} from '@/api/admin/articleCategories'
import { formatBeijingTime } from '@/utils/timezone'

// 响应式数据
const loading = ref(false)
const categories = ref([])

const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 对话框相关
const dialogVisible = ref(false)
const isEdit = ref(false)
const submitting = ref(false)
const formRef = ref()

// 表单数据
const form = reactive({
  id: null,
  name: ''
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 2, max: 50, message: '分类名称长度在 2 到 50 个字符', trigger: 'blur' }
  ]
}

// 获取分类列表
const fetchCategories = async () => {
  loading.value = true
  try {
    const response = await getArticleCategoryList({
      page: currentPage.value,
      page_size: pageSize.value
    })

    categories.value = response.data || []
    total.value = response.total || 0
    currentPage.value = response.current_page || 1
  } catch (error) {
    console.error('获取分类列表失败:', error)
    ElMessage.error('获取分类列表失败')

    // 使用模拟数据
    categories.value = [
      {
        id: 1,
        name: '阳光规划',
        created_at: '2025-01-20 10:00:00',
        updated_at: '2025-01-20 10:00:00'
      },
      {
        id: 2,
        name: '政策法规',
        created_at: '2025-01-19 15:30:00',
        updated_at: '2025-01-19 15:30:00'
      },
      {
        id: 3,
        name: '政策解读',
        created_at: '2025-01-18 09:15:00',
        updated_at: '2025-01-18 09:15:00'
      }
    ]
    total.value = 3
  } finally {
    loading.value = false
  }
}

// 处理分页
const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchCategories()
}

// 新增分类
const handleAdd = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 编辑分类
const handleEdit = (row) => {
  resetForm()
  isEdit.value = true
  form.id = row.id
  form.name = row.name
  dialogVisible.value = true
}

// 删除分类
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除分类"${row.name}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteArticleCategory(row.id)
    ElMessage.success('删除成功')
    fetchCategories()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除分类失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    submitting.value = true

    if (isEdit.value) {
      await updateArticleCategory(form.id, {
        name: form.name
      })
      ElMessage.success('更新成功')
    } else {
      await createArticleCategory({
        name: form.name
      })
      ElMessage.success('创建成功')
    }

    dialogVisible.value = false
    fetchCategories()
  } catch (error) {
    console.error('提交失败:', error)

    // 处理表单验证错误
    if (error && typeof error === 'object' && !error.response) {
      const errorMessages = []
      Object.keys(error).forEach(field => {
        if (Array.isArray(error[field]) && error[field].length > 0) {
          errorMessages.push(error[field][0].message)
        }
      })

      if (errorMessages.length > 0) {
        ElMessage.error(errorMessages.join('；'))
      } else {
        ElMessage.error('表单验证失败，请检查输入内容')
      }
    } else {
      ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
    }
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  form.id = null
  form.name = ''

  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  return formatBeijingTime(dateString, 'datetime')
}

// 生命周期
onMounted(() => {
  fetchCategories()
})
</script>

<style scoped>
/* 文章分类管理特定样式 */
</style>
