# 经纪人管理功能实现

## 功能概述

根据提供的API接口，实现了机构管理员端的经纪人列表获取和经纪人添加功能。

## API接口

### 1. 获取经纪人列表

- **接口**: `GET /agency/brokers`
- **描述**: 获取当前机构下的经纪人列表
- **参数**:
  - `page`: 页码
  - `per_page`: 每页数量

### 2. 添加经纪人

- **接口**: `POST /agency/brokers`
- **描述**: 添加新的经纪人
- **参数**:
  - `name`: 姓名
  - `id_card`: 身份证号
  - `phone`: 手机号
  - `certificate_type`: 证书类型 (broker/assistant/training)
  - `certificate_number`: 证书编号
  - `id_card_image`: 身份证照片文件
  - `certificate_image`: 证书照片文件

## 实现的功能

### 1. 经纪人列表展示

- ✅ 表格形式展示经纪人信息
- ✅ 分页功能
- ✅ 显示姓名、身份证号、手机号、证书类型、证书编号、审核状态
- ✅ 操作按钮（编辑、删除）
- ✅ 加载状态指示器
- ✅ 证书类型中英文转换显示

### 2. 添加经纪人

- ✅ 弹窗表单
- ✅ 表单验证
- ✅ 文件上传（身份证照片、证书照片）
- ✅ 支持图片格式限制
- ✅ 文件大小限制（10MB）
- ✅ 提交到后端API

### 3. 错误处理

- ✅ API调用失败时的错误提示
- ✅ 表单验证错误提示
- ✅ 文件上传限制提示
- ✅ 后备模拟数据（当API不可用时）

## 文件结构

```
src/
├── api/
│   └── agency.js          # 经纪人相关API接口
└── views/
    └── agency/
        └── Brokers.vue    # 经纪人管理页面
```

## 使用方法

1. 登录为机构管理员账户
2. 导航到 `/agency/brokers` 页面
3. 查看经纪人列表
4. 点击"添加经纪人"按钮添加新经纪人
5. 填写表单信息并上传相关图片
6. 提交表单

## 技术特点

- 使用Vue 3 Composition API
- Element Plus UI组件库
- 支持文件上传（FormData）
- 响应式设计
- 错误处理和用户反馈
- 分页功能

## 注意事项

1. 需要先登录并获取有效的认证token
2. 文件上传仅支持图片格式
3. 单个文件大小限制为10MB
4. 编辑功能暂未实现（接口文档中未提供更新接口）
5. 删除功能暂时只在前端实现（接口文档中未提供删除接口）

## 后续改进

1. 实现编辑经纪人功能（需要后端提供更新接口）
2. 实现删除经纪人功能（需要后端提供删除接口）
3. 添加搜索和筛选功能
4. 优化文件上传体验（预览、进度条等）
5. 添加批量操作功能
