import request from '@/utils/request'

/**
 * 文章分类管理相关API接口
 */

/**
 * 获取文章分类列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @returns {Promise}
 */
export function getArticleCategoryList(params = {}) {
  return request.get('/admin/article-categories', { params })
}

/**
 * 获取文章分类详情
 * @param {number} id - 分类ID
 * @returns {Promise}
 */
export function getArticleCategoryDetail(id) {
  return request.get(`/admin/article-categories/${id}`)
}

/**
 * 创建文章分类
 * @param {Object} data - 分类数据
 * @param {string} data.name - 分类名称
 * @returns {Promise}
 */
export function createArticleCategory(data) {
  return request.post('/admin/article-categories', data)
}

/**
 * 更新文章分类
 * @param {number} id - 分类ID
 * @param {Object} data - 分类数据
 * @param {string} data.name - 分类名称
 * @returns {Promise}
 */
export function updateArticleCategory(id, data) {
  return request.put(`/admin/article-categories/${id}`, data)
}

/**
 * 删除文章分类
 * @param {number} id - 分类ID
 * @returns {Promise}
 */
export function deleteArticleCategory(id) {
  return request.delete(`/admin/article-categories/${id}`)
}

/**
 * 批量删除文章分类
 * @param {Array<number>} ids - 分类ID数组
 * @returns {Promise}
 */
export function batchDeleteArticleCategories(ids) {
  return request.post('/admin/article-categories/batch-delete', { ids })
}
