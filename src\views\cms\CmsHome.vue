<template>
  <div class="cms-home bg-gray-50 min-h-screen">
    <!-- 顶部导航栏 - 固定在顶部 -->
    <header class="bg-white shadow-sm fixed top-0 left-0 right-0 z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- 顶部标题栏 -->
        <div class="flex items-center justify-between py-4 border-b border-gray-200">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-red-600 rounded-full flex items-center justify-center mr-4">
              <span class="text-white font-bold text-lg">政</span>
            </div>
            <div>
              <h1 class="text-2xl font-bold text-gray-900">信息平台</h1>
              <p class="text-sm text-gray-600">Government Information Platform</p>
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <div class="relative">
              <input type="text" placeholder="请输入关键词"
                class="w-64 px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" v-model="searchKeyword"
                @keyup.enter="handleSearch" />
              <button @click="handleSearch" class="absolute right-0 top-0 h-full px-4 bg-orange-500 text-white rounded-r-md hover:bg-orange-600">
                搜索
              </button>
            </div>
          </div>
        </div>

        <!-- 主导航栏 -->
        <nav class="bg-slate-700">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div ref="navContainerRef" class="flex items-center h-12 overflow-x-auto scrollbar-hide">
              <!-- 首页固定显示 -->
              <a ref="homeNavRef" @click.prevent="goToHome" href="#"
                class="flex items-center px-6 py-3 text-white hover:bg-slate-600 transition-colors whitespace-nowrap">
                <el-icon class="mr-2">
                  <House />
                </el-icon>
                首页
              </a>

              <!-- 动态显示分类 -->
              <template v-if="!loading">
                <!-- 可见分类 -->
                <a v-for="(category, index) in visibleCategories" :key="category.id" :ref="el => setCategoryNavRef(el, index)"
                  @click.prevent="goToCategory(category.id)" href="#"
                  class="flex items-center px-6 py-3 text-white hover:bg-slate-600 transition-colors whitespace-nowrap">
                  {{ category.name }}
                </a>

                <!-- 更多分类下拉菜单 -->
                <el-dropdown v-if="hiddenCategories.length > 0" trigger="click">
                  <a href="#" @click.prevent class="flex items-center px-6 py-3 text-white hover:bg-slate-600 transition-colors whitespace-nowrap">
                    <span>更多</span>
                    <el-icon class="ml-1">
                      <ArrowDown />
                    </el-icon>
                  </a>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item v-for="category in hiddenCategories" :key="category.id" @click="goToCategory(category.id)">
                        {{ category.name }}
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>

              <!-- 加载中占位 -->
              <template v-else>
                <div v-for="i in 3" :key="i" class="flex items-center px-6 py-3 text-white opacity-50 whitespace-nowrap">
                  <el-skeleton style="width: 60px" animated />
                </div>
              </template>

              <!-- 隐藏的"更多"按钮用于测量宽度 -->
              <a ref="moreNavRef"
                class="flex items-center px-6 py-3 text-white hover:bg-slate-600 transition-colors whitespace-nowrap absolute -left-[9999px] opacity-0 pointer-events-none">
                <span>更多</span>
                <el-icon class="ml-1">
                  <ArrowDown />
                </el-icon>
              </a>
            </div>
          </div>
        </nav>
      </div>
    </header>

    <!-- 内容区域 - 添加顶部间距避免被固定导航遮挡 -->
    <div class="pt-32">
      <!-- 轮播横幅 -->
      <section class="bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <el-carousel height="300px" :interval="5000" arrow="hover" indicator-position="inside">
            <el-carousel-item v-for="banner in banners" :key="banner.id">
              <div class="relative w-full h-full cursor-pointer" @click="handleBannerClick(banner)">
                <!-- 背景图片 -->
                <img :src="banner.image_url" :alt="banner.description" class="w-full h-full object-cover" @error="handleImageError" />

                <!-- 左下角文字 -->
                <div class="absolute bottom-0 left-0 text-white text-sm px-4 py-2 bg-black bg-opacity-50 rounded-tr-md">
                  {{ banner.description }}
                </div>
              </div>
            </el-carousel-item>
          </el-carousel>
        </div>
      </section>

      <!-- 主要内容区域 -->
      <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pb-20">
        <!-- 分类内容区域 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <!-- 分类列表 -->
          <div v-for="category in categories" :key="category.id" :id="`category-${category.id}`"
            class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden" style="height: 350px;">
            <!-- 分类标题 -->
            <div class="border-b-2 border-blue-500 px-4 py-3 bg-gray-50 flex-shrink-0">
              <div class="flex items-center justify-between">
                <h2 class="text-lg font-bold text-gray-900 flex items-center">
                  <span class="w-1 h-6 bg-blue-500 mr-3"></span>
                  {{ category.name }}
                </h2>
                <!-- 更多链接移到标题右侧 -->
                <router-link :to="`/cms/category/${category.id}`" class="text-sm text-blue-600 hover:text-blue-800 flex items-center transition-colors">
                  更多
                  <el-icon class="ml-1 text-xs">
                    <ArrowRight />
                  </el-icon>
                </router-link>
              </div>
            </div>

            <!-- 文章列表容器 -->
            <div class="flex flex-col h-full">
              <!-- 文章列表 - 固定高度 -->
              <div class="p-4 flex-1" style="height: 290px;">
                <!-- 有文章时显示文章列表 -->
                <ul v-if="category.articles && category.articles.length > 0" class="space-y-3">
                  <!-- 显示最多5条文章 -->
                  <li v-for="article in category.articles.slice(0, 5)" :key="article.id" @click="goToArticle(article.id)"
                    class="cursor-pointer hover:bg-gray-50 p-2 rounded transition-colors group" style="height: 48px;">
                    <div class="flex items-start justify-between h-full">
                      <div class="flex-1 min-w-0">
                        <div class="flex items-center h-full">
                          <span class="w-1 h-1 bg-blue-500 rounded-full mr-2 flex-shrink-0"></span>
                          <h3 class="text-sm font-medium text-gray-900 group-hover:text-blue-600 truncate">
                            {{ article.title }}
                          </h3>
                        </div>
                      </div>
                      <span class="text-xs text-gray-500 ml-4 flex-shrink-0">
                        {{ formatDate(article.created_at) }}
                      </span>
                    </div>
                  </li>

                  <!-- 如果文章不足5条，用空白占位保持高度一致 -->
                  <li v-for="n in Math.max(0, 5 - category.articles.length)" :key="`placeholder-${n}`" class="p-2" style="height: 48px;">
                    <div class="h-full flex items-center text-gray-400 text-sm">
                      <!-- 空白占位 -->
                    </div>
                  </li>
                </ul>

                <!-- 无文章时显示美观的空状态 -->
                <div v-else class="h-full flex flex-col items-center justify-center">
                  <!-- 背景装饰 -->
                  <div class="relative">
                    <!-- 主图标 -->
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-50 to-blue-100 rounded-full flex items-center justify-center mb-4 relative">
                      <el-icon class="text-2xl text-blue-400">
                        <Document />
                      </el-icon>
                      <!-- 装饰小点 -->
                      <div class="absolute -top-1 -right-1 w-4 h-4 bg-blue-200 rounded-full flex items-center justify-center">
                        <div class="w-2 h-2 bg-blue-400 rounded-full"></div>
                      </div>
                    </div>
                  </div>

                  <!-- 提示文字 -->
                  <div class="text-center mb-4">
                    <h3 class="text-sm font-medium text-gray-600 mb-2">暂无文章</h3>
                    <p class="text-xs text-gray-400 leading-relaxed">
                      {{ category.name }}分类下暂时没有发布文章<br>
                      <span class="text-blue-500">点击右上角"更多"查看详情</span>
                    </p>
                  </div>

                  <!-- 装饰性波浪线 -->
                  <div class="flex items-center space-x-1 opacity-30">
                    <div class="w-8 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
                    <div class="w-1 h-1 bg-gray-300 rounded-full"></div>
                    <div class="w-8 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>
      </main>
    </div>

    <!-- 页脚 - 固定在底部 -->
    <footer class="bg-gray-800 text-white fixed bottom-0 left-0 right-0 z-40">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div class="text-center">
          <p class="text-gray-300 text-sm">© 2025 信息平台. 保留所有权利.</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowRight, ArrowDown, House, Document } from '@element-plus/icons-vue'
import { getCategories, getCategoriesWithArticles, getArticlesByCategory } from '@/api/cms'
import { getCmsBanners, formatBannersData } from '@/api/cms/banners'
import { formatBeijingTime } from '@/utils/timezone'

const router = useRouter()

// 响应式数据
const categories = ref([])
const banners = ref([])
const loading = ref(false)
const searchKeyword = ref('')

// 导航栏分类显示配置
const navContainerRef = ref(null) // 导航容器引用
const homeNavRef = ref(null) // 首页导航项引用
const moreNavRef = ref(null) // 更多导航项引用
const categoryNavRefs = ref([]) // 分类导航项引用数组
const visibleCategoriesCount = ref(5) // 默认显示5个分类

// 计算可见和隐藏的分类
const visibleCategories = computed(() => {
  return categories.value.slice(0, visibleCategoriesCount.value)
})

const hiddenCategories = computed(() => {
  return categories.value.slice(visibleCategoriesCount.value)
})

// 设置分类导航项引用
const setCategoryNavRef = (el, index) => {
  if (el) {
    categoryNavRefs.value[index] = el
  }
}

// 重置分类导航项引用
const resetCategoryNavRefs = () => {
  categoryNavRefs.value = []
}

// 计算导航栏可见分类数量
const calculateVisibleCategories = () => {
  // 使用多重nextTick确保DOM完全渲染
  nextTick(() => {
    nextTick(() => {
      setTimeout(() => {
        if (!navContainerRef.value || !homeNavRef.value || !moreNavRef.value || categories.value.length === 0) {
          console.log('导航栏元素未准备好，跳过计算')
          return
        }

        const containerWidth = navContainerRef.value.clientWidth
        const homeWidth = homeNavRef.value ? homeNavRef.value.offsetWidth : 0
        const moreWidth = moreNavRef.value ? moreNavRef.value.offsetWidth : 0

        console.log('导航栏元素检查:', {
          navContainer: !!navContainerRef.value,
          homeNav: !!homeNavRef.value,
          moreNav: !!moreNavRef.value,
          containerWidth,
          homeWidth,
          moreWidth
        })

        // 检查分类导航项是否已渲染
        const validCategoryRefs = categoryNavRefs.value.filter(ref => ref && ref.offsetWidth > 0)
        console.log(`分类导航项渲染状态: 总数=${categories.value.length}, 有效数=${validCategoryRefs.length}`)
        console.log('分类导航项详情:', categoryNavRefs.value.map((ref, index) => ({
          index,
          exists: !!ref,
          width: ref ? ref.offsetWidth : 0,
          element: ref
        })))

        if (validCategoryRefs.length === 0 || homeWidth === 0) {
          console.log('分类导航项或首页按钮未渲染完成，使用默认值')
          visibleCategoriesCount.value = Math.min(5, categories.value.length)
          return
        }

        // 可用宽度 = 容器宽度 - 首页宽度 - 更多按钮宽度 - 安全边距(40px)
        const availableWidth = containerWidth - homeWidth - moreWidth - 40

        // 获取已渲染分类的宽度作为参考
        const avgCategoryWidth = validCategoryRefs.length > 0
          ? validCategoryRefs.reduce((sum, ref) => sum + ref.offsetWidth, 0) / validCategoryRefs.length
          : 112 // 默认宽度

        // 计算理论上可以显示多少个分类
        const theoreticalCount = Math.floor(availableWidth / avgCategoryWidth)

        // 实际可显示数量不能超过总分类数
        const maxPossibleCount = Math.min(theoreticalCount, categories.value.length)

        // 如果所有分类都能显示，则不需要"更多"按钮
        if (maxPossibleCount >= categories.value.length) {
          visibleCategoriesCount.value = categories.value.length
        } else {
          visibleCategoriesCount.value = Math.max(1, maxPossibleCount) // 至少显示1个分类
        }

        console.log(`导航栏宽度计算详情:`, {
          容器宽度: containerWidth,
          首页宽度: homeWidth,
          更多宽度: moreWidth,
          可用宽度: availableWidth,
          平均分类宽度: avgCategoryWidth,
          理论可显示: theoreticalCount,
          实际显示: visibleCategoriesCount.value,
          分类总数: categories.value.length
        })

        console.log(`导航栏宽度计算: 容器=${containerWidth}px, 首页=${homeWidth}px, 更多=${moreWidth}px, 可用=${availableWidth}px, 分类总数=${categories.value.length}, 可见分类=${visibleCategoriesCount.value}`)
        console.log('平均分类宽度:', avgCategoryWidth)
      }, 100) // 添加100ms延迟确保渲染完成
    })
  })
}

// 防抖函数
const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 创建防抖的计算函数
const debouncedCalculateVisibleCategories = debounce(calculateVisibleCategories, 200)

// 带重试的计算函数
const calculateVisibleCategoriesWithRetry = (retryCount = 0, maxRetries = 3) => {
  calculateVisibleCategories()

  // 如果计算结果不合理且还有重试次数，则重试
  setTimeout(() => {
    if (retryCount < maxRetries) {
      const validCategoryRefs = categoryNavRefs.value.filter(ref => ref && ref.offsetWidth > 0)
      // 只有在明显有问题时才重试：没有任何有效引用，或者首页按钮宽度为0
      const homeWidth = homeNavRef.value ? homeNavRef.value.offsetWidth : 0
      if (validCategoryRefs.length === 0 || homeWidth === 0) {
        console.log(`第${retryCount + 1}次重试计算导航栏宽度，有效引用=${validCategoryRefs.length}，首页宽度=${homeWidth}`)
        calculateVisibleCategoriesWithRetry(retryCount + 1, maxRetries)
      }
    }
  }, 200 * (retryCount + 1)) // 递增延迟时间
}

// 窗口大小变化监听 - 修复自适应问题
const handleResize = () => {
  console.log('窗口大小变化，重新计算导航栏')
  debouncedCalculateVisibleCategories()
}







// 获取数据
const fetchData = async () => {
  loading.value = true

  // 分别获取各类数据，避免一个失败影响其他
  await Promise.allSettled([
    // 获取banner数据
    getCmsBanners().then(response => {
      const rawBanners = response.data || response || []
      banners.value = formatBannersData(rawBanners)
      console.log('Banner数据获取成功:', banners.value)
    }).catch(error => {
      console.error('获取Banner数据失败:', error)
      // 只对banner使用模拟数据
      loadMockBanners()
    }),

    // 获取分类及文章数据
    getCategoriesWithArticles({ per_page: 5 }).then(response => {
      categories.value = response.data || []
      console.log('分类及文章数据获取成功:', categories.value)
      // 重置分类导航项引用
      resetCategoryNavRefs()
      // 计算导航栏可见分类数量 - 多次重试确保计算准确
      calculateVisibleCategoriesWithRetry()
    }).catch(error => {
      console.error('获取分类及文章数据失败:', error)
      // 降级：分别获取分类和文章
      fetchCategoriesAndArticlesSeparately()
    })
  ])

  loading.value = false
}

// 降级处理：分别获取分类和文章
const fetchCategoriesAndArticlesSeparately = async () => {
  try {
    // 先获取分类列表
    const categoriesResponse = await getCategories()
    // API直接返回数组，不是包装在data字段中
    const categoriesList = Array.isArray(categoriesResponse) ? categoriesResponse : (categoriesResponse.data || [])

    // 为每个分类获取前5篇文章
    const categoriesWithArticles = await Promise.allSettled(
      categoriesList.map(async (category) => {
        try {
          const articlesResponse = await getArticlesByCategory(category.id, { page: 1, per_page: 5 })
          // API可能直接返回数组，也可能包装在data字段中
          const articles = Array.isArray(articlesResponse) ? articlesResponse : (articlesResponse.data || [])
          return {
            ...category,
            articles: articles
          }
        } catch (error) {
          console.error(`获取分类 ${category.name} 的文章失败:`, error)
          return {
            ...category,
            articles: []
          }
        }
      })
    )

    // 处理结果
    categories.value = categoriesWithArticles.map(result =>
      result.status === 'fulfilled' ? result.value : result.reason
    ).filter(category => category && category.id)

    console.log('分别获取分类和文章数据成功:', categories.value)
    // 重置分类导航项引用
    resetCategoryNavRefs()
    // 计算导航栏可见分类数量 - 多次重试确保计算准确
    calculateVisibleCategoriesWithRetry()
  } catch (error) {
    console.error('分别获取分类和文章数据失败:', error)
    loadMockCategories()
    console.log('使用模拟分类数据:', categories.value)
    // 重置分类导航项引用
    resetCategoryNavRefs()
    // 计算导航栏可见分类数量 - 多次重试确保计算准确
    calculateVisibleCategoriesWithRetry()
  }
}

// 模拟分类数据
const loadMockCategories = () => {
  categories.value = [
    {
      id: 1,
      name: '阳光规划',
      description: '城市规划公开信息',
      articles: [
        {
          id: 1,
          title: '菏泽市自然资源和规划局关于鲁中车...',
          created_at: '2025-07-17'
        },
        {
          id: 2,
          title: '菏泽市自然资源和规划局关于学院...',
          created_at: '2025-07-15'
        },
        {
          id: 3,
          title: '菏泽市自然资源和规划局关于工山路...',
          created_at: '2025-07-11'
        },
        {
          id: 4,
          title: '《菏泽市西城区单元控制性详细规划...',
          created_at: '2025-07-09'
        },
        {
          id: 5,
          title: '《菏泽市开发区单元控制性详细规划...',
          created_at: '2025-07-09'
        },
        {
          id: 6,
          title: '菏泽市自然资源和规划局关于某某小区建设项目规划许可的公示',
          created_at: '2025-07-05'
        },
        {
          id: 7,
          title: '菏泽市自然资源和规划局关于某某道路建设项目规划许可的公示',
          created_at: '2025-07-03'
        },
        {
          id: 8,
          title: '菏泽市自然资源和规划局关于某某公园建设项目规划许可的公示',
          created_at: '2025-07-01'
        },
        {
          id: 9,
          title: '菏泽市自然资源和规划局关于某某学校建设项目规划许可的公示',
          created_at: '2025-06-28'
        },
        {
          id: 10,
          title: '菏泽市自然资源和规划局关于某某医院建设项目规划许可的公示',
          created_at: '2025-06-25'
        }
      ]
    },
    {
      id: 2,
      name: '政策法规',
      description: '政策法规文件',
      articles: [
        {
          id: 11,
          title: '菏泽市自然资源和规划局等11部门关于进一步加强房地产市场监管的通知',
          created_at: '2025-05-20'
        },
        {
          id: 12,
          title: '菏泽市自然资源和规划局关于印发《菏泽市建设用地审批管理办法》的通知',
          created_at: '2024-08-13'
        },
        {
          id: 13,
          title: '菏泽市自然资源和规划局关于印发《菏泽市土地储备管理办法》的通知',
          created_at: '2024-06-12'
        },
        {
          id: 14,
          title: '菏泽市自然资源和规划局关于印发《菏泽市规划管理技术规定》的通知',
          created_at: '2023-12-20'
        },
        {
          id: 15,
          title: '菏泽市自然资源和规划局关于相关条例实施细则的通知',
          created_at: '2023-07-05'
        },
        {
          id: 16,
          title: '菏泽市自然资源和规划局关于加强城市规划管理的意见',
          created_at: '2023-05-15'
        },
        {
          id: 17,
          title: '菏泽市自然资源和规划局关于优化营商环境的实施方案',
          created_at: '2023-03-10'
        },
        {
          id: 18,
          title: '菏泽市自然资源和规划局关于深化"放管服"改革的通知',
          created_at: '2023-01-20'
        },
        {
          id: 19,
          title: '菏泽市自然资源和规划局关于加强土地利用管理的规定',
          created_at: '2022-12-05'
        },
        {
          id: 20,
          title: '菏泽市自然资源和规划局关于规范建设项目审批流程的通知',
          created_at: '2022-10-15'
        }
      ]
    },
    {
      id: 3,
      name: '政策解读',
      description: '政策解读说明',
      articles: [
        {
          id: 21,
          title: '解读：打好地质灾害"防治战"',
          created_at: '2025-05-21'
        },
        {
          id: 22,
          title: '政策图明问答《关于开展政府老旧小区改造工作的指导意见》',
          created_at: '2024-08-13'
        },
        {
          id: 23,
          title: '政策图明问答《关于开展工业用地供应改革的实施方案》',
          created_at: '2024-06-13'
        },
        {
          id: 24,
          title: '常态黄河入鲁生态安全屏障建设解读',
          created_at: '2023-12-23'
        },
        {
          id: 25,
          title: '图解：菏泽市国土空间规划编制工作方案',
          created_at: '2023-12-20'
        },
        {
          id: 26,
          title: '解读：菏泽市建设用地审批制度改革实施方案',
          created_at: '2023-10-15'
        },
        {
          id: 27,
          title: '政策解读：菏泽市城市更新行动实施方案',
          created_at: '2023-08-20'
        },
        {
          id: 28,
          title: '图解：菏泽市"十四五"自然资源保护和利用规划',
          created_at: '2023-06-10'
        },
        {
          id: 29,
          title: '解读：菏泽市农村宅基地管理办法',
          created_at: '2023-04-05'
        },
        {
          id: 30,
          title: '一图读懂《菏泽市国土空间生态修复规划》',
          created_at: '2023-02-15'
        }
      ]
    }
  ]
}

// 模拟banner数据
const loadMockBanners = () => {
  const mockBanners = [
    {
      id: 1,
      image_url: 'https://via.placeholder.com/1200x300/dc2626/ffffff?text=服务数字化转型',
      link_url: 'https://example.com/digital-government',
      description: '服务弘扬群行 教育家精神',
      order: 1
    },
    {
      id: 2,
      image_url: 'https://via.placeholder.com/1200x300/1e40af/ffffff?text=优化营商环境',
      link_url: 'https://example.com/business-environment',
      description: '优化营商环境 激发市场活力',
      order: 2
    }
  ]

  banners.value = formatBannersData(mockBanners)
}

// 方法
const handleSearch = () => {
  if (searchKeyword.value.trim()) {
    console.log('搜索关键词:', searchKeyword.value)
    // 这里可以实现搜索功能
  }
}



const goToArticle = (articleId) => {
  router.push({ name: 'cms-article-detail', params: { id: articleId } })
}

const goToCategory = (categoryId) => {
  router.push(`/cms/category/${categoryId}`)
}

const goToHome = () => {
  router.push('/cms')
}



const formatDate = (dateString) => {
  if (!dateString) return ''
  // 使用东八区时间格式化，只显示月-日
  const beijingTime = formatBeijingTime(dateString, 'date')
  if (beijingTime) {
    // 从 YYYY-MM-DD 格式中提取 MM-DD
    const parts = beijingTime.split('-')
    if (parts.length >= 3) {
      return `${parts[1]}-${parts[2]}`
    }
  }
  // 降级处理
  return new Date(dateString).toLocaleDateString('zh-CN', {
    month: '2-digit',
    day: '2-digit'
  })
}

// Banner点击处理
const handleBannerClick = (banner) => {
  if (banner.link_url && banner.link_url !== '#') {
    // 如果是外部链接，在新窗口打开
    if (banner.link_url.startsWith('http://') || banner.link_url.startsWith('https://')) {
      window.open(banner.link_url, '_blank')
    } else {
      // 如果是内部链接，使用路由跳转
      window.location.href = banner.link_url
    }
  }
}

// 图片加载错误处理
const handleImageError = (event) => {
  console.error('Banner图片加载失败:', event.target.src)
  // 设置默认图片
  event.target.src = 'https://via.placeholder.com/1200x300/dc2626/ffffff?text=信息平台'
}

// 生命周期
onMounted(() => {
  fetchData()
  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  // 移除窗口大小变化监听
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
/* 隐藏滚动条但保持滚动功能 */
.scrollbar-hide {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.cms-home {
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}
</style>
