import request from '@/utils/request'

/**
 * 上传相关API接口
 */

/**
 * 上传图片
 * @param {File} file - 图片文件
 * @returns {Promise}
 */
export function uploadImage(file) {
  const formData = new FormData()
  formData.append('image', file)
  
  return request.post('/upload/image', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 上传文件
 * @param {File} file - 文件
 * @returns {Promise}
 */
export function uploadFile(file) {
  const formData = new FormData()
  formData.append('file', file)
  
  return request.post('/upload/file', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 批量上传图片
 * @param {FileList} files - 图片文件列表
 * @returns {Promise}
 */
export function uploadImages(files) {
  const formData = new FormData()
  
  for (let i = 0; i < files.length; i++) {
    formData.append('images[]', files[i])
  }
  
  return request.post('/upload/images', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 删除上传的文件
 * @param {string} filePath - 文件路径
 * @returns {Promise}
 */
export function deleteUploadedFile(filePath) {
  return request.delete('/upload/file', {
    data: { file_path: filePath }
  })
}
