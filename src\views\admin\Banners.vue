<template>
  <div class="bg-white p-6 rounded-lg shadow-md">
    <h2 class="text-2xl font-bold mb-4">广告横幅管理</h2>

    <!-- 操作按钮 -->
    <div class="mb-4 flex items-center space-x-2">
      <el-button type="primary" @click="handleAdd">新增横幅</el-button>
      <el-button type="danger" :disabled="selectedBanners.length === 0" @click="handleBatchDelete">
        批量删除 ({{ selectedBanners.length }})
      </el-button>
      <el-button @click="fetchBanners">刷新</el-button>
    </div>

    <el-table :data="banners" border style="width: 100%" v-loading="loading" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />

      <el-table-column label="预览" width="120">
        <template #default="{ row }">
          <div class="w-20 h-12 rounded overflow-hidden border border-gray-200">
            <img :src="row.image_url" :alt="row.description" class="w-full h-full object-cover cursor-pointer" @click="previewImage(row.image_url)" />
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="description" label="描述" min-width="200">
        <template #default="{ row }">
          <div class="font-medium">{{ row.description }}</div>
          <div class="text-sm text-gray-500 mt-1">
            <el-link :href="row.link_url" target="_blank" type="primary" size="small">
              {{ row.link_url }}
            </el-link>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="order" label="排序" width="100" sortable>
        <template #default="{ row }">
          <el-tag size="small">{{ row.order }}</el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="created_at" label="创建时间" width="180">
        <template #default="{ row }">
          {{ formatDate(row.created_at) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <el-button size="small" type="primary" @click="handleEdit(row)">编辑</el-button>
          <el-button size="small" type="info" @click="handlePreview(row)">预览</el-button>
          <el-button size="small" type="danger" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination v-model:current-page="currentPage" :page-size="pageSize" :total="total" layout="total, prev, pager, next" class="mt-4"
      @current-change="handleCurrentChange" />

    <!-- 新增/编辑对话框 -->
    <el-dialog v-model="dialogVisible" :title="isEdit ? '编辑横幅' : '新增横幅'" width="600px" @close="resetForm">
      <el-form ref="formRef" :model="form" :rules="formRules" label-width="100px">
        <el-form-item label="横幅图片" prop="imageFile" required>
          <el-upload ref="uploadRef" :auto-upload="false" :show-file-list="false" :on-change="handleImageChange" accept="image/*" drag>
            <div v-if="!imagePreview" class="upload-area">
              <el-icon class="upload-icon">
                <Plus />
              </el-icon>
              <div class="upload-text">点击或拖拽上传图片</div>
              <div class="upload-hint">支持 JPG、PNG 格式，建议尺寸 1200x400</div>
            </div>
            <div v-else class="image-preview">
              <img :src="imagePreview" alt="预览" />
              <div class="image-overlay">
                <el-button size="small" type="primary" @click.stop="handleImageChange">
                  重新选择
                </el-button>
              </div>
            </div>
          </el-upload>
        </el-form-item>

        <el-form-item label="跳转链接" prop="link_url">
          <el-input v-model="form.link_url" placeholder="请输入跳转链接，如：https://example.com" />
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入横幅描述" />
        </el-form-item>

        <el-form-item label="排序" prop="order">
          <el-input-number v-model="form.order" :min="0" :max="999" placeholder="数字越小排序越靠前" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog v-model="previewVisible" title="图片预览" width="800px">
      <div class="text-center">
        <img :src="previewImageUrl" alt="预览" class="max-w-full max-h-96" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import {
  getBannerList,
  createBanner,
  updateBanner,
  deleteBanner,
  batchDeleteBanners
} from '@/api/admin/banners'
import { formatBeijingTime } from '@/utils/timezone'

// 响应式数据
const loading = ref(false)
const banners = ref([])
const selectedBanners = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 对话框相关
const dialogVisible = ref(false)
const isEdit = ref(false)
const submitting = ref(false)
const formRef = ref()
const uploadRef = ref()

// 图片预览
const previewVisible = ref(false)
const previewImageUrl = ref('')
const imagePreview = ref('')
const selectedFile = ref(null)

// 表单数据
const form = reactive({
  id: null,
  imageFile: null,
  link_url: '',
  description: '',
  order: 0
})

// 自定义图片验证规则
const validateImage = (rule, value, callback) => {
  // 编辑模式下，如果有现有图片或选择了新图片，则通过验证
  if (isEdit.value && (imagePreview.value || selectedFile.value)) {
    callback()
  }
  // 新增模式下，必须选择图片
  else if (!isEdit.value && selectedFile.value) {
    callback()
  }
  // 其他情况验证失败
  else {
    callback(new Error('请选择横幅图片'))
  }
}

// 表单验证规则
const formRules = {
  imageFile: [
    { validator: validateImage, trigger: ['change', 'blur'] }
  ],
  link_url: [
    { required: true, message: '请输入跳转链接', trigger: 'blur' },
    { type: 'url', message: '请输入有效的URL地址', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入描述', trigger: 'blur' },
    { min: 2, max: 200, message: '描述长度在 2 到 200 个字符', trigger: 'blur' }
  ],
  order: [
    { required: true, message: '请输入排序', trigger: 'blur' },
    { type: 'number', min: 0, max: 999, message: '排序必须在 0-999 之间', trigger: 'blur' }
  ]
}

// 获取横幅列表
const fetchBanners = async () => {
  loading.value = true
  try {
    const response = await getBannerList({
      page: currentPage.value,
      page_size: pageSize.value
    })

    banners.value = response.data || []
    total.value = response.total || 0
    currentPage.value = response.current_page || 1
  } catch (error) {
    console.error('获取横幅列表失败:', error)
    ElMessage.error('获取横幅列表失败')

    // 使用模拟数据
    banners.value = [
      {
        id: 1,
        image_url: 'https://via.placeholder.com/1200x400/1e40af/ffffff?text=Banner+1',
        link_url: 'https://example.com/page1',
        description: '服务数字化转型宣传横幅',
        order: 1,
        created_at: '2025-01-20 10:00:00',
        updated_at: '2025-01-20 10:00:00'
      },
      {
        id: 2,
        image_url: 'https://via.placeholder.com/1200x400/dc2626/ffffff?text=Banner+2',
        link_url: 'https://example.com/page2',
        description: '优化营商环境专项行动',
        order: 2,
        created_at: '2025-01-19 15:30:00',
        updated_at: '2025-01-19 15:30:00'
      }
    ]
    total.value = 2
  } finally {
    loading.value = false
  }
}

// 处理选择变化
const handleSelectionChange = (selection) => {
  selectedBanners.value = selection
}

// 处理分页
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  fetchBanners()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchBanners()
}

// 新增横幅
const handleAdd = () => {
  isEdit.value = false
  resetForm() // 确保表单被重置
  dialogVisible.value = true
}

// 编辑横幅
const handleEdit = (row) => {
  resetForm() // 先重置表单
  isEdit.value = true
  form.id = row.id
  form.link_url = row.link_url
  form.description = row.description
  form.order = row.order
  form.imageFile = 'existing' // 标记为已有图片
  imagePreview.value = row.image_url
  selectedFile.value = null // 清空新选择的文件
  dialogVisible.value = true
}

// 删除横幅
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除横幅"${row.description}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteBanner(row.id)
    ElMessage.success('删除成功')
    fetchBanners()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除横幅失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedBanners.value.length} 个横幅吗？此操作不可恢复。`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const ids = selectedBanners.value.map(item => item.id)
    await batchDeleteBanners(ids)
    ElMessage.success('批量删除成功')
    fetchBanners()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// 预览横幅
const handlePreview = (row) => {
  previewImage(row.image_url)
}

// 预览图片
const previewImage = (url) => {
  previewImageUrl.value = url
  previewVisible.value = true
}

// 处理图片选择
const handleImageChange = (file) => {
  if (file && file.raw) {
    selectedFile.value = file.raw
    form.imageFile = file.raw // 设置表单字段用于验证

    // 创建预览URL
    const reader = new FileReader()
    reader.onload = (e) => {
      imagePreview.value = e.target.result
    }
    reader.readAsDataURL(file.raw)

    // 触发验证
    if (formRef.value) {
      formRef.value.validateField('imageFile')
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    submitting.value = true

    // 创建FormData
    const formData = new FormData()
    if (selectedFile.value) {
      formData.append('image', selectedFile.value)
    }
    formData.append('link_url', form.link_url)
    formData.append('description', form.description)
    formData.append('order', form.order)

    if (isEdit.value) {
      await updateBanner(form.id, formData)
      ElMessage.success('更新成功')
    } else {
      await createBanner(formData)
      ElMessage.success('创建成功')
    }

    dialogVisible.value = false
    fetchBanners()
  } catch (error) {
    console.error('提交失败:', error)

    // 处理表单验证错误
    if (error && typeof error === 'object' && !error.response) {
      // 这是表单验证错误
      const errorMessages = []

      // 遍历所有字段的错误
      Object.keys(error).forEach(field => {
        if (Array.isArray(error[field]) && error[field].length > 0) {
          errorMessages.push(error[field][0].message)
        }
      })

      if (errorMessages.length > 0) {
        ElMessage.error(errorMessages.join('；'))
      } else {
        ElMessage.error('表单验证失败，请检查输入内容')
      }
    } else {
      // 这是API请求错误
      ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
    }
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  form.id = null
  form.imageFile = null
  form.link_url = ''
  form.description = ''
  form.order = 0
  imagePreview.value = ''
  selectedFile.value = null

  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  return formatBeijingTime(dateString, 'datetime')
}

// 生命周期
onMounted(() => {
  fetchBanners()
})
</script>

<style scoped>
.upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 200px;
  border: 2px dashed #dcdfe6;
  border-radius: 6px;
  cursor: pointer;
  transition: border-color 0.3s;
}

.upload-area:hover {
  border-color: #409eff;
}

.upload-icon {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.upload-text {
  color: #606266;
  font-size: 14px;
  margin-bottom: 8px;
}

.upload-hint {
  color: #909399;
  font-size: 12px;
}

.image-preview {
  position: relative;
  width: 100%;
  height: 200px;
  border-radius: 6px;
  overflow: hidden;
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.image-preview:hover .image-overlay {
  opacity: 1;
}
</style>
