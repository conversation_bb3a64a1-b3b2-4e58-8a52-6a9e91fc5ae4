# 经纪人状态显示修复验证指南

## 🎯 验证目标

验证两个关键修复：
1. 账户状态使用 `deleted_at` 字段正确显示
2. 机构管理员端审核状态显示中文而非英文

## 📋 验证步骤

### 1. 超级管理员端验证

#### 1.1 访问经纪人列表
1. 登录超级管理员账户
2. 导航到 `/admin/brokers`
3. 检查"账户状态"列

**预期结果**:
- 列表中显示"账户状态"列
- `deleted_at` 为null的记录显示"正常"（绿色标签）
- `deleted_at` 有值的记录显示"已禁用"（红色标签）

#### 1.2 查看经纪人详情
1. 点击任意经纪人的"查看"按钮
2. 在详情页面查找"账户状态"字段

**预期结果**:
- 详情页面显示账户状态信息
- 根据 `deleted_at` 字段正确显示"正常"或"已禁用"
- 标签颜色正确（绿色/红色）

### 2. 机构管理员端验证

#### 2.1 访问经纪人列表
1. 登录机构管理员账户
2. 导航到 `/agency/brokers`
3. 检查"审核状态"列

**预期结果**:
- 审核状态显示中文文本
- `pending` 显示为"待审核"（橙色标签）
- `approved` 显示为"已通过"（绿色标签）
- `rejected` 显示为"已驳回"（红色标签）

## 🧪 测试数据对照

### 账户状态测试数据
```javascript
// 正常账户
{
  id: 1,
  name: "张三",
  deleted_at: null
  // 应显示: "正常" (绿色标签)
}

// 已禁用账户
{
  id: 2,
  name: "李四",
  deleted_at: "2025-07-23 12:00:00"
  // 应显示: "已禁用" (红色标签)
}
```

### 审核状态测试数据
```javascript
// 待审核
{
  id: 1,
  name: "张三",
  status: "pending"
  // 应显示: "待审核" (橙色标签)
}

// 已通过
{
  id: 2,
  name: "李四",
  status: "approved"
  // 应显示: "已通过" (绿色标签)
}

// 已驳回
{
  id: 3,
  name: "王五",
  status: "rejected"
  // 应显示: "已驳回" (红色标签)
}
```

## 🔍 详细验证清单

### 超级管理员端 - 列表页面
- [ ] 页面正常加载
- [ ] "账户状态"列存在
- [ ] deleted_at为null时显示"正常"
- [ ] deleted_at有值时显示"已禁用"
- [ ] 正常状态使用绿色success标签
- [ ] 禁用状态使用红色danger标签

### 超级管理员端 - 详情页面
- [ ] 详情页面正常加载
- [ ] 账户状态信息正确显示
- [ ] 标签颜色与列表页面一致
- [ ] 空值处理正常

### 机构管理员端 - 列表页面
- [ ] 页面正常加载
- [ ] "审核状态"列存在
- [ ] pending状态显示"待审核"（橙色）
- [ ] approved状态显示"已通过"（绿色）
- [ ] rejected状态显示"已驳回"（红色）
- [ ] 不再显示英文原始值

## 🚨 常见问题检查

### 1. 账户状态显示问题
**问题**: 账户状态显示异常
**检查项**:
- [ ] API返回数据包含 `deleted_at` 字段
- [ ] `deleted_at` 字段格式正确（null或时间戳）
- [ ] 前端函数 `getAccountStatusLabel` 正常工作

### 2. 审核状态显示问题
**问题**: 仍显示英文状态
**检查项**:
- [ ] 模板使用了 `getStatusLabel` 函数
- [ ] 函数映射关系正确
- [ ] 浏览器缓存已清除

### 3. 标签颜色问题
**问题**: 标签颜色不正确
**检查项**:
- [ ] `getAccountStatusType` 函数返回正确类型
- [ ] `getStatusType` 函数返回正确类型
- [ ] Element Plus标签组件正常工作

## 📱 浏览器测试

建议在以下浏览器中验证：
- [ ] Chrome (最新版本)
- [ ] Firefox (最新版本)
- [ ] Safari (最新版本)
- [ ] Edge (最新版本)

## 🔄 回归测试

确保修复没有影响其他功能：
- [ ] 经纪人列表加载正常
- [ ] 分页功能正常
- [ ] 审核功能正常
- [ ] 详情页面其他信息正常显示

## 📊 预期效果对比

### 修复前 vs 修复后

#### 账户状态
| 修复前 | 修复后 |
|--------|--------|
| 显示user.status | 显示deleted_at状态 |
| pending/approved/rejected | 正常/已禁用 |
| 不够直观 | 清晰明了 |

#### 审核状态（机构管理员端）
| 修复前 | 修复后 |
|--------|--------|
| pending | 待审核 |
| approved | 已通过 |
| rejected | 已驳回 |
| 英文显示 | 中文显示 |

## ✅ 验证完成标准

所有验证项目通过，包括：
- 账户状态正确使用 `deleted_at` 字段
- 机构管理员端审核状态显示中文
- 标签颜色正确
- 用户体验良好
- 没有引入新的问题

## 📝 验证报告模板

```
验证时间: ____
验证人员: ____
验证环境: ____

超级管理员端:
- 列表页面账户状态: ✅/❌
- 详情页面账户状态: ✅/❌

机构管理员端:
- 列表页面审核状态: ✅/❌

发现问题:
1. ____
2. ____

总体评价: ✅通过/❌需要修复
```

---

**验证环境**: `http://localhost:5174`  
**API环境**: `http://127.0.0.1:8000`  
**修复版本**: 最新版本
