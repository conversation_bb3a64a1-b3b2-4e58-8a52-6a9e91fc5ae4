import request from '@/utils/request'

/**
 * 视频管理相关API接口
 */

/**
 * 获取课程视频列表
 * @param {number} courseId - 课程ID
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.per_page - 每页数量
 * @param {string} params.keyword - 搜索关键词
 * @returns {Promise}
 */
export function getCourseVideoList(courseId, params = {}) {
  return request.get(`/admin/courses/${courseId}/videos`, { params })
}

/**
 * 获取视频详情
 * @param {number} videoId - 视频ID
 * @returns {Promise}
 */
export function getVideoDetail(videoId) {
  return request.get(`/admin/videos/${videoId}`)
}

/**
 * 创建课程视频
 * @param {number} courseId - 课程ID
 * @param {Object} data - 视频数据
 * @param {string} data.title - 视频标题
 * @param {string} data.description - 视频描述
 * @param {string} data.video_url - 视频URL
 * @param {number} data.duration - 视频时长（秒）
 * @param {number} data.order - 排序序号
 * @returns {Promise}
 */
export function createCourseVideo(courseId, data) {
  return request.post(`/admin/courses/${courseId}/videos`, data)
}

/**
 * 更新视频信息
 * @param {number} videoId - 视频ID
 * @param {Object} data - 视频数据
 * @param {string} data.title - 视频标题
 * @param {string} data.description - 视频描述
 * @param {string} data.video_url - 视频URL
 * @param {number} data.duration - 视频时长（秒）
 * @param {number} data.order - 排序序号
 * @returns {Promise}
 */
export function updateVideo(videoId, data) {
  return request.put(`/admin/videos/${videoId}`, data)
}

/**
 * 删除视频
 * @param {number} videoId - 视频ID
 * @returns {Promise}
 */
export function deleteVideo(videoId) {
  return request.delete(`/admin/videos/${videoId}`)
}
