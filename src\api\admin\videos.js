import request from '@/utils/request'

/**
 * 视频管理相关API接口
 */

/**
 * 获取课程视频列表
 * @param {number} courseId - 课程ID
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.per_page - 每页数量
 * @param {string} params.keyword - 搜索关键词
 * @returns {Promise}
 */
export function getCourseVideoList(courseId, params = {}) {
  return request.get(`/admin/courses/${courseId}/videos`, { params })
}

/**
 * 获取视频详情
 * @param {number} videoId - 视频ID
 * @returns {Promise}
 */
export function getVideoDetail(videoId) {
  return request.get(`/admin/videos/${videoId}`)
}

/**
 * 创建课程视频
 * @param {number} courseId - 课程ID
 * @param {Object|FormData} data - 视频数据
 * @param {string} data.title - 视频标题
 * @param {string} data.description - 视频描述
 * @param {string} [data.video_url] - 视频URL（与video_file二选一）
 * @param {File} [data.video_file] - 视频文件（与video_url二选一）
 * @param {number} [data.duration] - 视频时长（秒），上传文件时可自动获取
 * @param {number} data.order - 排序序号
 * @returns {Promise}
 */
export function createCourseVideo(courseId, data) {
  const isFormData = data instanceof FormData
  return request.post(`/admin/courses/${courseId}/videos`, data, {
    headers: isFormData ? {
      'Content-Type': 'multipart/form-data'
    } : {}
  })
}

/**
 * 更新视频信息
 * @param {number} videoId - 视频ID
 * @param {Object|FormData} data - 视频数据
 * @param {string} data.title - 视频标题
 * @param {string} data.description - 视频描述
 * @param {string} [data.video_url] - 视频URL（与video_file二选一）
 * @param {File} [data.video_file] - 新视频文件（可选，上传则替换原文件）
 * @param {number} data.duration - 视频时长（秒）
 * @param {number} data.order - 排序序号
 * @returns {Promise}
 */
export function updateVideo(videoId, data) {
  const isFormData = data instanceof FormData
  return request.put(`/admin/videos/${videoId}`, data, {
    headers: isFormData ? {
      'Content-Type': 'multipart/form-data'
    } : {}
  })
}

/**
 * 删除视频
 * @param {number} videoId - 视频ID
 * @returns {Promise}
 */
export function deleteVideo(videoId) {
  return request.delete(`/admin/videos/${videoId}`)
}
