<template>
  <div class="bg-white p-6 rounded-lg shadow-md mx-auto max-w-4xl">
    <div v-if="archive">
      <h2 class="text-2xl md:text-3xl font-bold mb-2">{{ archive.title }}</h2>
      <div class="text-gray-500 text-sm mb-4">
        {{ archive.created_at }} | {{ archive.entity_type }} - {{ archive.entity_name }} |
        {{ archive.type }}
      </div>
      <div class="content prose max-w-none" v-html="archive.content"></div>
      <div class="mt-6 flex justify-end">
        <el-button type="primary" @click="handleBack">返回列表</el-button>
      </div>
    </div>
    <div v-else class="text-center py-10">
      <el-spinner></el-spinner>
      <p class="mt-2 text-gray-500">加载中...</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()
const archive = ref(null)
const archiveId = ref(parseInt(route.params.id))

onMounted(() => {
  fetchArchive()
})

const fetchArchive = () => {
  // 模拟数据
  setTimeout(() => {
    archive.value = {
      id: archiveId.value,
      title: '优秀中介公司表彰',
      entity_type: '企业',
      entity_name: '中介公司A',
      type: '红榜',
      created_at: '2023-01-01 10:00:00',
      content: `
        <p>中介公司A因其卓越的服务质量和良好的市场口碑，被评为2023年度优秀中介公司。</p>
        <h3>表彰原因</h3>
        <ul>
          <li>客户满意度高</li>
          <li>严格遵守行业规范</li>
          <li>积极参与行业培训和公益活动</li>
        </ul>
        <p>希望其他中介公司向其学习，共同提升行业服务水平。</p>
      `,
    }
  }, 500)
}

const handleBack = () => {
  router.push('/cms/credit-archives')
}
</script>

<style scoped>
/* 信用档案详情特定样式 */
:deep(.content) {
  line-height: 1.6;
}
:deep(.content h3) {
  font-size: 1.25rem;
  font-weight: bold;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
}
:deep(.content ul) {
  list-style-type: disc;
  padding-left: 2rem;
}
:deep(.content li) {
  margin-bottom: 0.5rem;
}
</style>
