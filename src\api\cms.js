import request from '@/utils/request'

// CMS相关API接口

// 获取文章分类列表
export function getCategories() {
    return request.get('/cms/categories')
}

// 获取单个分类信息
export function getCategoryById(id) {
    return request.get(`/cms/categories/${id}`)
}

// 获取热门分类（用于首页卡片展示）
export function getHotCategories() {
    return request.get('/cms/categories/hot')
}

// 获取指定分类下的文章列表
export function getArticlesByCategory(categoryId, params = {}) {
    // 使用正确的API路径，将category_id作为查询参数
    const queryParams = {
        category_id: categoryId,
        ...params
    }
    return request.get('/cms/articles', { params: queryParams })
}

// 获取所有分类及其文章列表（用于首页展示）
export function getCategoriesWithArticles(params = {}) {
    const defaultParams = {
        page: 1,
        per_page: 10,
        ...params
    }
    return request.get('/cms/categories-with-articles', { params: defaultParams })
}

// 获取文章列表
export function getArticles(params = {}) {
    return request.get('/cms/articles', { params })
}

// 获取文章详情
export function getArticleDetail(id) {
    return request.get(`/cms/articles/${id}`)
}

// 获取轮播图/横幅广告
export function getBanners() {
    return request.get('/cms/banners')
}

// 获取热门文章
export function getHotArticles(limit = 10) {
    return request.get('/cms/articles/hot', {
        params: { limit }
    })
}

// 搜索文章
export function searchArticles(keyword, params = {}) {
    return request.get('/cms/articles/search', {
        params: { keyword, ...params }
    })
}
