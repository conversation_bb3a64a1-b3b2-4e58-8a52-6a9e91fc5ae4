# CMS 信息平台重新设计文档

## 🎯 设计目标

根据您提供的参考样式，重新设计CMS首页，采用更符合政府网站标准的列表式布局和简洁风格。

## 📋 主要更新内容

### 1. 顶部导航栏重新设计

#### 1.1 标题区域

- **政府徽标**: 红色圆形徽标，内含"政"字
- **双语标题**: 中文"信息平台" + 英文"Government Information Platform"
- **搜索功能**: 右侧搜索框，橙色搜索按钮

#### 1.2 主导航栏

- **深色背景**: 使用slate-700深灰色背景
- **图标导航**: 每个导航项配有对应图标
- **导航项目**: 首页、概况、动态、公开、服务、互动、专题

### 2. 轮播横幅优化

#### 2.1 视觉设计

- **渐变背景**: 红色到橙色的渐变背景
- **高度调整**: 从400px调整为300px
- **文字布局**: 居中显示，更大的字体

#### 2.2 内容更新

- **主标题**: "服务弘扬群行"
- **副标题**: "教育家精神"
- **底部标识**: 显示具体项目名称

### 3. 内容布局重新设计

#### 3.1 列表式布局

- **三列网格**: 桌面端显示3个分类列
- **响应式**: 移动端自动调整为单列
- **紧凑设计**: 更高效的空间利用

#### 3.2 分类卡片设计

```
┌─────────────────────────┐
│ ▌ 阳光规划              │ ← 蓝色边框 + 左侧蓝条
├─────────────────────────┤
│ • 菏泽市自然资源...  07-17│ ← 圆点 + 标题 + 日期
│ • 菏泽市自然资源...  07-15│
│ • 菏泽市自然资源...  07-11│
│ • 《菏泽市西城区...  07-09│
│ • 《菏泽市开发区...  07-09│
├─────────────────────────┤
│      更多 阳光规划 →     │ ← 查看更多链接
└─────────────────────────┘
```

### 4. 内容数据更新

#### 4.1 分类重新设计

- **阳光规划**: 城市规划公开信息
- **政策法规**: 政策法规文件
- **政策解读**: 政策解读说明

#### 4.2 文章标题优化

- 使用真实的政府文件标题
- 保持标题的专业性和权威性
- 添加省略号表示标题截断

#### 4.3 日期格式调整

- 使用MM-DD格式显示日期
- 更符合政府网站的显示习惯

## 🎨 视觉设计特点

### 1. 政府风格强化

- **权威色彩**: 红色徽标 + 蓝色主题色
- **简洁布局**: 清晰的信息层次
- **专业字体**: 系统字体，确保可读性

### 2. 用户体验优化

- **快速浏览**: 列表式布局便于快速扫描
- **信息密度**: 在有限空间内展示更多内容
- **交互反馈**: 悬停效果和点击反馈

### 3. 响应式设计

- **桌面端**: 3列布局，充分利用屏幕空间
- **平板端**: 2列布局，保持可读性
- **移动端**: 单列布局，适合触摸操作

## 🔧 技术实现

### 1. 组件结构优化

```vue
<template>
  <div class="cms-home">
    <!-- 顶部导航 -->
    <header>
      <!-- 标题栏 -->
      <div class="title-bar">
        <div class="logo-title">
          <div class="logo">政</div>
          <div class="title">
            <h1>信息平台</h1>
            <p>Government Information Platform</p>
          </div>
        </div>
        <div class="search-box">
          <input placeholder="请输入关键词" />
          <button>搜索</button>
        </div>
      </div>

      <!-- 主导航 -->
      <nav class="main-nav">
        <a v-for="nav in mainNavs"> <icon />{{ nav.name }} </a>
      </nav>
    </header>

    <!-- 轮播横幅 -->
    <section class="banner">
      <el-carousel>
        <!-- 轮播内容 -->
      </el-carousel>
    </section>

    <!-- 内容区域 -->
    <main class="content">
      <div class="grid-3-cols">
        <div v-for="category in categories" class="category-card">
          <div class="category-header">
            <span class="blue-bar"></span>
            <h2>{{ category.name }}</h2>
          </div>
          <ul class="article-list">
            <li v-for="article in articles">
              <span class="dot"></span>
              <span class="title">{{ article.title }}</span>
              <span class="date">{{ article.date }}</span>
            </li>
          </ul>
          <div class="more-link">更多 {{ category.name }} →</div>
        </div>
      </div>
    </main>
  </div>
</template>
```

### 2. 样式系统

```css
/* 主要颜色变量 */
--primary-red: #dc2626 --primary-blue: #2563eb --slate-700: #334155 --orange-500: #f97316
  /* 布局网格 */ .grid-3-cols {display: grid; grid-template-columns: repeat(3, 1fr) ; gap: 2rem;}
  /* 分类卡片 */ .category-card {background: white; border: 1px solid #e5e7eb;
  border-radius: 0.5rem;} /* 蓝色装饰条 */ .blue-bar {width: 4px; height: 24px;
  background: #2563eb;};
```

### 3. 数据结构

```javascript
// 主导航数据
const mainNavs = [
  { id: 1, name: '首页', icon: 'House' },
  { id: 2, name: '概况', icon: 'Document' },
  { id: 3, name: '动态', icon: 'Notification' },
  { id: 4, name: '公开', icon: 'Service' },
  { id: 5, name: '服务', icon: 'Setting' },
  { id: 6, name: '互动', icon: 'User' },
  { id: 7, name: '专题', icon: 'Document' },
]

// 分类数据
const categories = [
  {
    id: 1,
    name: '阳光规划',
    articles: [
      {
        id: 1,
        title: '菏泽市自然资源和规划局关于鲁中车...',
        created_at: '2025-07-17',
      },
      // ...更多文章
    ],
  },
  // ...更多分类
]
```

## 📱 响应式适配

### 1. 断点设置

- **lg**: >= 1024px (3列布局)
- **md**: 768px - 1023px (2列布局)
- **sm**: < 768px (1列布局)

### 2. 适配策略

- **导航栏**: 移动端可能需要汉堡菜单
- **搜索框**: 移动端可能需要折叠
- **文章标题**: 自动截断，保持整齐

## 🚀 部署和测试

### 1. 访问地址

- **CMS首页**: `http://localhost:5174/cms`
- **文章详情**: `http://localhost:5174/cms/article/1`

### 2. 测试要点

- [ ] 顶部导航栏正确显示
- [ ] 搜索功能正常工作
- [ ] 轮播横幅自动播放
- [ ] 分类卡片正确布局
- [ ] 文章列表点击跳转
- [ ] 响应式布局正确

### 3. 浏览器兼容性

- Chrome、Firefox、Safari、Edge
- 移动端Safari、Chrome

## 📊 对比分析

### 更新前 vs 更新后

| 特性     | 更新前           | 更新后           |
| -------- | ---------------- | ---------------- |
| 布局方式 | 卡片式大块布局   | 列表式紧凑布局   |
| 信息密度 | 较低，每屏显示少 | 较高，每屏显示多 |
| 视觉风格 | 现代化卡片风格   | 传统政府网站风格 |
| 导航设计 | 简单顶部导航     | 双层导航 + 搜索  |
| 内容展示 | 摘要 + 详情      | 标题 + 日期      |
| 响应式   | 3-2-1列响应式    | 3-2-1列响应式    |

## ✅ 完成状态

- [x] 顶部导航栏重新设计
- [x] 轮播横幅优化
- [x] 列表式布局实现
- [x] 分类卡片重新设计
- [x] 内容数据更新
- [x] 响应式适配
- [x] 政府风格强化

---

**设计状态**: ✅ 完成  
**开发状态**: ✅ 完成  
**测试状态**: ✅ 可测试  
**风格符合度**: ✅ 符合政府网站标准
