import request from '@/utils/request'

/**
 * CMS端信用档案API
 */

// 获取信用档案列表
export function getCreditArchivesList(params = {}) {
  return request.get('/cms/credit-archives', { params })
}

// 获取信用档案详情
export function getCreditArchiveDetail(id) {
  return request.get(`/cms/credit-archives/${id}`)
}

// 获取信用档案统计数据 - 近6个月红榜黑榜增长趋势
export function getCreditArchiveStatistics() {
  return request.get('/cms/credit-archives/statistics')
}

// 档案类型常量
export const ARCHIVE_TYPES = {
  red: 'red',
  black: 'black'
}

// 主体类型常量
export const ENTITY_TYPES = {
  agency: 'agency',
  broker: 'broker'
}

// 档案类型标签映射
export const ARCHIVE_TYPE_LABELS = {
  red: '红榜',
  black: '黑榜'
}

// 主体类型标签映射
export const ENTITY_TYPE_LABELS = {
  agency: '企业',
  broker: '个人'
}
