<template>
  <div class="bg-white p-6 rounded-lg shadow-md">
    <h2 class="text-2xl font-bold mb-4">课程视频 - {{ courseTitle }}</h2>
    <div class="mb-4" v-if="currentVideo">
      <div class="aspect-w-16 aspect-h-9 bg-gray-200 rounded-lg overflow-hidden mb-2">
        <video
          ref="videoPlayer"
          controls
          class="w-full h-full"
          :src="currentVideo.video_url"
          @timeupdate="handleTimeUpdate"
        >
          您的浏览器不支持视频播放。
        </video>
      </div>
      <div class="flex justify-between items-center text-sm text-gray-600">
        <span>{{ currentVideo.title }}</span>
        <span>进度: {{ currentVideo.progress }}%</span>
      </div>
    </div>
    <el-table :data="videos" border style="width: 100%" class="w-full mt-4">
      <el-table-column prop="id" label="ID" width="80"></el-table-column>
      <el-table-column prop="title" label="视频标题" min-width="200"></el-table-column>
      <el-table-column prop="duration" label="时长（秒）" width="100"></el-table-column>
      <el-table-column prop="progress" label="学习进度" width="120">
        <template #default="scope">
          <el-progress :percentage="scope.row.progress" :format="formatProgress"></el-progress>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120">
        <template #default="scope">
          <el-button
            size="small"
            type="primary"
            @click="handlePlayVideo(scope.row)"
            :disabled="!scope.row.isUnlocked"
            >播放</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="currentPage"
      :page-size="pageSize"
      :total="total"
      layout="total, prev, pager, next"
      class="mt-4 flex justify-center"
      @current-change="handlePageChange"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const courseId = ref(parseInt(route.params.id))
const courseTitle = ref('')
const videos = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const currentVideo = ref(null)
const videoPlayer = ref(null)
let progressInterval = ref(null)

onMounted(() => {
  fetchCourseVideos()
})

onUnmounted(() => {
  if (progressInterval.value) {
    clearInterval(progressInterval.value)
  }
})

const fetchCourseVideos = () => {
  // 模拟数据
  courseTitle.value = '房产中介基础课程'
  videos.value = [
    {
      id: 1,
      title: '房产中介基础知识视频1',
      video_url: 'https://via.placeholder.com/video.mp4',
      duration: 300,
      progress: 60,
      isUnlocked: true,
      currentTime: 180,
    },
    {
      id: 2,
      title: '房产中介基础知识视频2',
      video_url: 'https://via.placeholder.com/video.mp4',
      duration: 450,
      progress: 0,
      isUnlocked: false,
      currentTime: 0,
    },
    // 更多数据...
  ]
  total.value = videos.value.length
  checkVideoUnlockStatus()
}

const checkVideoUnlockStatus = () => {
  // 模拟顺序解锁逻辑：只有前一个视频完成才能解锁下一个
  let allPreviousCompleted = true
  for (let i = 0; i < videos.value.length; i++) {
    if (i === 0) {
      videos.value[i].isUnlocked = true
    } else {
      videos.value[i].isUnlocked = allPreviousCompleted
    }
    if (videos.value[i].progress < 100) {
      allPreviousCompleted = false
    }
  }
}

const formatProgress = (percentage) => {
  return `${percentage}%`
}

const handlePlayVideo = (video) => {
  currentVideo.value = video
  setTimeout(() => {
    if (videoPlayer.value) {
      videoPlayer.value.currentTime = video.currentTime || 0
      videoPlayer.value.play()
      startProgressTracking()
    }
  }, 100)
}

const handleTimeUpdate = () => {
  if (currentVideo.value && videoPlayer.value) {
    const currentTime = videoPlayer.value.currentTime
    const duration = currentVideo.value.duration
    const progress = Math.min(100, Math.round((currentTime / duration) * 100))
    currentVideo.value.currentTime = currentTime
    if (progress > currentVideo.value.progress) {
      currentVideo.value.progress = progress
      // 更新所有视频的解锁状态
      checkVideoUnlockStatus()
    }
  }
}

const startProgressTracking = () => {
  if (progressInterval.value) {
    clearInterval(progressInterval.value)
  }
  progressInterval.value = setInterval(() => {
    if (currentVideo.value && videoPlayer.value) {
      // 每10秒保存一次进度（模拟API调用）
      console.log('保存进度:', currentVideo.value.title, currentVideo.value.progress)
    }
  }, 10000)
}

const handlePageChange = (page) => {
  currentPage.value = page
  fetchCourseVideos()
}
</script>

<style scoped>
/* 课程视频特定样式 */
.aspect-w-16 {
  position: relative;
  width: 100%;
  padding-bottom: 56.25%; /* 16:9 比例 */
}
.aspect-h-9 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
</style>
