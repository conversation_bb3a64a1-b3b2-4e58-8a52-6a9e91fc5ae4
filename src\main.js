import './assets/main.css'

import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'

// 设置全局时区为东八区
// 这会影响Date对象的默认行为
if (typeof process !== 'undefined' && process.env) {
  process.env.TZ = 'Asia/Shanghai'
}

// 设置Element Plus的时区配置
const elementPlusConfig = {
  locale: zhCn,
  // 设置日期时间组件的默认时区
  zIndex: 3000,
  // 可以在这里添加更多全局配置
}

const app = createApp(App)

// 全局属性：提供时区工具函数
app.config.globalProperties.$timezone = {
  // 可以在组件中通过 this.$timezone 访问
  getBeijingTime: () => {
    const now = new Date()
    const utc = now.getTime() + (now.getTimezoneOffset() * 60000)
    return new Date(utc + (8 * 3600000))
  },
  formatBeijingTime: (date, format = 'datetime') => {
    if (!date) return ''
    const options = {
      timeZone: 'Asia/Shanghai',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    }
    if (format === 'datetime' || format === 'time') {
      options.hour = '2-digit'
      options.minute = '2-digit'
      options.second = '2-digit'
      options.hour12 = false
    }
    return new Date(date).toLocaleString('zh-CN', options)
  }
}

app
  .use(ElementPlus, elementPlusConfig)
  .use(router)
  .use(createPinia())
  .mount('#app')
