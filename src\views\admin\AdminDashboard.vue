<template>
  <div class="bg-gray-100 min-h-screen">
    <header
      class="bg-blue-600 text-white py-4 px-6 flex justify-between items-center shadow-md fixed top-0 left-0 right-0 z-20 h-16"
    >
      <h1 class="text-2xl font-bold">DFHDemo - 超级管理员</h1>
      <div>
        <router-link to="/admin/statistics" class="mr-4 hover:text-blue-200">数据统计</router-link>
        <LogoutButton />
      </div>
    </header>
    <div class="flex">
      <aside
        class="bg-white w-64 h-screen p-5 shadow-md fixed left-0 top-16 bottom-0 z-10 overflow-y-auto"
      >
        <el-menu
          :default-active="activeMenu"
          class="h-full"
          router
          :unique-opened="true"
          background-color="#f5f7fa"
          text-color="#333"
          active-text-color="#409EFF"
        >
          <el-menu-item index="/admin">首页</el-menu-item>
          <el-menu-item index="/admin/agencies">中介机构管理</el-menu-item>
          <el-menu-item index="/admin/brokers">经纪人管理</el-menu-item>
          <el-menu-item index="/admin/users">账号管理</el-menu-item>
          <el-menu-item index="/admin/article-categories">文章分类管理</el-menu-item>
          <el-menu-item index="/admin/articles">文章管理</el-menu-item>
          <el-menu-item index="/admin/courses">课程管理</el-menu-item>
          <el-menu-item index="/admin/training-plans">培训计划管理</el-menu-item>
          <el-menu-item index="/admin/credit-archives">信用档案管理</el-menu-item>
          <el-menu-item index="/admin/banners">广告横幅管理</el-menu-item>
          <el-menu-item index="/admin/orders">订单管理</el-menu-item>
          <el-menu-item index="/admin/statistics">数据统计</el-menu-item>
        </el-menu>
      </aside>
      <main class="flex-1 overflow-auto ml-64 mt-16" style="height: calc(100vh - 4rem)">
        <router-view />
      </main>
    </div>
  </div>
</template>

<script setup>
// 超级管理员仪表板逻辑
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import LogoutButton from '@/components/LogoutButton.vue'

const route = useRoute()
const activeMenu = computed(() => {
  // 如果是首页，返回 /admin
  if (route.path === '/admin') return '/admin'

  // 其他页面的匹配逻辑
  if (route.path.startsWith('/admin/agencies')) return '/admin/agencies'
  if (route.path.startsWith('/admin/brokers')) return '/admin/brokers'
  if (route.path.startsWith('/admin/users')) return '/admin/users'
  if (route.path.startsWith('/admin/article-categories')) return '/admin/article-categories'
  if (route.path.startsWith('/admin/articles')) return '/admin/articles'
  if (route.path.startsWith('/admin/courses')) return '/admin/courses'
  if (route.path.startsWith('/admin/training-plans')) return '/admin/training-plans'
  if (route.path.startsWith('/admin/credit-archives')) return '/admin/credit-archives'
  if (route.path.startsWith('/admin/banners')) return '/admin/banners'
  if (route.path.startsWith('/admin/orders')) return '/admin/orders'
  if (route.path.startsWith('/admin/statistics')) return '/admin/statistics'

  return route.path
})
</script>

<style scoped>
main {
  /* 让右侧内容区高度自适应且可滚动 */
  height: calc(100vh - 4rem);
}
</style>
