<template>
  <div class="bg-gray-100 min-h-screen flex flex-col">
    <header class="bg-blue-600 text-white py-4 px-6 flex justify-between items-center shadow-md">
      <h1 class="text-xl md:text-2xl font-bold">DFHDemo - 经纪人</h1>
      <div>
        <LogoutButton />
      </div>
    </header>
    <div class="flex flex-col md:flex-row flex-grow">
      <aside class="bg-white w-full md:w-64 h-auto md:h-screen p-5 shadow-md overflow-y-auto">
        <nav>
          <h2 class="text-lg font-semibold mb-4 hidden md:block">菜单</h2>
          <ul class="flex flex-col space-y-2 md:space-y-0 md:block">
            <li class="mb-2">
              <router-link
                to="/broker/training-plans"
                class="block p-2 rounded hover:bg-blue-50 text-sm md:text-base"
                >培训缴费</router-link
              >
            </li>
            <li class="mb-2">
              <router-link
                to="/broker/courses"
                class="block p-2 rounded hover:bg-blue-50 text-sm md:text-base"
                >课程学习</router-link
              >
            </li>
            <li class="mb-2">
              <router-link
                to="/broker/certificates"
                class="block p-2 rounded hover:bg-blue-50 text-sm md:text-base"
                >证书查看</router-link
              >
            </li>
            <li class="mb-2">
              <router-link
                to="/broker/credit-archives"
                class="block p-2 rounded hover:bg-blue-50 text-sm md:text-base"
                >信用档案</router-link
              >
            </li>
            <li class="mb-2">
              <router-link
                to="/broker/change-password"
                class="block p-2 rounded hover:bg-blue-50 text-sm md:text-base"
                >修改密码</router-link
              >
            </li>
          </ul>
        </nav>
      </aside>
      <main class="flex-1 p-4 md:p-6 overflow-auto">
        <router-view />
      </main>
    </div>
  </div>
</template>

<script setup>
// 经纪人仪表板逻辑
import LogoutButton from '@/components/LogoutButton.vue'
</script>

<style scoped>
/* 经纪人特定样式 */
@media (max-width: 768px) {
  aside {
    display: flex;
    flex-direction: row;
    overflow-x: auto;
    white-space: nowrap;
    padding: 0.5rem;
  }
  nav ul {
    display: flex;
    flex-direction: row;
    space-y: 0;
  }
  nav ul li {
    margin-right: 0.5rem;
    margin-bottom: 0;
  }
  nav ul li a {
    padding: 0.5rem;
  }
}
</style>
