import request from '@/utils/request'

// 超级管理员经纪人管理相关API

// 获取经纪人列表
export function getAdminBrokerList(params) {
    return request.get('/admin/brokers', { params })
}

// 获取经纪人详情
export function getAdminBrokerDetail(id) {
    return request.get(`/admin/brokers/${id}`)
}

// 审核经纪人
export function verifyBroker(id, status, reject_reason = '') {
    return request.post(`/admin/brokers/${id}/verify`, {
        status,
        reject_reason
    })
}

// 审核通过经纪人
export function approveBroker(id) {
    return verifyBroker(id, 'approved')
}

// 驳回经纪人申请
export function rejectBroker(id, reject_reason) {
    return verifyBroker(id, 'rejected', reject_reason)
}
