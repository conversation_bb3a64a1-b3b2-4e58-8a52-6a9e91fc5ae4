<template>
  <div class="bg-white p-6 rounded-lg shadow-md">
    <h2 class="text-2xl font-bold mb-4">课程学习</h2>
    <el-table :data="courses" border style="width: 100%" class="w-full">
      <el-table-column prop="id" label="ID" width="80"></el-table-column>
      <el-table-column prop="title" label="课程标题" min-width="200"></el-table-column>
      <el-table-column prop="progress" label="学习进度" width="120">
        <template #default="scope">
          <el-progress :percentage="scope.row.progress" :format="formatProgress"></el-progress>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120">
        <template #default="scope">
          <el-button size="small" type="primary" @click="handleViewVideos(scope.row)"
            >查看视频</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="currentPage"
      :page-size="pageSize"
      :total="total"
      layout="total, prev, pager, next"
      class="mt-4"
      @current-change="handlePageChange"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const courses = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

onMounted(() => {
  fetchCourses()
})

const fetchCourses = () => {
  // 模拟数据
  courses.value = [
    { id: 1, title: '房产中介基础课程', progress: 60 },
    { id: 2, title: '房产中介进阶课程', progress: 20 },
    // 更多数据...
  ]
  total.value = courses.value.length
}

const formatProgress = (percentage) => {
  return `${percentage}%`
}

const handleViewVideos = (row) => {
  router.push(`/broker/courses/${row.id}/videos`)
}

const handlePageChange = (page) => {
  currentPage.value = page
  fetchCourses()
}
</script>

<style scoped>
/* 课程学习特定样式 */
</style>
