<template>
  <div class="bg-white p-6 rounded-lg shadow-md mx-auto max-w-4xl">
    <h2 class="text-2xl font-bold mb-4">文章列表</h2>
    <div class="mb-4 flex flex-wrap gap-2">
      <el-select
        v-model="selectedCategory"
        placeholder="选择分类"
        clearable
        @change="handleCategoryChange"
        class="w-full md:w-48"
      >
        <el-option
          v-for="category in categories"
          :key="category.id"
          :label="category.name"
          :value="category.id"
        ></el-option>
      </el-select>
    </div>
    <el-table :data="articles" border style="width: 100%" class="w-full" :show-header="false">
      <el-table-column prop="title" label="标题" min-width="200">
        <template #default="scope">
          <div class="flex flex-col">
            <router-link
              :to="{ path: `/cms/articles/${scope.row.id}` }"
              class="text-blue-600 hover:underline font-semibold"
              >{{ scope.row.title }}</router-link
            >
            <span class="text-gray-500 text-sm"
              >{{ scope.row.created_at }} | {{ scope.row.category_name }}</span
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="currentPage"
      :page-size="pageSize"
      :total="total"
      layout="total, prev, pager, next"
      class="mt-4 flex justify-center"
      @current-change="handlePageChange"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()
const articles = ref([])
const categories = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const selectedCategory = ref(null)

onMounted(() => {
  fetchCategories()
  if (route.query.categoryId) {
    selectedCategory.value = parseInt(route.query.categoryId)
  }
  fetchArticles()
})

watch(selectedCategory, (newVal) => {
  currentPage.value = 1
  fetchArticles()
})

const fetchCategories = () => {
  // 模拟数据
  categories.value = [
    { id: 1, name: '政策法规' },
    { id: 2, name: '行业动态' },
    { id: 3, name: '市场分析' },
    { id: 4, name: '培训资讯' },
    // 更多数据...
  ]
}

const fetchArticles = () => {
  // 模拟数据
  let filteredArticles = [
    {
      id: 1,
      title: '房产中介新政策解读',
      category_name: '政策法规',
      category_id: 1,
      created_at: '2023-01-01 10:00:00',
    },
    {
      id: 2,
      title: '行业发展趋势分析',
      category_name: '行业动态',
      category_id: 2,
      created_at: '2023-01-02 11:00:00',
    },
    {
      id: 3,
      title: '房产市场月度报告',
      category_name: '市场分析',
      category_id: 3,
      created_at: '2023-01-03 09:00:00',
    },
    {
      id: 4,
      title: '培训课程更新通知',
      category_name: '培训资讯',
      category_id: 4,
      created_at: '2023-01-04 14:00:00',
    },
    // 更多数据...
  ]

  if (selectedCategory.value) {
    filteredArticles = filteredArticles.filter(
      (article) => article.category_id === selectedCategory.value,
    )
  }

  total.value = filteredArticles.length
  const startIndex = (currentPage.value - 1) * pageSize.value
  const endIndex = startIndex + pageSize.value
  articles.value = filteredArticles.slice(startIndex, endIndex)
}

const handleCategoryChange = (value) => {
  router.push({ path: '/cms/articles', query: { categoryId: value } })
  selectedCategory.value = value
}

const handlePageChange = (page) => {
  currentPage.value = page
  fetchArticles()
}
</script>

<style scoped>
/* 文章列表特定样式 */
</style>
