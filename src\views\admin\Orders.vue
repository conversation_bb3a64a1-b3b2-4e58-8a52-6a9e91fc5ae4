<template>
  <div class="bg-white p-6 rounded-lg shadow-md">
    <h2 class="text-2xl font-bold mb-4">订单管理</h2>
    <el-table :data="orders" border style="width: 100%">
      <el-table-column prop="id" label="ID" width="80"></el-table-column>
      <el-table-column prop="broker_name" label="经纪人" width="120"></el-table-column>
      <el-table-column prop="training_plan_title" label="培训计划" width="300"></el-table-column>
      <el-table-column prop="amount" label="金额（元）" width="100"></el-table-column>
      <el-table-column prop="status" label="状态" width="100"></el-table-column>
      <el-table-column prop="created_at" label="创建时间" width="180"></el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button size="small" @click="handleView(scope.row)">查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="currentPage"
      :page-size="pageSize"
      :total="total"
      layout="total, prev, pager, next"
      class="mt-4"
      @current-change="handlePageChange"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const orders = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

onMounted(() => {
  fetchOrders()
})

const fetchOrders = () => {
  // 模拟数据
  orders.value = [
    {
      id: 1,
      broker_name: '张三',
      training_plan_title: '2023年度房产中介培训计划',
      amount: 500.0,
      status: '待支付',
      created_at: '2023-01-01 10:00:00',
    },
    {
      id: 2,
      broker_name: '李四',
      training_plan_title: '2023年度房产中介进阶培训',
      amount: 800.0,
      status: '已完成',
      created_at: '2023-01-02 11:00:00',
    },
    // 更多数据...
  ]
  total.value = orders.value.length
}

const handleView = (row) => {
  router.push(`/admin/orders/${row.id}`)
}

const handlePageChange = (page) => {
  currentPage.value = page
  fetchOrders()
}
</script>

<style scoped>
/* 订单管理特定样式 */
</style>
