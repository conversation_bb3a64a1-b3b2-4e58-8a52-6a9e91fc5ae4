# 经纪人管理功能测试指南

## 测试环境准备

1. 确保后端API服务运行在 `http://127.0.0.1:8000`
2. 确保前端开发服务器运行在 `http://localhost:5174`
3. 准备测试用的图片文件（身份证照片和证书照片）

## 测试步骤

### 1. 登录测试
1. 访问 `http://localhost:5174`
2. 使用机构管理员账户登录
3. 确保获得有效的认证token

### 2. 访问经纪人管理页面
1. 登录成功后，导航到机构管理员界面
2. 点击"经纪人管理"菜单或直接访问 `/agency/brokers`
3. 验证页面正常加载

### 3. 经纪人列表功能测试
1. **列表加载测试**
   - 页面应显示经纪人列表表格
   - 如果API可用，应显示真实数据
   - 如果API不可用，应显示模拟数据并有错误提示

2. **分页功能测试**
   - 验证分页组件正常显示
   - 测试页码切换功能

### 4. 添加经纪人功能测试
1. **打开添加对话框**
   - 点击"添加经纪人"按钮
   - 验证弹窗正常打开
   - 验证表单字段完整

2. **表单验证测试**
   - 尝试提交空表单，验证必填项验证
   - 输入无效数据，验证格式验证

3. **文件上传测试**
   - 上传身份证照片（测试图片格式限制）
   - 上传证书照片（测试文件大小限制）
   - 验证文件列表正确显示

4. **提交测试**
   - 填写完整表单信息
   - 上传必要文件
   - 点击确定提交
   - 验证API调用和响应处理

### 5. 编辑功能测试
1. 点击列表中的"编辑"按钮
2. 验证弹窗打开并预填数据
3. 注意：编辑功能暂未完全实现

### 6. 删除功能测试
1. 点击列表中的"删除"按钮
2. 验证确认对话框显示
3. 确认删除，验证列表更新
4. 注意：删除功能仅在前端实现

## 预期结果

### 成功场景
- 页面正常加载，无JavaScript错误
- 列表数据正确显示
- 添加经纪人表单提交成功
- 文件上传功能正常
- 用户反馈信息准确

### 错误处理场景
- API不可用时显示错误信息并使用模拟数据
- 表单验证错误时显示相应提示
- 文件上传限制生效时显示警告
- 网络错误时显示友好的错误信息

## 测试数据示例

```javascript
// 测试用经纪人数据
{
  name: "张三",
  id_card: "110101199001011234",
  phone: "13800138000",
  certificate_type: "broker",
  certificate_number: "CERT001"
}
```

## 常见问题排查

1. **401未认证错误**
   - 检查是否正确登录
   - 验证token是否有效
   - 确认用户角色为agency_admin

2. **文件上传失败**
   - 检查文件格式是否为图片
   - 验证文件大小是否超过10MB
   - 确认网络连接正常

3. **API调用失败**
   - 检查后端服务是否运行
   - 验证API接口地址是否正确
   - 查看浏览器网络面板的错误信息

## 注意事项

- 当前实现基于提供的API接口文档
- 编辑和删除功能受限于后端接口支持
- 文件上传使用FormData格式
- 所有用户操作都有相应的反馈信息
