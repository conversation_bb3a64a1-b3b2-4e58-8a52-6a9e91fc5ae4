<template>
  <div class="bg-white p-6 rounded-lg shadow-md">
    <h2 class="text-2xl font-bold mb-4">信用档案查看</h2>
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="企业档案" name="agency"></el-tab-pane>
      <el-tab-pane label="经纪人档案" name="broker"></el-tab-pane>
    </el-tabs>
    <el-table :data="archives" border style="width: 100%">
      <el-table-column prop="id" label="ID" width="80"></el-table-column>
      <el-table-column prop="entity_name" label="主体名称" width="200"></el-table-column>
      <el-table-column prop="type" label="档案类型" width="100"></el-table-column>
      <el-table-column prop="title" label="标题" width="300"></el-table-column>
      <el-table-column prop="created_at" label="创建时间" width="180"></el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button size="small" @click="handleView(scope.row)">查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="currentPage"
      :page-size="pageSize"
      :total="total"
      layout="total, prev, pager, next"
      class="mt-4"
      @current-change="handlePageChange"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const archives = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const activeTab = ref('agency')

onMounted(() => {
  fetchArchives(activeTab.value)
})

const fetchArchives = (tab = 'agency') => {
  // 模拟数据
  if (tab === 'agency') {
    archives.value = [
      {
        id: 1,
        entity_name: '中介公司A',
        type: '红榜',
        title: '优秀中介公司表彰',
        created_at: '2023-01-01 10:00:00',
      },
      // 更多数据...
    ]
  } else {
    archives.value = [
      {
        id: 2,
        entity_name: '张三',
        type: '黑榜',
        title: '违规操作处罚通知',
        created_at: '2023-01-02 11:00:00',
      },
      // 更多数据...
    ]
  }
  total.value = archives.value.length
}

const handleTabClick = (tab) => {
  activeTab.value = tab.props.name
  fetchArchives(tab.props.name)
}

const handleView = (row) => {
  // 查看详情逻辑
  console.log('查看信用档案详情:', row)
}

const handlePageChange = (page) => {
  currentPage.value = page
  fetchArchives(activeTab.value)
}
</script>

<style scoped>
/* 信用档案查看特定样式 */
</style>
