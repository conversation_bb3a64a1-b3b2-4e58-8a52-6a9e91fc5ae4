<template>
  <div class="bg-white p-6 rounded-lg shadow-md">
    <h2 class="text-2xl font-bold mb-4">信用档案查看</h2>
    <el-table :data="archives" border style="width: 100%" class="w-full">
      <el-table-column prop="id" label="ID" width="80"></el-table-column>
      <el-table-column prop="type" label="档案类型" width="100"></el-table-column>
      <el-table-column prop="title" label="标题" min-width="200"></el-table-column>
      <el-table-column prop="created_at" label="创建时间" width="180"></el-table-column>
      <el-table-column label="操作" width="120">
        <template #default="scope">
          <el-button size="small" @click="handleView(scope.row)">查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="currentPage"
      :page-size="pageSize"
      :total="total"
      layout="total, prev, pager, next"
      class="mt-4"
      @current-change="handlePageChange"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const archives = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

onMounted(() => {
  fetchArchives()
})

const fetchArchives = () => {
  // 模拟数据
  archives.value = [
    { id: 1, type: '红榜', title: '优秀经纪人表彰', created_at: '2023-01-01 10:00:00' },
    // 更多数据...
  ]
  total.value = archives.value.length
}

const handleView = (row) => {
  // 查看详情逻辑
  console.log('查看信用档案详情:', row)
}

const handlePageChange = (page) => {
  currentPage.value = page
  fetchArchives()
}
</script>

<style scoped>
/* 信用档案查看特定样式 */
</style>
