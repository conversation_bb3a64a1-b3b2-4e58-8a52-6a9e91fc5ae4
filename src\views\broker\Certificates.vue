<template>
  <div class="bg-white p-6 rounded-lg shadow-md">
    <h2 class="text-2xl font-bold mb-4">证书查看</h2>
    <el-table :data="certificates" border style="width: 100%" class="w-full">
      <el-table-column prop="id" label="ID" width="80"></el-table-column>
      <el-table-column
        prop="training_plan_title"
        label="培训计划"
        min-width="200"
      ></el-table-column>
      <el-table-column prop="certificate_number" label="证书编号" width="150"></el-table-column>
      <el-table-column prop="created_at" label="颁发时间" width="180"></el-table-column>
      <el-table-column label="操作" width="150">
        <template #default="scope">
          <el-button size="small" type="primary" @click="handleViewImage(scope.row)"
            >查看证书</el-button
          >
          <el-button size="small" type="success" @click="handleViewQRCode(scope.row)"
            >二维码</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="currentPage"
      :page-size="pageSize"
      :total="total"
      layout="total, prev, pager, next"
      class="mt-4 flex justify-center"
      @current-change="handlePageChange"
    />
    <el-dialog v-model="imageDialogVisible" title="证书图片" width="90%" :max-width="'800px'">
      <div class="flex justify-center">
        <el-image
          :src="currentCertificateImage"
          style="width: 100%; max-height: 500px"
          fit="contain"
        ></el-image>
      </div>
      <template #footer>
        <el-button @click="imageDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
    <el-dialog v-model="qrCodeDialogVisible" title="证书二维码" width="90%" :max-width="'400px'">
      <div class="flex flex-col items-center justify-center">
        <div id="qrcode" ref="qrCodeEl" class="w-64 h-64"></div>
        <p class="mt-2 text-center">扫描二维码查看证书详情</p>
      </div>
      <template #footer>
        <el-button @click="qrCodeDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import QRCode from 'qrcodejs2'

const certificates = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const imageDialogVisible = ref(false)
const qrCodeDialogVisible = ref(false)
const currentCertificateImage = ref('')
const currentCertificateId = ref(null)
const qrCodeEl = ref(null)

onMounted(() => {
  fetchCertificates()
})

const fetchCertificates = () => {
  // 模拟数据
  certificates.value = [
    {
      id: 1,
      training_plan_title: '2023年度房产中介培训计划',
      certificate_number: 'CERT2023001',
      created_at: '2023-03-01 10:00:00',
      image_url: 'https://via.placeholder.com/800x400',
    },
    // 更多数据...
  ]
  total.value = certificates.value.length
}

const handleViewImage = (row) => {
  currentCertificateImage.value = row.image_url
  imageDialogVisible.value = true
}

const handleViewQRCode = async (row) => {
  currentCertificateId.value = row.id
  qrCodeDialogVisible.value = true
  await nextTick()
  generateQRCode(row.id)
}

const generateQRCode = (certificateId) => {
  if (qrCodeEl.value) {
    qrCodeEl.value.innerHTML = ''
    new QRCode(qrCodeEl.value, {
      text: `${window.location.origin}/broker/certificates/${certificateId}/qrcode`,
      width: 256,
      height: 256,
      colorDark: '#000000',
      colorLight: '#ffffff',
      correctLevel: QRCode.CorrectLevel.H,
    })
  }
}

const handlePageChange = (page) => {
  currentPage.value = page
  fetchCertificates()
}
</script>

<style scoped>
/* 证书查看特定样式 */
</style>
