<template>
  <div class="bg-white p-6 rounded-lg shadow-md">
    <h2 class="text-2xl font-bold mb-4">培训缴费</h2>
    <el-table :data="plans" border style="width: 100%" class="w-full">
      <el-table-column prop="id" label="ID" width="80"></el-table-column>
      <el-table-column prop="title" label="培训计划名称" min-width="200"></el-table-column>
      <el-table-column prop="price" label="培训费用（元）" width="120"></el-table-column>
      <el-table-column prop="deadline" label="缴费截止时间" width="180"></el-table-column>
      <el-table-column prop="status" label="状态" width="100"></el-table-column>
      <el-table-column label="操作" width="120">
        <template #default="scope">
          <el-button
            size="small"
            type="primary"
            @click="handlePay(scope.row)"
            :disabled="scope.row.status !== '待支付'"
            >支付</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="currentPage"
      :page-size="pageSize"
      :total="total"
      layout="total, prev, pager, next"
      class="mt-4"
      @current-change="handlePageChange"
    />
    <el-dialog v-model="payDialogVisible" title="支付培训费用" width="90%" :max-width="'500px'">
      <el-form :model="payForm" :rules="payRules" ref="payFormRef">
        <el-form-item label="培训计划" prop="title">
          <el-input v-model="payForm.title" disabled></el-input>
        </el-form-item>
        <el-form-item label="金额（元）" prop="amount">
          <el-input-number
            v-model="payForm.amount"
            disabled
            :min="0"
            :precision="2"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="支付方式" prop="paymentMethod">
          <el-select v-model="payForm.paymentMethod" placeholder="请选择支付方式">
            <el-option label="微信支付" value="wechat"></el-option>
            <el-option label="支付宝" value="alipay"></el-option>
            <el-option label="银行卡" value="bank"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="payDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleConfirmPay">确认支付</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const plans = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const payDialogVisible = ref(false)
const payForm = ref({ title: '', amount: 0, paymentMethod: '' })
const payFormRef = ref(null)
const payRules = {
  paymentMethod: [{ required: true, message: '请选择支付方式', trigger: 'change' }],
}

onMounted(() => {
  fetchPlans()
})

const fetchPlans = () => {
  // 模拟数据
  plans.value = [
    {
      id: 1,
      title: '2023年度房产中介培训计划',
      price: 500.0,
      deadline: '2023-12-31 23:59:59',
      status: '待支付',
    },
    {
      id: 2,
      title: '2023年度房产中介进阶培训',
      price: 800.0,
      deadline: '2023-11-30 23:59:59',
      status: '已完成',
    },
    // 更多数据...
  ]
  total.value = plans.value.length
}

const handlePay = (row) => {
  payForm.value = { title: row.title, amount: row.price, paymentMethod: '' }
  payDialogVisible.value = true
}

const handleConfirmPay = () => {
  payFormRef.value.validate((valid) => {
    if (valid) {
      // 支付逻辑
      console.log('支付信息:', payForm.value)
      payDialogVisible.value = false
      // 模拟支付成功后更新状态
      const index = plans.value.findIndex((p) => p.title === payForm.value.title)
      if (index !== -1) {
        plans.value[index].status = '已完成'
      }
    }
  })
}

const handlePageChange = (page) => {
  currentPage.value = page
  fetchPlans()
}
</script>

<style scoped>
/* 培训缴费特定样式 */
</style>
