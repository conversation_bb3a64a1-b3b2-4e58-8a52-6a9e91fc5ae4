<template>
  <div class="p-8">
    <h1 class="text-2xl font-bold mb-6">分类数据测试</h1>
    
    <div class="mb-6">
      <button @click="testGetCategories" class="bg-blue-500 text-white px-4 py-2 rounded mr-4">
        测试获取分类
      </button>
      <button @click="testGetCategoriesWithArticles" class="bg-green-500 text-white px-4 py-2 rounded mr-4">
        测试获取分类及文章
      </button>
      <button @click="testGetCategoryArticles" class="bg-purple-500 text-white px-4 py-2 rounded">
        测试获取分类文章
      </button>
    </div>

    <div v-if="loading" class="text-blue-600">
      加载中...
    </div>

    <div v-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
      错误: {{ error }}
    </div>

    <div v-if="result" class="bg-gray-100 p-4 rounded">
      <h3 class="font-bold mb-2">API响应结果:</h3>
      <pre class="text-sm overflow-auto">{{ JSON.stringify(result, null, 2) }}</pre>
    </div>

    <div v-if="categories.length > 0" class="mt-6">
      <h3 class="font-bold mb-4">解析后的分类数据:</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div v-for="category in categories" :key="category.id" class="bg-white p-4 rounded border">
          <h4 class="font-semibold text-lg">{{ category.name }}</h4>
          <p class="text-sm text-gray-600">ID: {{ category.id }}</p>
          <p class="text-sm text-gray-600">创建时间: {{ category.created_at }}</p>
          <div v-if="category.articles && category.articles.length > 0" class="mt-2">
            <p class="text-sm font-medium">文章 ({{ category.articles.length }}篇):</p>
            <ul class="text-xs text-gray-500 mt-1">
              <li v-for="article in category.articles.slice(0, 3)" :key="article.id">
                • {{ article.title }}
              </li>
              <li v-if="category.articles.length > 3" class="text-blue-500">
                ... 还有 {{ category.articles.length - 3 }} 篇
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { getCategories, getCategoriesWithArticles, getArticlesByCategory } from '@/api/cms'

const loading = ref(false)
const error = ref('')
const result = ref(null)
const categories = ref([])

const testGetCategories = async () => {
  loading.value = true
  error.value = ''
  result.value = null
  categories.value = []
  
  try {
    const response = await getCategories()
    result.value = response
    
    // 处理响应数据
    const categoriesData = Array.isArray(response) ? response : (response.data || [])
    categories.value = categoriesData
    
    console.log('分类数据:', categoriesData)
  } catch (err) {
    error.value = err.message || '获取分类失败'
    console.error('获取分类失败:', err)
  } finally {
    loading.value = false
  }
}

const testGetCategoriesWithArticles = async () => {
  loading.value = true
  error.value = ''
  result.value = null
  categories.value = []
  
  try {
    const response = await getCategoriesWithArticles({ per_page: 10 })
    result.value = response
    
    // 处理响应数据
    const categoriesData = Array.isArray(response) ? response : (response.data || [])
    categories.value = categoriesData
    
    console.log('分类及文章数据:', categoriesData)
  } catch (err) {
    error.value = err.message || '获取分类及文章失败'
    console.error('获取分类及文章失败:', err)
  } finally {
    loading.value = false
  }
}

const testGetCategoryArticles = async () => {
  loading.value = true
  error.value = ''
  result.value = null
  
  try {
    // 先获取分类
    const categoriesResponse = await getCategories()
    const categoriesData = Array.isArray(categoriesResponse) ? categoriesResponse : (categoriesResponse.data || [])
    
    if (categoriesData.length > 0) {
      const firstCategory = categoriesData[0]
      const articlesResponse = await getArticlesByCategory(firstCategory.id, { page: 1, per_page: 10 })
      
      result.value = {
        category: firstCategory,
        articles: articlesResponse
      }
      
      console.log('分类文章数据:', articlesResponse)
    } else {
      error.value = '没有找到分类'
    }
  } catch (err) {
    error.value = err.message || '获取分类文章失败'
    console.error('获取分类文章失败:', err)
  } finally {
    loading.value = false
  }
}
</script>
