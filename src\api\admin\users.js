import request from '@/utils/request'

/**
 * 用户管理相关API接口
 */

/**
 * 获取用户列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.per_page - 每页数量
 * @param {string} params.role - 角色筛选
 * @param {string} params.status - 状态筛选
 * @param {string} params.keyword - 搜索关键词
 * @returns {Promise}
 */
export function getUserList(params = {}) {
  return request.get('/admin/users', { params })
}

/**
 * 获取用户详情
 * @param {number} id - 用户ID
 * @returns {Promise}
 */
export function getUserDetail(id) {
  return request.get(`/admin/users/${id}`)
}

/**
 * 修改用户密码
 * @param {number} id - 用户ID
 * @param {string} newPassword - 新密码
 * @returns {Promise}
 */
export function changeUserPassword(id, newPassword) {
  return request.post(`/admin/users/${id}/change-password`, {
    new_password: newPassword
  })
}

/**
 * 更新用户状态
 * @param {number} id - 用户ID
 * @param {string} status - 新状态 (approved/pending/rejected)
 * @returns {Promise}
 */
export function updateUserStatus(id, status) {
  return request.post(`/admin/users/${id}/status`, {
    status
  })
}

/**
 * 删除用户（软删除）
 * @param {number} id - 用户ID
 * @returns {Promise}
 */
export function deleteUser(id) {
  return request.delete(`/admin/users/${id}`)
}

// 角色类型常量
export const USER_ROLES = {
  super_admin: 'super_admin',
  agency_admin: 'agency_admin',
  broker: 'broker'
}

// 状态类型常量
export const USER_STATUS = {
  approved: 'approved',
  pending: 'pending',
  rejected: 'rejected'
}

// 角色标签映射
export const USER_ROLE_LABELS = {
  super_admin: '超级管理员',
  agency_admin: '企业管理员',
  broker: '经纪人'
}

// 状态标签映射
export const USER_STATUS_LABELS = {
  approved: '已审核',
  pending: '待审核',
  rejected: '已拒绝'
}
