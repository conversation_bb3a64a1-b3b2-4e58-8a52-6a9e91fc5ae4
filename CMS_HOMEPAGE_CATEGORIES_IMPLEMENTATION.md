# CMS首页文章分类展示功能实现

## 🎯 实现目标

根据API实现首页文章分类及各分类下前10篇文章的展示，恢复之前的card展示方式，所有分类都予以展示。

## ✅ 主要实现内容

### 1. API接口扩展

#### 1.1 新增API接口 (`src/api/cms.js`)

```javascript
// 获取所有分类及其文章列表（用于首页展示）
export function getCategoriesWithArticles(params = {}) {
  const defaultParams = {
    page: 1,
    per_page: 10,
    ...params,
  }
  return request.get('/cms/categories-with-articles', { params: defaultParams })
}
```

**API设计说明**:

- **主接口**: `/cms/categories-with-articles` - 一次性获取所有分类及其文章
- **降级接口**: 分别调用 `/cms/categories` 和 `/cms/categories/{id}/articles`
- **参数**: `per_page=10` 获取每个分类的前10篇文章

#### 1.2 API数据格式

**期望的API响应格式**:

```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "阳光规划",
      "description": "城市规划公开信息",
      "articles": [
        {
          "id": 1,
          "title": "菏泽市自然资源和规划局关于鲁中车辆检测有限公司建设项目规划许可的公示",
          "summary": "关于鲁中车辆检测有限公司建设项目的规划许可公示",
          "created_at": "2025-07-17 10:30:00",
          "updated_at": "2025-07-17 10:30:00"
        }
        // ... 更多文章（最多10篇）
      ]
    },
    {
      "id": 2,
      "name": "政策法规",
      "description": "政策法规文件",
      "articles": [
        // ... 文章列表
      ]
    },
    {
      "id": 3,
      "name": "政策解读",
      "description": "政策解读说明",
      "articles": [
        // ... 文章列表
      ]
    }
  ]
}
```

### 2. 数据获取逻辑优化

#### 2.1 主要获取逻辑

```javascript
// 获取分类及文章数据
getCategoriesWithArticles({ per_page: 10 })
  .then((response) => {
    categories.value = response.data || []
    console.log('分类及文章数据获取成功:', categories.value)
  })
  .catch((error) => {
    console.error('获取分类及文章数据失败:', error)
    // 降级：分别获取分类和文章
    fetchCategoriesAndArticlesSeparately()
  })
```

#### 2.2 降级处理机制

```javascript
// 降级处理：分别获取分类和文章
const fetchCategoriesAndArticlesSeparately = async () => {
  try {
    // 先获取分类列表
    const categoriesResponse = await getCategories()
    const categoriesList = categoriesResponse.data || []

    // 为每个分类获取前10篇文章
    const categoriesWithArticles = await Promise.allSettled(
      categoriesList.map(async (category) => {
        try {
          const articlesResponse = await getArticlesByCategory(category.id, {
            page: 1,
            per_page: 10,
          })
          return {
            ...category,
            articles: articlesResponse.data || [],
          }
        } catch (error) {
          console.error(`获取分类 ${category.name} 的文章失败:`, error)
          return {
            ...category,
            articles: [],
          }
        }
      }),
    )

    // 处理结果
    categories.value = categoriesWithArticles
      .map((result) => (result.status === 'fulfilled' ? result.value : result.reason))
      .filter((category) => category && category.id)

    console.log('分别获取分类和文章数据成功:', categories.value)
  } catch (error) {
    console.error('分别获取分类和文章数据失败:', error)
    loadMockCategories()
    console.log('使用模拟分类数据:', categories.value)
  }
}
```

### 3. 模拟数据完善

#### 3.1 完整的模拟数据结构

为了更好地展示效果，每个分类都包含10篇文章：

**阳光规划分类** (10篇文章):

```javascript
{
  id: 1,
  name: '阳光规划',
  description: '城市规划公开信息',
  articles: [
    {
      id: 1,
      title: '菏泽市自然资源和规划局关于鲁中车辆检测有限公司建设项目规划许可的公示',
      created_at: '2025-07-17'
    },
    // ... 共10篇文章
  ]
}
```

**政策法规分类** (10篇文章):

```javascript
{
  id: 2,
  name: '政策法规',
  description: '政策法规文件',
  articles: [
    {
      id: 11,
      title: '菏泽市自然资源和规划局等11部门关于进一步加强房地产市场监管的通知',
      created_at: '2025-05-20'
    },
    // ... 共10篇文章
  ]
}
```

**政策解读分类** (10篇文章):

```javascript
{
  id: 3,
  name: '政策解读',
  description: '政策解读说明',
  articles: [
    {
      id: 21,
      title: '解读：打好地质灾害"防治战"',
      created_at: '2025-05-21'
    },
    // ... 共10篇文章
  ]
}
```

### 4. 界面展示优化

#### 4.1 Card布局保持

```vue
<!-- 分类内容区域 -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
  <!-- 分类列表 -->
  <div v-for="category in categories" :key="category.id" :id="`category-${category.id}`"
    class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">

    <!-- 分类标题 -->
    <div class="border-b-2 border-blue-500 px-4 py-3 bg-gray-50">
      <h2 class="text-lg font-bold text-gray-900 flex items-center">
        <span class="w-1 h-6 bg-blue-500 mr-3"></span>
        {{ category.name }}
      </h2>
    </div>

    <!-- 文章列表 -->
    <div class="p-4">
      <ul class="space-y-3">
        <li v-for="article in category.articles" :key="article.id"
          @click="goToArticle(article.id)"
          class="cursor-pointer hover:bg-gray-50 p-2 rounded transition-colors group">
          <div class="flex items-start justify-between">
            <div class="flex-1 min-w-0">
              <div class="flex items-center mb-1">
                <span class="w-1 h-1 bg-blue-500 rounded-full mr-2 flex-shrink-0"></span>
                <h3 class="text-sm font-medium text-gray-900 group-hover:text-blue-600 truncate">
                  {{ article.title }}
                </h3>
              </div>
            </div>
            <span class="text-xs text-gray-500 ml-4 flex-shrink-0">
              {{ formatDate(article.created_at) }}
            </span>
          </div>
        </li>
      </ul>

      <!-- 查看更多链接 -->
      <div class="mt-4 pt-3 border-t border-gray-200">
        <a href="#" @click.prevent="viewMoreArticles(category.id)"
          class="text-sm text-blue-600 hover:text-blue-800 flex items-center justify-center">
          更多 {{ category.name }}
          <el-icon class="ml-1">
            <ArrowRight />
          </el-icon>
        </a>
      </div>
    </div>
  </div>
</div>
```

#### 4.2 时间格式优化

```javascript
const formatDate = (dateString) => {
  if (!dateString) return ''
  // 使用东八区时间格式化，只显示月-日
  const beijingTime = formatBeijingTime(dateString, 'date')
  if (beijingTime) {
    // 从 YYYY-MM-DD 格式中提取 MM-DD
    const parts = beijingTime.split('-')
    if (parts.length >= 3) {
      return `${parts[1]}-${parts[2]}`
    }
  }
  // 降级处理
  return new Date(dateString).toLocaleDateString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
  })
}
```

### 5. 用户交互功能

#### 5.1 文章点击跳转

```javascript
const goToArticle = (articleId) => {
  router.push({ name: 'cms-article-detail', params: { id: articleId } })
}
```

#### 5.2 查看更多功能

```javascript
const viewMoreArticles = (categoryId) => {
  router.push({ name: 'cms-category-articles', params: { id: categoryId } })
}
```

## 🎨 视觉效果

### 1. 布局特点

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                              Banner轮播图                                    │
└─────────────────────────────────────────────────────────────────────────────┘

┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
│ ▌ 阳光规划      │ │ ▌ 政策法规      │ │ ▌ 政策解读      │
├─────────────────┤ ├─────────────────┤ ├─────────────────┤
│ • 文章标题1  日期│ │ • 文章标题1  日期│ │ • 文章标题1  日期│
│ • 文章标题2  日期│ │ • 文章标题2  日期│ │ • 文章标题2  日期│
│ • 文章标题3  日期│ │ • 文章标题3  日期│ │ • 文章标题3  日期│
│ • 文章标题4  日期│ │ • 文章标题4  日期│ │ • 文章标题4  日期│
│ • 文章标题5  日期│ │ • 文章标题5  日期│ │ • 文章标题5  日期│
│ • 文章标题6  日期│ │ • 文章标题6  日期│ │ • 文章标题6  日期│
│ • 文章标题7  日期│ │ • 文章标题7  日期│ │ • 文章标题7  日期│
│ • 文章标题8  日期│ │ • 文章标题8  日期│ │ • 文章标题8  日期│
│ • 文章标题9  日期│ │ • 文章标题9  日期│ │ • 文章标题9  日期│
│ • 文章标题10 日期│ │ • 文章标题10 日期│ │ • 文章标题10 日期│
├─────────────────┤ ├─────────────────┤ ├─────────────────┤
│   更多 阳光规划 →│ │   更多 政策法规 →│ │   更多 政策解读 →│
└─────────────────┘ └─────────────────┘ └─────────────────┘
```

### 2. 交互效果

- **悬停效果**: 文章标题悬停时变为蓝色
- **点击效果**: 文章标题可点击跳转到详情页
- **更多链接**: 每个分类底部有"更多"链接
- **响应式**: 大屏幕3列，小屏幕1列

### 3. 样式特点

- **统一风格**: 与整体网站风格保持一致
- **清晰层次**: 分类标题、文章列表、更多链接层次分明
- **易于阅读**: 合适的字体大小和行间距
- **视觉引导**: 蓝色主题色引导用户操作

## 🔧 技术实现要点

### 1. 数据获取策略

**优先级策略**:

1. **首选**: 调用 `/cms/categories-with-articles` 一次性获取所有数据
2. **降级**: 分别调用分类API和文章API
3. **兜底**: 使用完整的模拟数据

### 2. 错误处理

**多层错误处理**:

- API级别错误处理
- 分类级别错误处理
- 全局降级处理

### 3. 性能优化

**优化措施**:

- 使用 `Promise.allSettled` 避免单个失败影响整体
- 每个分类限制10篇文章，避免数据过多
- 合理的加载状态提示

## 🧪 测试验证

### 1. 功能测试

**访问地址**: `http://localhost:5176/cms`

**测试项目**:

- [ ] 页面正常加载，显示3个分类卡片
- [ ] 每个分类显示10篇文章
- [ ] 文章标题可以点击
- [ ] "更多"链接可以点击
- [ ] 时间格式显示正确（MM-DD格式）
- [ ] 响应式布局正常

### 2. API测试

**真实API测试**:

- [ ] `/cms/categories-with-articles` 接口调用
- [ ] 降级API调用机制
- [ ] 模拟数据降级机制

### 3. 用户体验测试

**交互测试**:

- [ ] 悬停效果正常
- [ ] 点击跳转正常
- [ ] 加载状态提示
- [ ] 错误处理友好

## ✅ 实现效果

### 1. 数据展示

- ✅ 所有分类都予以展示
- ✅ 每个分类显示前10篇文章
- ✅ 恢复了card展示方式
- ✅ 时间格式使用东八区

### 2. 用户体验

- ✅ 清晰的分类布局
- ✅ 丰富的文章内容
- ✅ 流畅的交互体验
- ✅ 完善的错误处理

### 3. 技术实现

- ✅ 灵活的API调用策略
- ✅ 完善的降级机制
- ✅ 优化的性能表现
- ✅ 统一的时区处理

---

**实现状态**: ✅ 完成  
**展示方式**: Card布局，3列展示  
**数据来源**: API + 降级 + 模拟数据  
**测试地址**: `http://localhost:5176/cms`
