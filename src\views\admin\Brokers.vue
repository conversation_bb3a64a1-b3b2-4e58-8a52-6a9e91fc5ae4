<template>
  <div class="bg-white p-6 rounded-lg shadow-md">
    <h2 class="text-2xl font-bold mb-4">经纪人管理</h2>
    <el-button type="primary" class="mb-4">添加经纪人</el-button>
    <el-table :data="brokers" border style="width: 100%" v-loading="loading">
      <el-table-column prop="id" label="ID" width="80"></el-table-column>
      <el-table-column prop="name" label="姓名" width="120"></el-table-column>
      <el-table-column prop="id_card" label="身份证号" width="180"></el-table-column>
      <el-table-column prop="phone" label="手机号" width="120"></el-table-column>
      <el-table-column prop="certificate_type" label="证书类型" width="100">
        <template #default="scope">
          {{ getCertificateTypeLabel(scope.row.certificate_type) }}
        </template>
      </el-table-column>
      <el-table-column prop="certificate_number" label="证书编号" width="150"></el-table-column>
      <el-table-column prop="status" label="审核状态" width="100">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusLabel(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="agency" label="所属企业" width="200">
        <template #default="scope">
          {{ scope.row.agency?.name || '未知企业' }}
        </template>
      </el-table-column>
      <el-table-column prop="deleted_at" label="账户状态" width="100">
        <template #default="scope">
          <el-tag :type="getAccountStatusType(scope.row.deleted_at)">
            {{ getAccountStatusLabel(scope.row.deleted_at) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button size="small" @click="handleView(scope.row)">查看</el-button>
          <el-button size="small" type="success" @click="handleVerify(scope.row)">审核</el-button>
          <el-button size="small" type="primary" @click="handleHistories(scope.row)">流转记录</el-button>
          <el-button size="small" type="info" @click="handleChanges(scope.row)">变更记录</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination v-model:current-page="currentPage" :page-size="pageSize" :total="total" layout="total, prev, pager, next" class="mt-4"
      @current-change="handlePageChange" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getAdminBrokerList, verifyBroker } from '@/api/admin-broker'

const router = useRouter()
const brokers = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const loading = ref(false)

onMounted(() => {
  fetchBrokers()
})

const fetchBrokers = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      per_page: pageSize.value
    }
    const response = await getAdminBrokerList(params)
    // 处理Laravel分页响应格式
    if (response.data && Array.isArray(response.data)) {
      brokers.value = response.data
      total.value = response.total || 0
    } else if (Array.isArray(response)) {
      brokers.value = response
      total.value = response.length
    } else {
      brokers.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取经纪人列表失败:', error)
    ElMessage.error('获取经纪人列表失败')
    // 如果API调用失败，使用模拟数据作为后备
    brokers.value = [
      {
        id: 1,
        agency_id: 1,
        user_id: 3,
        name: '张三',
        id_card: '110101199001011234',
        phone: '13800138000',
        certificate_type: 'broker',
        certificate_number: 'CERT001',
        status: 'pending',
        created_at: '2025-07-23 10:00:00',
        updated_at: '2025-07-23 10:00:00',
        deleted_at: null,
        agency: {
          id: 1,
          name: '中介公司A',
          contact: '13800138000',
          address: '北京市朝阳区示例地址1号'
        },
        user: {
          id: 3,
          phone: '13800138000',
          status: 'pending'
        }
      },
      {
        id: 2,
        agency_id: 2,
        user_id: 4,
        name: '李四',
        id_card: '110101199001021234',
        phone: '13800138001',
        certificate_type: 'assistant',
        certificate_number: 'CERT002',
        status: 'approved',
        created_at: '2025-07-23 11:00:00',
        updated_at: '2025-07-23 11:00:00',
        deleted_at: '2025-07-23 12:00:00', // 这个账户被禁用了
        agency: {
          id: 2,
          name: '中介公司B',
          contact: '13800138001',
          address: '上海市浦东新区示例地址2号'
        },
        user: {
          id: 4,
          phone: '13800138001',
          status: 'approved'
        }
      },
    ]
    total.value = brokers.value.length
  } finally {
    loading.value = false
  }
}

const handleView = (row) => {
  router.push(`/admin/brokers/${row.id}`)
}

const handleVerify = async (row) => {
  try {
    const action = await ElMessageBox.confirm(
      `请选择对经纪人 "${row.name}" 的审核操作`,
      '经纪人审核',
      {
        distinguishCancelAndClose: true,
        confirmButtonText: '通过',
        cancelButtonText: '驳回',
        type: 'warning',
      }
    )

    if (action === 'confirm') {
      // 审核通过
      await verifyBroker(row.id, 'approved')
      ElMessage.success('审核通过成功')
      await fetchBrokers() // 重新获取列表
    }
  } catch (action) {
    if (action === 'cancel') {
      // 驳回，需要输入驳回原因
      try {
        const { value: reason } = await ElMessageBox.prompt(
          '请输入驳回原因',
          '驳回经纪人申请',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            inputPattern: /.+/,
            inputErrorMessage: '驳回原因不能为空'
          }
        )

        await verifyBroker(row.id, 'rejected', reason)
        ElMessage.success('驳回成功')
        await fetchBrokers() // 重新获取列表
      } catch (error) {
        // 用户取消输入驳回原因
        if (error !== 'cancel') {
          console.error('驳回失败:', error)
          ElMessage.error('驳回失败')
        }
      }
    }
  }
}

const handleHistories = (row) => {
  router.push(`/admin/brokers/histories?id=${row.id}`)
}

const handleChanges = (row) => {
  router.push(`/admin/brokers/${row.id}/changes`)
}

const handlePageChange = (page) => {
  currentPage.value = page
  fetchBrokers()
}

// 证书类型标签转换
const getCertificateTypeLabel = (type) => {
  const typeMap = {
    'broker': '经纪人',
    'assistant': '助理',
    'training': '培训'
  }
  return typeMap[type] || type
}

// 状态标签转换
const getStatusLabel = (status) => {
  const statusMap = {
    'pending': '待审核',
    'approved': '已通过',
    'rejected': '已驳回'
  }
  return statusMap[status] || status
}

// 状态类型转换
const getStatusType = (status) => {
  const typeMap = {
    'pending': 'warning',
    'approved': 'success',
    'rejected': 'danger'
  }
  return typeMap[status] || 'info'
}

// 账户状态标签转换
const getAccountStatusLabel = (deletedAt) => {
  return deletedAt ? '已禁用' : '正常'
}

// 账户状态类型转换
const getAccountStatusType = (deletedAt) => {
  return deletedAt ? 'danger' : 'success'
}
</script>

<style scoped>
/* 经纪人管理特定样式 */
</style>
