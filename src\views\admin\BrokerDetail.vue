<template>
  <div class="bg-white min-h-screen flex flex-col" v-loading="loading">
    <!-- 固定头部 -->
    <div class="bg-white shadow-sm border-b sticky top-0 z-10">
      <div class="flex justify-between items-center p-6">
        <div class="flex items-center">
          <el-button type="primary" plain icon="ArrowLeft" @click="goBack" class="mr-4" size="default">
            返回
          </el-button>
          <h2 class="text-2xl font-bold">经纪人详情</h2>
        </div>
        <div class="flex gap-2">
          <el-button type="primary" @click="editMode = !editMode" plain :icon="editMode ? 'Close' : 'EditPen'">
            {{ editMode ? '取消编辑' : '编辑' }}
          </el-button>
          <el-button v-if="broker.status === 'pending'" type="success" @click="approveBroker" plain icon="Check">
            审核通过
          </el-button>
          <el-button v-if="broker.status === 'pending'" type="danger" @click="rejectBroker" plain icon="Close">
            驳回
          </el-button>
        </div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="flex-grow p-6">
      <!-- 编辑模式 -->
      <el-form v-if="editMode" :model="broker" label-width="160px" class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <el-form-item label="姓名" required>
          <el-input v-model="broker.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="身份证号" required>
          <el-input v-model="broker.id_card" placeholder="请输入身份证号" />
        </el-form-item>
        <el-form-item label="联系电话" required>
          <el-input v-model="broker.phone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="证书类型" required>
          <el-select v-model="broker.certificate_type" placeholder="请选择证书类型">
            <el-option label="经纪人" value="broker" />
            <el-option label="助理" value="assistant" />
            <el-option label="培训" value="training" />
          </el-select>
        </el-form-item>
        <el-form-item label="证书编号" required>
          <el-input v-model="broker.certificate_number" placeholder="请输入证书编号" />
        </el-form-item>
        <el-form-item label="所属机构">
          <el-input v-model="broker.agency.name" placeholder="所属机构名称" readonly />
        </el-form-item>
        <el-form-item label="机构联系电话">
          <el-input v-model="broker.agency.contact" placeholder="机构联系电话" readonly />
        </el-form-item>
        <el-form-item label="机构地址" class="col-span-1 lg:col-span-2">
          <el-input v-model="broker.agency.address" placeholder="机构地址" readonly />
        </el-form-item>
        <el-form-item label="审核状态">
          <el-select v-model="broker.status" placeholder="请选择审核状态">
            <el-option label="待审核" value="pending" />
            <el-option label="已通过" value="approved" />
            <el-option label="已驳回" value="rejected" />
          </el-select>
        </el-form-item>
        <div class="col-span-1 lg:col-span-2 flex justify-end space-x-2 mt-6">
          <el-button type="primary" @click="saveBroker" icon="Check">保存</el-button>
          <el-button @click="editMode = false" icon="Close">取消</el-button>
        </div>
      </el-form>

      <!-- 查看模式 -->
      <div v-else class="max-w-6xl mx-auto">
        <el-descriptions :title="broker.name" :column="2" border class="mb-6">
          <!-- 基本信息 -->
          <el-descriptions-item label="经纪人ID">{{ broker.id }}</el-descriptions-item>
          <el-descriptions-item label="身份证号">{{ broker.id_card }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ broker.phone }}</el-descriptions-item>
          <el-descriptions-item label="证书类型">{{ getCertificateTypeLabel(broker.certificate_type) }}</el-descriptions-item>
          <el-descriptions-item label="证书编号">{{ broker.certificate_number }}</el-descriptions-item>
          <el-descriptions-item label="审核状态">
            <el-tag :type="statusTagType(broker.status)">
              {{ statusText(broker.status) }}
            </el-tag>
          </el-descriptions-item>

          <!-- 机构信息 -->
          <el-descriptions-item label="所属机构">{{ broker.agency?.name || '未知机构' }}</el-descriptions-item>
          <el-descriptions-item label="机构联系电话">{{ broker.agency?.contact || '未提供' }}</el-descriptions-item>
          <el-descriptions-item label="机构地址" :span="2">{{ broker.agency?.address || '未提供' }}</el-descriptions-item>

          <!-- 账户信息 -->
          <el-descriptions-item label="账户状态">
            <el-tag :type="getAccountStatusType(broker.deleted_at)">
              {{ getAccountStatusLabel(broker.deleted_at) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="账户手机号">{{ broker.user?.phone || '未绑定' }}</el-descriptions-item>

          <!-- 时间信息 -->
          <el-descriptions-item label="创建时间">{{ formatDateTime(broker.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatDateTime(broker.updated_at) }}</el-descriptions-item>
        </el-descriptions>

        <!-- 图片展示 -->
        <div class="mt-6" v-if="broker.id_card_image || broker.certificate_image">
          <h3 class="text-lg font-bold mb-4">相关图片</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div v-if="broker.id_card_image">
              <h4 class="text-sm font-medium mb-2">身份证照片</h4>
              <el-image :src="getImageUrl(broker.id_card_image)" :preview-src-list="[getImageUrl(broker.id_card_image)]"
                class="w-full h-48 object-cover border rounded" fit="cover">
                <template #error>
                  <div class="w-full h-48 flex items-center justify-center bg-gray-100 text-gray-400">
                    图片加载失败
                  </div>
                </template>
              </el-image>
            </div>
            <div v-if="broker.certificate_image">
              <h4 class="text-sm font-medium mb-2">证书照片</h4>
              <el-image :src="getImageUrl(broker.certificate_image)" :preview-src-list="[getImageUrl(broker.certificate_image)]"
                class="w-full h-48 object-cover border rounded" fit="cover">
                <template #error>
                  <div class="w-full h-48 flex items-center justify-center bg-gray-100 text-gray-400">
                    图片加载失败
                  </div>
                </template>
              </el-image>
            </div>
          </div>
        </div>
        <div class="flex justify-end space-x-2 mt-6" v-if="broker.status === 'pending'">
          <el-button type="success" @click="approveBroker">审核通过</el-button>
          <el-button type="danger" @click="rejectBroker">驳回</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getAdminBrokerDetail, verifyBroker } from '@/api/admin-broker'

const route = useRoute()
const router = useRouter()
const editMode = ref(false)
const loading = ref(false)
const broker = ref({
  id: 0,
  agency_id: 0,
  user_id: 0,
  name: '',
  id_card: '',
  phone: '',
  certificate_type: '',
  certificate_number: '',
  status: 'pending',
  id_card_image: '',
  certificate_image: '',
  created_at: '',
  updated_at: '',
  deleted_at: null,
  agency: {
    id: 0,
    name: '',
    contact: '',
    address: ''
  },
  user: {
    id: 0,
    phone: '',
    status: 'pending'
  }
})

onMounted(() => {
  fetchBrokerDetail()
})

const fetchBrokerDetail = async () => {
  loading.value = true
  try {
    const brokerId = route.params.id
    const response = await getAdminBrokerDetail(brokerId)
    broker.value = response.data || response
  } catch (error) {
    console.error('获取经纪人详情失败:', error)
    ElMessage.error('获取经纪人详情失败')
    // 使用模拟数据作为后备
    broker.value = {
      id: parseInt(route.params.id),
      agency_id: 1,
      user_id: 5,
      name: '李四',
      id_card: '310101199001011234',
      phone: '13900000000',
      certificate_type: 'broker',
      certificate_number: 'CERT002',
      status: 'pending',
      id_card_image: '/storage/id_card/broker_id_card_example.png',
      certificate_image: '/storage/certificate/broker_cert_example.png',
      created_at: '2025-07-23 10:00:00',
      updated_at: '2025-07-23 10:00:00',
      deleted_at: null,
      agency: {
        id: 1,
        name: '示例房产中介公司',
        contact: '13900000000',
        address: '上海市浦东新区示例地址123号'
      },
      user: {
        id: 5,
        phone: '13900000000',
        status: 'pending'
      }
    }
  } finally {
    loading.value = false
  }
}

function saveBroker() {
  editMode.value = false
  ElMessage.success('保存成功（模拟）')
}

function goBack() {
  router.back()
}

async function approveBroker() {
  try {
    await verifyBroker(broker.value.id, 'approved')
    broker.value.status = 'approved'
    ElMessage.success('审核通过成功')
  } catch (error) {
    console.error('审核通过失败:', error)
    ElMessage.error('审核通过失败')
  }
}

async function rejectBroker() {
  try {
    const { value: reason } = await ElMessageBox.prompt(
      '请输入驳回原因',
      '驳回经纪人申请',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /.+/,
        inputErrorMessage: '驳回原因不能为空'
      }
    )

    await verifyBroker(broker.value.id, 'rejected', reason)
    broker.value.status = 'rejected'
    ElMessage.success('驳回成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('驳回失败:', error)
      ElMessage.error('驳回失败')
    }
  }
}
// 证书类型标签转换
const getCertificateTypeLabel = (type) => {
  const typeMap = {
    'broker': '经纪人',
    'assistant': '助理',
    'training': '培训'
  }
  return typeMap[type] || type
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '未知'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 获取图片URL
const getImageUrl = (imagePath) => {
  if (!imagePath) return ''
  // 如果是完整URL，直接返回
  if (imagePath.startsWith('http')) return imagePath
  // 如果是相对路径，添加基础URL
  return `http://127.0.0.1:8000${imagePath}`
}

function statusText(status) {
  switch (status) {
    case 'pending':
      return '待审核'
    case 'approved':
      return '已通过'
    case 'rejected':
      return '已驳回'
    default:
      return status
  }
}

function statusTagType(status) {
  switch (status) {
    case 'pending':
      return 'warning'
    case 'approved':
      return 'success'
    case 'rejected':
      return 'danger'
    default:
      return ''
  }
}

// 账户状态标签转换
const getAccountStatusLabel = (deletedAt) => {
  return deletedAt ? '已禁用' : '正常'
}

// 账户状态类型转换
const getAccountStatusType = (deletedAt) => {
  return deletedAt ? 'danger' : 'success'
}
</script>

<style scoped>
/* 经纪人详情页面样式 */
</style>
