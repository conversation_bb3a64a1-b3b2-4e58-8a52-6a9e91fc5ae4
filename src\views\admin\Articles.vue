<template>
  <div class="bg-white p-6 rounded-lg shadow-md">
    <h2 class="text-2xl font-bold mb-4">文章管理</h2>

    <!-- 搜索和筛选 -->
    <div class="mb-4 flex items-center space-x-4">
      <el-input v-model="searchKeyword" placeholder="搜索文章标题或作者" style="width: 300px" clearable @keyup.enter="handleSearch">
        <template #prefix>
          <el-icon>
            <Search />
          </el-icon>
        </template>
      </el-input>

      <el-select v-model="selectedCategoryId" placeholder="选择分类" style="width: 200px" clearable @change="handleCategoryChange">
        <el-option v-for="category in categories" :key="category.id" :label="category.name" :value="category.id" />
      </el-select>

      <el-button type="primary" @click="handleSearch">搜索</el-button>
      <el-button @click="handleReset">重置</el-button>
    </div>

    <!-- 操作按钮 -->
    <div class="mb-4 flex items-center space-x-2">
      <el-button type="primary" @click="handleAdd">新增文章</el-button>
      <el-button @click="fetchArticles">刷新</el-button>
    </div>

    <!-- 文章列表 -->
    <el-table :data="articles" border style="width: 100%" v-loading="loading">
      <el-table-column prop="id" label="ID" width="80" />

      <el-table-column prop="title" label="文章标题" min-width="200">
        <template #default="{ row }">
          <div class="font-medium text-blue-600 cursor-pointer hover:text-blue-800" @click="handleView(row)">
            {{ row.title }}
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="author" label="作者" width="120">
        <template #default="{ row }">
          <div class="font-medium">{{ row.author }}</div>
        </template>
      </el-table-column>

      <el-table-column prop="category" label="分类" width="120">
        <template #default="{ row }">
          <el-tag size="small" type="info">{{ row.category?.name || '未分类' }}</el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="views" label="浏览量" width="100">
        <template #default="{ row }">
          <span class="text-gray-600">{{ row.views || 0 }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="published_at" label="发布时间" width="180">
        <template #default="{ row }">
          {{ formatDate(row.published_at) }}
        </template>
      </el-table-column>

      <el-table-column prop="created_at" label="创建时间" width="180">
        <template #default="{ row }">
          {{ formatDate(row.created_at) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button size="small" type="info" @click="handleView(row)">查看</el-button>
          <el-button size="small" type="primary" @click="handleEdit(row)">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination v-model:current-page="currentPage" :page-size="pageSize" :total="total" layout="total, prev, pager, next" class="mt-4"
      @current-change="handleCurrentChange" />
    <!-- 新增/编辑对话框 -->
    <el-dialog v-model="dialogVisible" :title="isEdit ? '编辑文章' : '新增文章'" width="800px" @close="resetForm">
      <el-form ref="formRef" :model="form" :rules="formRules" label-width="100px">
        <el-form-item label="文章标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入文章标题" maxlength="200" show-word-limit />
        </el-form-item>

        <el-form-item label="作者" prop="author">
          <el-input v-model="form.author" placeholder="请输入作者姓名" maxlength="50" show-word-limit />
        </el-form-item>

        <el-form-item label="分类" prop="category_id">
          <el-select v-model="form.category_id" placeholder="请选择分类" style="width: 100%">
            <el-option v-for="category in categories" :key="category.id" :label="category.name" :value="category.id" />
          </el-select>
        </el-form-item>

        <el-form-item label="摘要" prop="summary">
          <el-input v-model="form.summary" type="textarea" :rows="3" placeholder="请输入文章摘要" maxlength="500" show-word-limit />
        </el-form-item>

        <el-form-item label="发布时间" prop="published_at">
          <el-date-picker v-model="form.published_at" type="datetime" placeholder="选择发布时间" style="width: 100%" format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss" />
        </el-form-item>

        <el-form-item label="文章内容" prop="content">
          <RichTextEditor v-model="form.content" placeholder="请输入文章内容，支持富文本格式、图片上传和Word粘贴" height="400px" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 查看文章对话框 -->
    <el-dialog v-model="viewDialogVisible" title="查看文章" width="800px">
      <div v-if="viewArticle">
        <div class="mb-4">
          <h3 class="text-xl font-bold mb-2">{{ viewArticle.title }}</h3>
          <div class="text-sm text-gray-600 mb-4">
            <span>作者：{{ viewArticle.author }}</span>
            <span class="ml-4">分类：{{ viewArticle.category?.name || '未分类' }}</span>
            <span class="ml-4">发布时间：{{ formatDate(viewArticle.published_at) }}</span>
            <span class="ml-4">浏览量：{{ viewArticle.views || 0 }}</span>
          </div>
        </div>

        <div v-if="viewArticle.summary" class="mb-4">
          <h4 class="font-medium mb-2">摘要：</h4>
          <p class="text-gray-700 bg-gray-50 p-3 rounded">{{ viewArticle.summary }}</p>
        </div>

        <div>
          <h4 class="font-medium mb-2">内容：</h4>
          <div class="text-gray-700 bg-gray-50 p-4 rounded max-h-96 overflow-y-auto article-content" v-html="viewArticle.content">
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import {
  getArticleList,
  createArticle,
  updateArticle,
  deleteArticle,
  getArticleDetail
} from '@/api/admin/articles'
import { getArticleCategoryList } from '@/api/admin/articleCategories'
import RichTextEditor from '@/components/RichTextEditor.vue'
import {
  getBeijingTimeString,
  formatBeijingTime,
  toDatePickerValue,
  fromDatePickerValue
} from '@/utils/timezone'

// 响应式数据
const loading = ref(false)
const articles = ref([])
const categories = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 搜索和筛选
const searchKeyword = ref('')
const selectedCategoryId = ref(null)

// 对话框相关
const dialogVisible = ref(false)
const viewDialogVisible = ref(false)
const isEdit = ref(false)
const submitting = ref(false)
const formRef = ref()
const viewArticle = ref(null)

// 表单数据
const form = reactive({
  id: null,
  title: '',
  content: '',
  category_id: null,
  author: '',
  summary: '',
  published_at: ''
})

// 表单验证规则
const formRules = {
  title: [
    { required: true, message: '请输入文章标题', trigger: 'blur' },
    { min: 2, max: 200, message: '标题长度在 2 到 200 个字符', trigger: 'blur' }
  ],
  author: [
    { required: true, message: '请输入作者姓名', trigger: 'blur' },
    { min: 2, max: 50, message: '作者姓名长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  category_id: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  summary: [
    { max: 500, message: '摘要长度不能超过 500 个字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入文章内容', trigger: 'blur' }
  ],
  published_at: [
    { required: true, message: '请选择发布时间', trigger: 'change' }
  ]
}

// 获取文章列表
const fetchArticles = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      page_size: pageSize.value
    }

    // 添加搜索条件
    if (searchKeyword.value) {
      params.keyword = searchKeyword.value
    }
    if (selectedCategoryId.value) {
      params.category_id = selectedCategoryId.value
    }

    const response = await getArticleList(params)

    articles.value = response.data || []
    total.value = response.total || 0
    currentPage.value = response.current_page || 1
  } catch (error) {
    console.error('获取文章列表失败:', error)
    ElMessage.error('获取文章列表失败')

    // 使用模拟数据
    articles.value = [
      {
        id: 1,
        title: '菏泽市自然资源和规划局关于鲁中车辆检测有限公司建设项目规划许可的公示',
        author: '规划局',
        summary: '关于鲁中车辆检测有限公司建设项目的规划许可公示',
        views: 156,
        published_at: '2025-01-20 10:00:00',
        category: { id: 1, name: '阳光规划' },
        created_at: '2025-01-20 10:00:00',
        updated_at: '2025-01-20 10:00:00'
      },
      {
        id: 2,
        title: '菏泽市自然资源和规划局关于学院路西延工程建设项目规划许可的公示',
        author: '规划局',
        summary: '关于学院路西延工程建设项目的规划许可公示',
        views: 89,
        published_at: '2025-01-19 15:30:00',
        category: { id: 1, name: '阳光规划' },
        created_at: '2025-01-19 15:30:00',
        updated_at: '2025-01-19 15:30:00'
      },
      {
        id: 3,
        title: '菏泽市自然资源和规划局等11部门关于进一步加强房地产市场监管的通知',
        author: '规划局',
        summary: '关于进一步加强房地产市场监管的通知',
        views: 234,
        published_at: '2025-01-18 09:15:00',
        category: { id: 2, name: '政策法规' },
        created_at: '2025-01-18 09:15:00',
        updated_at: '2025-01-18 09:15:00'
      }
    ]
    total.value = 3
  } finally {
    loading.value = false
  }
}

// 获取分类列表
const fetchCategories = async () => {
  try {
    const response = await getArticleCategoryList()
    categories.value = response.data || []
  } catch (error) {
    console.error('获取分类列表失败:', error)
    // 使用模拟数据
    categories.value = [
      { id: 1, name: '阳光规划' },
      { id: 2, name: '政策法规' },
      { id: 3, name: '政策解读' }
    ]
  }
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  fetchArticles()
}

// 分类筛选处理
const handleCategoryChange = () => {
  currentPage.value = 1
  fetchArticles()
}

// 重置筛选条件
const handleReset = () => {
  searchKeyword.value = ''
  selectedCategoryId.value = null
  currentPage.value = 1
  fetchArticles()
}

// 分页处理
const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchArticles()
}

// 新增文章
const handleAdd = () => {
  isEdit.value = false
  resetForm()
  // 设置默认发布时间为当前东八区时间
  form.published_at = getBeijingTimeString()
  dialogVisible.value = true
}

// 编辑文章
const handleEdit = async (row) => {
  resetForm()
  isEdit.value = true

  try {
    loading.value = true
    // 获取文章详情
    const response = await getArticleDetail(row.id)
    const article = response || row

    // 填充表单
    form.id = article.id
    form.title = article.title
    form.content = article.content || ''
    form.category_id = article.category?.id || article.category_id
    form.author = article.author || ''
    form.summary = article.summary || ''
    form.published_at = article.published_at || ''

    dialogVisible.value = true
  } catch (error) {
    console.error('获取文章详情失败:', error)
    ElMessage.error('获取文章详情失败')

    // 使用行数据填充
    form.id = row.id
    form.title = row.title
    form.content = row.content || '获取详情失败，请重试'
    form.category_id = row.category?.id || row.category_id
    form.author = row.author || ''
    form.summary = row.summary || ''
    form.published_at = row.published_at || ''

    dialogVisible.value = true
  } finally {
    loading.value = false
  }
}
// 查看文章
const handleView = async (row) => {
  try {
    loading.value = true
    // 获取文章详情
    const response = await getArticleDetail(row.id)
    viewArticle.value = response || row
    viewDialogVisible.value = true
  } catch (error) {
    console.error('获取文章详情失败:', error)
    ElMessage.error('获取文章详情失败')

    // 使用行数据
    viewArticle.value = row
    viewDialogVisible.value = true
  } finally {
    loading.value = false
  }
}

// 删除文章
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除文章"${row.title}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteArticle(row.id)
    ElMessage.success('删除成功')
    fetchArticles()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除文章失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    submitting.value = true

    if (isEdit.value) {
      await updateArticle(form.id, {
        title: form.title,
        content: form.content,
        category_id: form.category_id,
        author: form.author,
        summary: form.summary,
        published_at: form.published_at
      })
      ElMessage.success('更新成功')
    } else {
      await createArticle({
        title: form.title,
        content: form.content,
        category_id: form.category_id,
        author: form.author,
        summary: form.summary,
        published_at: form.published_at
      })
      ElMessage.success('创建成功')
    }

    dialogVisible.value = false
    fetchArticles()
  } catch (error) {
    console.error('提交失败:', error)

    // 处理表单验证错误
    if (error && typeof error === 'object' && !error.response) {
      const errorMessages = []
      Object.keys(error).forEach(field => {
        if (Array.isArray(error[field]) && error[field].length > 0) {
          errorMessages.push(error[field][0].message)
        }
      })

      if (errorMessages.length > 0) {
        ElMessage.error(errorMessages.join('；'))
      } else {
        ElMessage.error('表单验证失败，请检查输入内容')
      }
    } else {
      ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
    }
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  form.id = null
  form.title = ''
  form.content = ''
  form.category_id = null
  form.author = ''
  form.summary = ''
  form.published_at = ''

  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  return formatBeijingTime(dateString, 'datetime')
}

// 生命周期
onMounted(() => {
  fetchArticles()
  fetchCategories()
})
</script>

<style scoped>
/* 文章管理特定样式 */

/* 文章内容显示样式 */
:deep(.article-content) {
  line-height: 1.6;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
    sans-serif;
}

:deep(.article-content img) {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 10px auto;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.article-content table) {
  border-collapse: collapse;
  width: 100%;
  margin: 10px 0;
  border: 1px solid #ddd;
}

:deep(.article-content table td, .article-content table th) {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

:deep(.article-content table th) {
  background-color: #f5f5f5;
  font-weight: bold;
}

:deep(.article-content pre) {
  background-color: #f5f5f5;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
  margin: 10px 0;
  overflow-x: auto;
  font-family: 'Courier New', Courier, monospace;
}

:deep(.article-content blockquote) {
  border-left: 4px solid #409eff;
  padding-left: 16px;
  margin: 10px 0;
  color: #666;
  background-color: #f9f9f9;
  padding: 10px 16px;
  border-radius: 4px;
}

:deep(
    .article-content h1,
    .article-content h2,
    .article-content h3,
    .article-content h4,
    .article-content h5,
    .article-content h6
  ) {
  margin: 15px 0 10px 0;
  font-weight: bold;
  line-height: 1.4;
}

:deep(.article-content h1) {
  font-size: 2em;
}
:deep(.article-content h2) {
  font-size: 1.5em;
}
:deep(.article-content h3) {
  font-size: 1.3em;
}
:deep(.article-content h4) {
  font-size: 1.1em;
}
:deep(.article-content h5) {
  font-size: 1em;
}
:deep(.article-content h6) {
  font-size: 0.9em;
}

:deep(.article-content p) {
  margin: 8px 0;
}

:deep(.article-content ul, .article-content ol) {
  margin: 10px 0;
  padding-left: 20px;
}

:deep(.article-content li) {
  margin: 5px 0;
}

:deep(.article-content a) {
  color: #409eff;
  text-decoration: none;
}

:deep(.article-content a:hover) {
  text-decoration: underline;
}

:deep(.article-content strong) {
  font-weight: bold;
}

:deep(.article-content em) {
  font-style: italic;
}

:deep(.article-content u) {
  text-decoration: underline;
}

:deep(.article-content s) {
  text-decoration: line-through;
}
</style>
