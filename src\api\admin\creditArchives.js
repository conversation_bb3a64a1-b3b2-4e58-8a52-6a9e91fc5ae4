import request from '@/utils/request'

/**
 * 信用档案管理相关API接口
 */

/**
 * 获取信用档案列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.per_page - 每页数量
 * @param {string} params.entity_type - 主体类型 (agency/broker)
 * @param {string} params.type - 档案类型 (red/black)
 * @param {string} params.keyword - 搜索关键词
 * @returns {Promise}
 */
export function getCreditArchiveList(params = {}) {
  return request.get('/admin/credit-archives', { params })
}

/**
 * 获取信用档案详情
 * @param {number} id - 档案ID
 * @returns {Promise}
 */
export function getCreditArchiveDetail(id) {
  return request.get(`/admin/credit-archives/${id}`)
}

/**
 * 创建信用档案
 * @param {Object} data - 档案数据
 * @param {string} data.entity_type - 主体类型 (agency/broker)
 * @param {number} data.entity_id - 主体ID
 * @param {string} data.type - 档案类型 (red/black)
 * @param {string} data.title - 标题
 * @param {string} data.content - 内容
 * @returns {Promise}
 */
export function createCreditArchive(data) {
  return request.post('/admin/credit-archives', data)
}

/**
 * 更新信用档案
 * @param {number} id - 档案ID
 * @param {Object} data - 档案数据
 * @param {string} data.entity_type - 主体类型 (agency/broker)
 * @param {number} data.entity_id - 主体ID
 * @param {string} data.type - 档案类型 (red/black)
 * @param {string} data.title - 标题
 * @param {string} data.content - 内容
 * @returns {Promise}
 */
export function updateCreditArchive(id, data) {
  return request.put(`/admin/credit-archives/${id}`, data)
}

/**
 * 删除信用档案
 * @param {number} id - 档案ID
 * @returns {Promise}
 */
export function deleteCreditArchive(id) {
  return request.delete(`/admin/credit-archives/${id}`)
}

/**
 * 获取机构列表（用于选择）
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getAgencyOptions(params = {}) {
  return request.get('/admin/agencies', {
    params: {
      ...params,
      per_page: 1000 // 获取所有机构用于选择（管理员权限，不限制状态）
    }
  })
}

/**
 * 获取经纪人列表（用于选择）
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getBrokerOptions(params = {}) {
  return request.get('/admin/brokers', {
    params: {
      ...params,
      per_page: 1000 // 获取所有经纪人用于选择（管理员权限，不限制状态）
    }
  })
}

// 常量定义
export const ENTITY_TYPES = {
  agency: '企业',
  broker: '个人'
}

export const ARCHIVE_TYPES = {
  red: '红榜',
  black: '黑榜'
}

export const ENTITY_TYPE_OPTIONS = [
  { value: 'agency', label: '企业' },
  { value: 'broker', label: '个人' }
]

export const ARCHIVE_TYPE_OPTIONS = [
  { value: 'red', label: '红榜' },
  { value: 'black', label: '黑榜' }
]
