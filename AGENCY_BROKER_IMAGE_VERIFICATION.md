# 机构管理员端经纪人图片功能验证指南

## 🎯 验证目标

验证机构管理员端经纪人管理页面的图片展示功能是否正常工作，包括列表展示和编辑时的图片显示。

## 📋 验证步骤

### 1. 访问经纪人管理页面

1. 登录机构管理员账户
2. 导航到 `/agency/brokers`
3. 检查页面是否正常加载

### 2. 列表页面图片展示验证

#### 2.1 表格列验证
验证表格是否包含以下列：
- [ ] ID列
- [ ] 姓名列
- [ ] 身份证号列
- [ ] 手机号列
- [ ] 证书类型列
- [ ] 证书编号列
- [ ] 审核状态列
- [ ] **身份证照片列** (新增)
- [ ] **证书照片列** (新增)
- [ ] 操作列

#### 2.2 图片显示验证

**有图片的情况**:
- [ ] 身份证照片正确显示为64x40px缩略图
- [ ] 证书照片正确显示为64x40px缩略图
- [ ] 图片有圆角边框效果
- [ ] 点击图片可以预览放大
- [ ] 图片比例正确（object-cover）

**无图片的情况**:
- [ ] 显示"暂无图片"占位符
- [ ] 占位符尺寸与有图片时一致
- [ ] 占位符背景为浅灰色
- [ ] 文字居中显示

**图片加载失败的情况**:
- [ ] 显示"加载失败"提示
- [ ] 错误状态背景为灰色
- [ ] 布局不变形

### 3. 编辑对话框图片验证

#### 3.1 编辑有图片的经纪人
1. 点击有图片经纪人的"编辑"按钮
2. 验证编辑对话框内容：

**身份证照片部分**:
- [ ] 显示"当前身份证照片："标签
- [ ] 显示128x80px的当前图片预览
- [ ] 图片可以点击预览放大
- [ ] 上传按钮显示"重新上传"
- [ ] 上传组件正常工作

**证书照片部分**:
- [ ] 显示"当前证书照片："标签
- [ ] 显示128x80px的当前图片预览
- [ ] 图片可以点击预览放大
- [ ] 上传按钮显示"重新上传"
- [ ] 上传组件正常工作

#### 3.2 编辑无图片的经纪人
1. 点击无图片经纪人的"编辑"按钮
2. 验证编辑对话框内容：

- [ ] 不显示"当前身份证照片"预览
- [ ] 不显示"当前证书照片"预览
- [ ] 上传按钮显示"点击上传"
- [ ] 上传组件正常工作

#### 3.3 编辑部分有图片的经纪人
1. 点击只有部分图片的经纪人"编辑"按钮
2. 验证：
- [ ] 有图片的字段显示预览和"重新上传"
- [ ] 无图片的字段不显示预览，显示"点击上传"

### 4. 添加经纪人功能验证

1. 点击"添加经纪人"按钮
2. 验证添加对话框：
- [ ] 不显示任何图片预览
- [ ] 所有上传按钮显示"点击上传"
- [ ] 上传组件正常工作

### 5. 图片预览功能验证

#### 5.1 列表中的图片预览
- [ ] 点击列表中的身份证图片可以预览
- [ ] 点击列表中的证书图片可以预览
- [ ] 预览窗口正常显示
- [ ] 预览窗口可以正常关闭

#### 5.2 编辑中的图片预览
- [ ] 点击编辑对话框中的现有图片可以预览
- [ ] 预览功能与列表中一致

## 🧪 测试数据

### 模拟数据验证
当API不可用时，应显示模拟数据：

**经纪人1 (有图片)**:
- 姓名: 张三
- 身份证图片: 有
- 证书图片: 有
- 状态: 待审核

**经纪人2 (部分图片)**:
- 姓名: 李四
- 身份证图片: 无
- 证书图片: 有
- 状态: 已通过

### API数据验证
当API可用时，验证：
- [ ] 真实数据正确加载
- [ ] 图片路径正确解析
- [ ] 图片URL正确拼接

## 🔍 详细检查项

### 1. 图片尺寸和样式
- [ ] 列表缩略图: 64x40px (w-16 h-10)
- [ ] 编辑预览图: 128x80px (w-32 h-20)
- [ ] 圆角边框效果正确
- [ ] object-cover裁剪正确

### 2. 文字和标签
- [ ] "暂无图片"文字显示正确
- [ ] "加载失败"文字显示正确
- [ ] "当前身份证照片："标签显示正确
- [ ] "当前证书照片："标签显示正确
- [ ] 按钮文字切换正确

### 3. 交互功能
- [ ] 图片点击预览功能正常
- [ ] 上传组件交互正常
- [ ] 文件选择功能正常
- [ ] 对话框开关正常

### 4. 错误处理
- [ ] 图片路径为空时的处理
- [ ] 图片加载失败时的处理
- [ ] 网络错误时的处理
- [ ] 无效图片格式的处理

## 🚨 常见问题检查

### 1. 图片不显示
**可能原因**:
- 图片路径错误
- 服务器图片不存在
- CORS问题
- 网络连接问题

**检查方法**:
- 查看浏览器控制台错误
- 检查网络面板请求状态
- 验证图片URL是否可直接访问

### 2. 布局异常
**可能原因**:
- CSS样式冲突
- 图片尺寸不正确
- 容器宽度问题

**检查方法**:
- 检查元素样式
- 验证Tailwind CSS类是否生效
- 查看布局计算

### 3. 预览功能异常
**可能原因**:
- Element Plus版本问题
- 图片URL格式问题
- 事件绑定问题

**检查方法**:
- 查看控制台JavaScript错误
- 验证Element Plus组件正常工作
- 检查事件监听器

## 📱 不同设备验证

### 桌面端
- [ ] Chrome浏览器正常显示
- [ ] Firefox浏览器正常显示
- [ ] Safari浏览器正常显示
- [ ] Edge浏览器正常显示

### 移动端
- [ ] 响应式布局正确
- [ ] 触摸交互正常
- [ ] 图片预览功能正常

## ✅ 验证完成标准

所有验证项目通过，包括：
- 列表图片正确显示
- 编辑时图片正确显示
- 无图片情况正确处理
- 图片预览功能正常
- 错误处理得当
- 用户体验良好

## 📝 验证记录模板

```
验证时间: ____
验证人员: ____
验证环境: ____

列表页面:
- 图片列显示: ✅/❌
- 有图片显示: ✅/❌
- 无图片显示: ✅/❌
- 预览功能: ✅/❌

编辑对话框:
- 现有图片显示: ✅/❌
- 按钮文字切换: ✅/❌
- 上传功能: ✅/❌

错误处理:
- 加载失败处理: ✅/❌
- 无图片处理: ✅/❌

发现问题:
1. ____
2. ____

总体评价: ✅通过/❌需要修复
```

---

**验证环境**: `http://localhost:5174/agency/brokers`  
**功能版本**: 最新版本  
**预期结果**: 所有图片功能正常工作
