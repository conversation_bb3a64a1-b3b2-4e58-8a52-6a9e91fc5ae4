<template>
  <div class="bg-white p-6 rounded-lg shadow-md mx-auto max-w-4xl">
    <h2 class="text-2xl font-bold mb-4">信用档案</h2>
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="企业红榜" name="agency_red"></el-tab-pane>
      <el-tab-pane label="企业黑榜" name="agency_black"></el-tab-pane>
      <el-tab-pane label="个人红榜" name="broker_red"></el-tab-pane>
      <el-tab-pane label="个人黑榜" name="broker_black"></el-tab-pane>
    </el-tabs>
    <el-table :data="archives" border style="width: 100%" class="w-full mt-4">
      <el-table-column prop="id" label="ID" width="80"></el-table-column>
      <el-table-column prop="entity_name" label="主体名称" min-width="150"></el-table-column>
      <el-table-column prop="type" label="档案类型" width="100"></el-table-column>
      <el-table-column prop="title" label="标题" min-width="200"></el-table-column>
      <el-table-column prop="created_at" label="创建时间" width="180"></el-table-column>
      <el-table-column label="操作" width="120">
        <template #default="scope">
          <el-button size="small" @click="handleView(scope.row)">查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="currentPage"
      :page-size="pageSize"
      :total="total"
      layout="total, prev, pager, next"
      class="mt-4 flex justify-center"
      @current-change="handlePageChange"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const archives = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const activeTab = ref('agency_red')

onMounted(() => {
  fetchArchives(activeTab.value)
})

const fetchArchives = (tab = 'agency_red') => {
  // 模拟数据
  if (tab === 'agency_red') {
    archives.value = [
      {
        id: 1,
        entity_name: '中介公司A',
        type: '红榜',
        title: '优秀中介公司表彰',
        created_at: '2023-01-01 10:00:00',
      },
      // 更多数据...
    ]
  } else if (tab === 'agency_black') {
    archives.value = [
      {
        id: 2,
        entity_name: '中介公司B',
        type: '黑榜',
        title: '违规中介公司处罚',
        created_at: '2023-01-02 11:00:00',
      },
      // 更多数据...
    ]
  } else if (tab === 'broker_red') {
    archives.value = [
      {
        id: 3,
        entity_name: '张三',
        type: '红榜',
        title: '优秀经纪人表彰',
        created_at: '2023-01-03 09:00:00',
      },
      // 更多数据...
    ]
  } else {
    archives.value = [
      {
        id: 4,
        entity_name: '李四',
        type: '黑榜',
        title: '违规经纪人处罚通知',
        created_at: '2023-01-04 14:00:00',
      },
      // 更多数据...
    ]
  }
  total.value = archives.value.length
}

const handleTabClick = (tab) => {
  activeTab.value = tab.props.name
  currentPage.value = 1
  fetchArchives(tab.props.name)
}

const handleView = (row) => {
  router.push(`/cms/credit-archives/${row.id}`)
}

const handlePageChange = (page) => {
  currentPage.value = page
  fetchArchives(activeTab.value)
}
</script>

<style scoped>
/* 信用档案列表特定样式 */
</style>
