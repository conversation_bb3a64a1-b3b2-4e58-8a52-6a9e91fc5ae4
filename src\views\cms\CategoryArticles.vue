<template>
  <div class="category-articles bg-gray-50 min-h-screen">
    <!-- 顶部导航栏 - 固定在顶部 -->
    <header class="bg-white shadow-sm fixed top-0 left-0 right-0 z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- 顶部标题栏 -->
        <div class="flex items-center justify-between py-4 border-b border-gray-200">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-red-600 rounded-full flex items-center justify-center mr-4">
              <span class="text-white font-bold text-lg">政</span>
            </div>
            <div>
              <h1 class="text-2xl font-bold text-gray-900">信息平台</h1>
              <p class="text-sm text-gray-600">Government Information Platform</p>
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <el-button type="primary" plain icon="ArrowLeft" @click="goBack" class="mr-4">
              返回首页
            </el-button>
          </div>
        </div>

        <!-- 主导航栏 -->
        <nav class="bg-slate-700">
          <div class="flex items-center h-12">
            <router-link to="/cms" class="flex items-center px-6 py-3 text-white hover:bg-slate-600 transition-colors">
              <el-icon class="mr-2">
                <House />
              </el-icon>
              首页
            </router-link>
          </div>
        </nav>
      </div>
    </header>

    <!-- 内容区域 - 添加顶部间距避免被固定导航遮挡 -->
    <div class="pt-32 pb-20">
      <!-- 面包屑导航 - 固定 -->
      <div class="bg-white border-b border-gray-200 sticky top-32 z-30">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item>
              <router-link to="/cms" class="text-blue-600 hover:text-blue-800">
                首页
              </router-link>
            </el-breadcrumb-item>
            <el-breadcrumb-item class="text-gray-500">
              {{ categoryName }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="flex flex-col" style="height: calc(100vh - 20rem);">
          <!-- 分类标题 - 固定 -->
          <div class="mb-8 flex-shrink-0">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">{{ categoryName }}</h1>
            <p class="text-gray-600">{{ categoryDescription }}</p>
          </div>

          <!-- 文章列表容器 - 可滚动 -->
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden flex-1 flex flex-col min-h-0">
            <!-- 文章列表头部 - 固定 -->
            <div class="border-b-2 border-blue-500 px-6 py-4 bg-gray-50 flex-shrink-0">
              <h2 class="text-xl font-bold text-gray-900 flex items-center">
                <span class="w-1 h-6 bg-blue-500 mr-3"></span>
                文章列表
                <span class="ml-2 text-sm font-normal text-gray-500">(共 {{ total }} 篇)</span>
              </h2>
            </div>

            <!-- 加载状态 -->
            <div v-if="loading" class="flex-1 flex items-center justify-center">
              <div class="text-center">
                <el-icon class="animate-spin text-4xl text-blue-600 mb-4">
                  <Loading />
                </el-icon>
                <p class="text-gray-600">加载中...</p>
              </div>
            </div>

            <!-- 文章列表内容 - 可滚动区域 -->
            <div v-else class="flex-1 overflow-y-auto">
              <div class="divide-y divide-gray-200">
                <article v-for="article in articles" :key="article.id" @click="goToArticle(article.id)"
                  class="p-6 hover:bg-gray-50 cursor-pointer transition-colors group">
                  <div class="flex items-start justify-between">
                    <div class="flex-1 min-w-0">
                      <h3 class="text-lg font-medium text-gray-900 group-hover:text-blue-600 mb-2">
                        {{ article.title }}
                      </h3>
                      <p v-if="article.summary" class="text-gray-600 text-sm mb-3 line-clamp-2">
                        {{ article.summary }}
                      </p>
                      <div class="flex items-center text-sm text-gray-500 space-x-4">
                        <span class="flex items-center">
                          <el-icon class="mr-1">
                            <Calendar />
                          </el-icon>
                          {{ formatDate(article.created_at) }}
                        </span>
                        <span class="flex items-center">
                          <el-icon class="mr-1">
                            <View />
                          </el-icon>
                          {{ article.views || 0 }} 次浏览
                        </span>
                      </div>
                    </div>
                    <div class="ml-4 flex-shrink-0">
                      <el-icon class="text-gray-400 group-hover:text-blue-600">
                        <ArrowRight />
                      </el-icon>
                    </div>
                  </div>
                </article>
              </div>
            </div>

            <!-- 分页 - 固定在底部 -->
            <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex-shrink-0">
              <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]" :total="total"
                layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange" @current-change="handleCurrentChange" class="justify-center" />
            </div>
          </div>
        </div>
      </main>
    </div>

    <!-- 页脚 - 固定在底部 -->
    <footer class="bg-gray-800 text-white fixed bottom-0 left-0 right-0 z-40">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div class="text-center">
          <p class="text-gray-300 text-sm">© 2025 信息平台. 保留所有权利.</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  ArrowLeft,
  House,
  Calendar,
  View,
  ArrowRight,
  Loading,
  User
} from '@element-plus/icons-vue'
import { getArticlesByCategory, getCategories } from '@/api/cms'

const route = useRoute()
const router = useRouter()

// 响应式数据
const articles = ref([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const categoryName = ref('')
const categoryDescription = ref('')

// 分类映射 - 默认值，将从API获取实际数据
const categoryMap = ref({
  1: { name: '阳光规划', description: '城市规划公开信息' },
  2: { name: '政策法规', description: '政策法规文件' },
  3: { name: '政策解读', description: '政策解读说明' }
})



// 更新当前分类信息
const updateCategoryInfo = () => {
  const categoryId = route.params.id
  const category = categoryMap.value[categoryId] || categoryMap.value[1]
  categoryName.value = category.name
  categoryDescription.value = category.description
}

// 从分类映射中获取分类信息
const updateCategoryInfoFromMap = () => {
  updateCategoryInfo()
}

// 获取分类信息
const fetchCategoryInfo = async () => {
  try {
    const categoryId = parseInt(route.params.id)
    const response = await getCategories()

    // 处理API响应
    if (response && Array.isArray(response)) {
      const category = response.find(cat => cat.id === categoryId)
      if (category) {
        categoryName.value = category.name || `分类${categoryId}`
        categoryDescription.value = category.description || `${category.name}相关信息`
        console.log('获取分类信息成功:', category)
      } else {
        throw new Error(`未找到ID为${categoryId}的分类`)
      }
    } else {
      throw new Error('分类数据格式错误')
    }
  } catch (error) {
    console.error('获取分类信息失败:', error)
    // 使用默认值
    updateCategoryInfoFromMap()
  }
}

// 获取文章列表
const fetchArticles = async () => {
  loading.value = true
  try {
    const categoryId = route.params.id
    const response = await getArticlesByCategory(categoryId, {
      page: currentPage.value,
      per_page: pageSize.value
    })

    // 处理Laravel分页响应格式
    if (response && typeof response === 'object') {
      articles.value = response.data || []
      total.value = response.total || 0

      console.log('获取文章列表成功:', {
        articles: articles.value,
        total: total.value,
        currentPage: response.current_page,
        lastPage: response.last_page
      })
    } else {
      // 降级处理
      articles.value = Array.isArray(response) ? response : []
      total.value = articles.value.length
    }
  } catch (error) {
    console.error('获取文章列表失败:', error)
    ElMessage.error('获取文章列表失败')
    // 使用模拟数据
    loadMockArticles()
  } finally {
    loading.value = false
  }
}

// 模拟文章数据
const loadMockArticles = () => {
  const categoryId = parseInt(route.params.id)
  const mockData = {
    1: { // 阳光规划
      articles: [
        { id: 1, title: '菏泽市自然资源和规划局关于鲁中车城项目规划许可的公示', summary: '根据《城乡规划法》等相关法律法规，现对鲁中车城项目规划许可进行公示...', created_at: '2025-07-17', views: 156 },
        { id: 2, title: '菏泽市自然资源和规划局关于学院路东段改造工程规划的通知', summary: '为完善城市道路网络，提升交通通行能力，现就学院路东段改造工程规划...', created_at: '2025-07-15', views: 203 },
        { id: 3, title: '菏泽市自然资源和规划局关于工山路北延工程规划设计方案', summary: '工山路北延工程是完善城市路网结构的重要项目，现公布规划设计方案...', created_at: '2025-07-11', views: 178 },
        { id: 4, title: '《菏泽市西城区单元控制性详细规划》批复公告', summary: '经市政府批准，《菏泽市西城区单元控制性详细规划》正式获得批复...', created_at: '2025-07-09', views: 245 },
        { id: 5, title: '《菏泽市开发区单元控制性详细规划》公示', summary: '为规范开发区建设发展，现将《菏泽市开发区单元控制性详细规划》进行公示...', created_at: '2025-07-09', views: 189 }
      ],
      total: 25
    },
    2: { // 政策法规
      articles: [
        { id: 6, title: '菏泽市自然资源和规划局等11部门关于加强规划管理的通知', summary: '为进一步加强城乡规划管理，规范规划审批程序，现就有关事项通知如下...', created_at: '2025-05-20', views: 312 },
        { id: 7, title: '菏泽市自然资源和规划局关于印发《建设用地规划许可管理办法》的通知', summary: '为规范建设用地规划许可管理，根据相关法律法规，制定本办法...', created_at: '2024-08-13', views: 267 },
        { id: 8, title: '菏泽市自然资源和规划局关于印发《工业用地供应管理规定》的通知', summary: '为促进工业用地节约集约利用，规范工业用地供应管理，特制定本规定...', created_at: '2024-06-12', views: 198 }
      ],
      total: 18
    },
    3: { // 政策解读
      articles: [
        { id: 11, title: '解读：打好地质灾害"防治战"', summary: '地质灾害防治是保障人民生命财产安全的重要工作，本文详细解读相关政策措施...', created_at: '2025-05-21', views: 423 },
        { id: 12, title: '政策图明问答《关于开展政府老旧小区改造的实施意见》', summary: '老旧小区改造是重要的民生工程，通过图文问答形式解读实施意见...', created_at: '2024-08-13', views: 356 },
        { id: 13, title: '政策图明问答《关于开展工业用地供应改革的指导意见》', summary: '工业用地供应改革关系到经济发展质量，本文通过问答形式详细解读...', created_at: '2024-06-13', views: 289 }
      ],
      total: 12
    }
  }

  const data = mockData[categoryId] || mockData[1]
  articles.value = data.articles
  total.value = data.total
}

// 方法
const goBack = () => {
  router.push('/cms')
}

const goToArticle = (articleId) => {
  router.push({ name: 'cms-article-detail', params: { id: articleId } })
}

const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  fetchArticles()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchArticles()
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

// 监听路由变化
watch(() => route.params.id, (newId) => {
  if (newId) {
    currentPage.value = 1
    // 并行获取分类信息和文章列表
    Promise.all([
      fetchCategoryInfo(),
      fetchArticles()
    ])
  }
}, { immediate: true })

// 生命周期
onMounted(() => {
  console.log('CategoryArticles组件已挂载，路由参数:', route.params.id)
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.category-articles {
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}
</style>
