<template>
  <div class="bg-white p-6 rounded-lg shadow-md">
    <h2 class="text-2xl font-bold mb-4">课程管理</h2>
    <el-button type="primary" class="mb-4" @click="handleAdd">添加课程</el-button>
    <el-table :data="courses" border style="width: 100%">
      <el-table-column prop="id" label="ID" width="80"></el-table-column>
      <el-table-column prop="title" label="课程标题" width="300"></el-table-column>
      <el-table-column prop="created_at" label="创建时间" width="180"></el-table-column>
      <el-table-column prop="updated_at" label="更新时间" width="180"></el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button size="small" @click="handleView(scope.row)">查看</el-button>
          <el-button size="small" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="small" type="success" @click="handleVideos(scope.row)"
            >视频管理</el-button
          >
          <el-button size="small" type="info" @click="handlePricing(scope.row)">定价</el-button>
          <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="currentPage"
      :page-size="pageSize"
      :total="total"
      layout="total, prev, pager, next"
      class="mt-4"
      @current-change="handlePageChange"
    />
    <el-dialog v-model="dialogVisible" title="添加/编辑课程" width="50%">
      <el-form :model="form" :rules="rules" ref="formRef">
        <el-form-item label="课程标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入课程标题"></el-input>
        </el-form-item>
        <el-form-item label="课程介绍" prop="description">
          <el-input
            type="textarea"
            v-model="form.description"
            placeholder="请输入课程介绍"
            rows="5"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
    <el-dialog v-model="pricingDialogVisible" title="课程定价" width="30%">
      <el-form :model="pricingForm" :rules="pricingRules" ref="pricingFormRef">
        <el-form-item label="价格（元）" prop="price">
          <el-input-number
            v-model="pricingForm.price"
            placeholder="请输入价格"
            :min="0"
            :precision="2"
          ></el-input-number>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="pricingDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handlePricingSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const courses = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const dialogVisible = ref(false)
const pricingDialogVisible = ref(false)
const form = ref({ id: null, title: '', description: '' })
const pricingForm = ref({ id: null, price: 0 })
const formRef = ref(null)
const pricingFormRef = ref(null)
const rules = {
  title: [{ required: true, message: '请输入课程标题', trigger: 'blur' }],
  description: [{ required: true, message: '请输入课程介绍', trigger: 'blur' }],
}
const pricingRules = {
  price: [{ required: true, message: '请输入价格', trigger: 'blur' }],
}

onMounted(() => {
  fetchCourses()
})

const fetchCourses = () => {
  // 模拟数据
  courses.value = [
    {
      id: 1,
      title: '房产中介基础课程',
      created_at: '2023-01-01 10:00:00',
      updated_at: '2023-01-01 10:00:00',
    },
    {
      id: 2,
      title: '房产中介进阶课程',
      created_at: '2023-01-02 11:00:00',
      updated_at: '2023-01-02 11:00:00',
    },
    // 更多数据...
  ]
  total.value = courses.value.length
}

const handleAdd = () => {
  form.value = { id: null, title: '', description: '' }
  dialogVisible.value = true
}

const handleEdit = (row) => {
  form.value = { ...row }
  dialogVisible.value = true
}

const handleView = (row) => {
  // 查看逻辑
  console.log('查看课程:', row)
}

const handleVideos = (row) => {
  router.push(`/admin/courses/${row.id}/videos`)
}

const handlePricing = (row) => {
  pricingForm.value = { id: row.id, price: 0 }
  pricingDialogVisible.value = true
}

const handleDelete = (row) => {
  // 删除逻辑
  console.log('删除课程:', row)
}

const handleSubmit = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      // 提交逻辑
      console.log('提交课程:', form.value)
      dialogVisible.value = false
    }
  })
}

const handlePricingSubmit = () => {
  pricingFormRef.value.validate((valid) => {
    if (valid) {
      // 提交定价逻辑
      console.log('提交定价:', pricingForm.value)
      pricingDialogVisible.value = false
    }
  })
}

const handlePageChange = (page) => {
  currentPage.value = page
  fetchCourses()
}
</script>

<style scoped>
/* 课程管理特定样式 */
</style>
