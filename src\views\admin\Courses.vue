<template>
  <div class="bg-white p-6 rounded-lg shadow-md">
    <h2 class="text-2xl font-bold mb-4">课程管理</h2>

    <!-- 操作栏 -->
    <div class="action-bar mb-4">
      <el-button type="primary" @click="handleAdd" :icon="Plus">添加课程</el-button>
      <el-input v-model="searchKeyword" placeholder="搜索课程标题" clearable @keyup.enter="handleSearch" @clear="handleSearch" class="search-input">
        <template #append>
          <el-button @click="handleSearch" :icon="Search"></el-button>
        </template>
      </el-input>
    </div>

    <el-table :data="courses" border style="width: 100%; table-layout: fixed;" v-loading="loading">
      <el-table-column prop="id" label="ID" width="60" fixed="left"></el-table-column>
      <el-table-column prop="title" label="课程标题" min-width="200" show-overflow-tooltip></el-table-column>
      <el-table-column prop="description" label="课程描述" width="150">
        <template #default="scope">
          <span :title="scope.row.description">{{ truncateText(scope.row.description, 15) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="created_at" label="创建时间" width="120">
        <template #default="scope">
          {{ formatDate(scope.row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column prop="updated_at" label="更新时间" width="120">
        <template #default="scope">
          {{ formatDate(scope.row.updated_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="280" fixed="right">
        <template #default="scope">
          <div class="action-buttons">
            <el-button size="small" @click="handleView(scope.row)" :icon="View" title="查看详情">查看</el-button>
            <el-button size="small" type="primary" @click="handleEdit(scope.row)" :icon="Edit" title="编辑课程">编辑</el-button>
            <el-button size="small" type="success" @click="handleVideos(scope.row)" :icon="VideoPlay" title="视频管理">视频</el-button>
            <el-button size="small" type="warning" @click="handlePricing(scope.row)" :icon="Money" title="设置价格">定价</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)" :icon="Delete" title="删除课程">删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination v-model:current-page="currentPage" :page-size="pageSize" :total="total" layout="total, prev, pager, next" class="mt-4"
      @current-change="handlePageChange" />
    <!-- 添加/编辑课程对话框 -->
    <el-dialog v-model="dialogVisible" :title="form.id ? '编辑课程' : '添加课程'" width="50%" :before-close="handleCloseDialog">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="课程标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入课程标题" maxlength="100" show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="课程描述" prop="description">
          <el-input type="textarea" v-model="form.description" placeholder="请输入课程描述" rows="5" maxlength="500" show-word-limit></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseDialog">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 课程定价对话框 -->
    <el-dialog v-model="pricingDialogVisible" title="课程定价" width="30%">
      <el-form :model="pricingForm" :rules="pricingRules" ref="pricingFormRef" label-width="100px">
        <el-form-item label="价格（元）" prop="price">
          <el-input-number v-model="pricingForm.price" placeholder="请输入价格" :min="0" :precision="2" :step="0.01" style="width: 100%"></el-input-number>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="pricingDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handlePricingSubmit" :loading="submitLoading">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, View, Edit, VideoPlay, Delete, Money } from '@element-plus/icons-vue'
import {
  getCourseList,
  getCourseDetail,
  createCourse,
  updateCourse,
  deleteCourse,
  setCoursePrice
} from '@/api/admin/courses'

const router = useRouter()
const courses = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const loading = ref(false)
const submitLoading = ref(false)
const searchKeyword = ref('')

// 对话框相关
const dialogVisible = ref(false)
const pricingDialogVisible = ref(false)
const form = ref({ id: null, title: '', description: '' })
const pricingForm = ref({ id: null, price: 0 })
const formRef = ref(null)
const pricingFormRef = ref(null)
const rules = {
  title: [{ required: true, message: '请输入课程标题', trigger: 'blur' }],
  description: [{ required: true, message: '请输入课程介绍', trigger: 'blur' }],
}
const pricingRules = {
  price: [{ required: true, message: '请输入价格', trigger: 'blur' }],
}

onMounted(() => {
  fetchCourses()
})

const fetchCourses = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      per_page: pageSize.value
    }

    // 如果有搜索关键词，添加到参数中
    if (searchKeyword.value.trim()) {
      params.keyword = searchKeyword.value.trim()
    }

    const response = await getCourseList(params)
    courses.value = response.data || []
    total.value = response.total || 0
  } catch (error) {
    console.error('获取课程列表失败:', error)
    let errorMessage = '获取课程列表失败'

    if (error.response) {
      errorMessage += '：' + (error.response.data?.message || `HTTP ${error.response.status}`)
    } else if (error.message) {
      errorMessage += '：' + error.message
    } else {
      errorMessage += '：网络连接失败，请检查网络或稍后重试'
    }

    ElMessage.error(errorMessage)
  } finally {
    loading.value = false
  }
}

const handleAdd = () => {
  form.value = { id: null, title: '', description: '' }
  dialogVisible.value = true
}

const handleEdit = async (row) => {
  try {
    loading.value = true
    const response = await getCourseDetail(row.id)
    form.value = { ...response }
    dialogVisible.value = true
  } catch (error) {
    ElMessage.error('获取课程详情失败：' + (error.response?.data?.message || error.message))
  } finally {
    loading.value = false
  }
}

const handleView = async (row) => {
  try {
    const response = await getCourseDetail(row.id)
    ElMessageBox.alert(
      `<div>
        <p><strong>课程标题：</strong>${response.title}</p>
        <p><strong>课程描述：</strong>${response.description || '暂无描述'}</p>
        <p><strong>创建时间：</strong>${formatDateTime(response.created_at)}</p>
        <p><strong>更新时间：</strong>${formatDateTime(response.updated_at)}</p>
      </div>`,
      '课程详情',
      {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '确定'
      }
    )
  } catch (error) {
    ElMessage.error('获取课程详情失败：' + (error.response?.data?.message || error.message))
  }
}

const handleVideos = (row) => {
  router.push(`/admin/courses/${row.id}/videos`)
}

const handlePricing = (row) => {
  pricingForm.value = { id: row.id, price: 0 }
  pricingDialogVisible.value = true
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除课程"${row.title}"吗？此操作不可恢复！`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    loading.value = true
    await deleteCourse(row.id)
    ElMessage.success('删除成功')
    fetchCourses()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败：' + (error.response?.data?.message || error.message))
    }
  } finally {
    loading.value = false
  }
}

const handleSubmit = async () => {
  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    submitLoading.value = true

    if (form.value.id) {
      // 编辑课程
      await updateCourse(form.value.id, {
        title: form.value.title,
        description: form.value.description
      })
      ElMessage.success('课程更新成功')
    } else {
      // 创建课程
      await createCourse({
        title: form.value.title,
        description: form.value.description
      })
      ElMessage.success('课程创建成功')
    }

    dialogVisible.value = false
    fetchCourses()
  } catch (error) {
    console.error('提交课程失败:', error)
    let errorMessage = form.value.id ? '更新课程失败' : '创建课程失败'

    if (error.response) {
      errorMessage += '：' + (error.response.data?.message || `HTTP ${error.response.status}`)
    } else if (error.message) {
      errorMessage += '：' + error.message
    } else {
      errorMessage += '：未知错误，请稍后重试'
    }

    ElMessage.error(errorMessage)
  } finally {
    submitLoading.value = false
  }
}

const handlePricingSubmit = async () => {
  try {
    const valid = await pricingFormRef.value.validate()
    if (!valid) return

    submitLoading.value = true
    await setCoursePrice(pricingForm.value.id, pricingForm.value.price)
    ElMessage.success('定价设置成功')
    pricingDialogVisible.value = false
  } catch (error) {
    console.error('设置定价失败:', error)
    let errorMessage = '设置定价失败'

    if (error.response) {
      errorMessage += '：' + (error.response.data?.message || `HTTP ${error.response.status}`)
    } else if (error.message) {
      errorMessage += '：' + error.message
    } else {
      errorMessage += '：未知错误，请稍后重试'
    }

    ElMessage.error(errorMessage)
  } finally {
    submitLoading.value = false
  }
}

const handlePageChange = (page) => {
  currentPage.value = page
  fetchCourses()
}

const handleSearch = () => {
  currentPage.value = 1
  fetchCourses()
}

const handleCloseDialog = () => {
  dialogVisible.value = false
  form.value = { id: null, title: '', description: '' }
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 截断文本
const truncateText = (text, maxLength) => {
  if (!text) return '-'
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

// 格式化日期（简化版）
const formatDate = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleDateString('zh-CN', {
    month: '2-digit',
    day: '2-digit'
  })
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<style scoped>
/* 课程管理特定样式 */
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.search-input {
  width: 300px;
}

.el-table {
  width: 100% !important;
}

.el-table__body-wrapper {
  overflow-x: auto;
}

/* 确保容器占满宽度 */
.bg-white {
  width: 100%;
  box-sizing: border-box;
}

/* 操作按钮样式优化 */
.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 3px;
  align-items: center;
  justify-content: flex-start;
}

.action-buttons .el-button {
  margin: 0;
  padding: 3px 6px;
  font-size: 11px;
  height: 26px;
  line-height: 1.2;
  min-width: auto;
  border-radius: 3px;
}

.action-buttons .el-button + .el-button {
  margin-left: 0;
}

.action-buttons .el-button .el-icon {
  margin-right: 2px;
  font-size: 12px;
}

/* 对话框样式优化 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 表格样式优化 */
.el-table .el-table__cell {
  padding: 8px 0;
}

.el-table .cell {
  padding: 0 8px;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-bar {
    flex-direction: column;
    align-items: stretch;
  }

  .search-input {
    width: 100%;
  }
}
</style>
