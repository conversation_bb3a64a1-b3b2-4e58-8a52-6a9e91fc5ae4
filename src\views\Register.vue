<template>
  <div class="min-h-screen bg-gray-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8 bg-white p-8 rounded-lg shadow-md">
      <div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">机构账号注册</h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          已有账号？
          <router-link to="/login" class="font-medium text-blue-600 hover:text-blue-500">
            立即登录
          </router-link>
        </p>
      </div>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        class="mt-8 space-y-6"
        @submit.prevent="handleRegister"
      >
        <el-form-item prop="phone">
          <el-input v-model="form.phone" placeholder="手机号" :prefix-icon="Iphone" />
        </el-form-item>

        <el-form-item prop="captcha">
          <div class="flex space-x-2 items-center">
            <el-input
              v-model="form.captcha"
              placeholder="请输入图片验证码"
              maxlength="4"
              :prefix-icon="Message"
              style="flex: 1"
            />
            <img
              :src="captchaImg"
              @click="refreshCaptcha"
              class="h-10 w-24 rounded cursor-pointer border border-gray-200"
              :title="'点击刷新验证码'"
            />
          </div>
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="form.password"
            type="password"
            placeholder="设置密码"
            :prefix-icon="Lock"
            show-password
          />
        </el-form-item>

        <el-form-item prop="confirmPassword">
          <el-input
            v-model="form.confirmPassword"
            type="password"
            placeholder="确认密码"
            :prefix-icon="Lock"
            show-password
          />
        </el-form-item>

        <div class="flex items-center">
          <el-checkbox v-model="form.agreement">
            我已阅读并同意
            <a
              href="#"
              class="text-blue-600 hover:text-blue-500"
              @click.prevent="showAgreement = true"
            >
              《用户协议》
            </a>
          </el-checkbox>
        </div>

        <div>
          <el-button type="primary" class="w-full" :loading="loading" @click="handleRegister">
            {{ loading ? '注册中...' : '注册' }}
          </el-button>
        </div>
      </el-form>
    </div>

    <!-- 用户协议对话框 -->
    <el-dialog v-model="showAgreement" title="用户协议" width="90%" :max-width="'600px'">
      <div class="text-sm text-gray-600 leading-relaxed">
        <h3 class="font-bold text-lg mb-4">DFHDemo用户协议</h3>
        <p class="mb-4">欢迎使用DFHDemo！请仔细阅读以下协议内容：</p>
        <ol class="list-decimal list-inside space-y-2">
          <li>本系统仅供合法房产中介机构使用，注册即表明您的机构为合法经营的房产中介机构。</li>
          <li>您需要保证提供的所有信息真实有效，如有虚假信息，将承担相应的法律责任。</li>
          <li>您需要妥善保管账号密码，因账号密码保管不当造成的损失由您自行承担。</li>
          <li>系统有权对违规机构采取限制或关闭账号等措施。</li>
          <li>本协议最终解释权归系统所有。</li>
        </ol>
      </div>
      <template #footer>
        <el-button @click="showAgreement = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Iphone, Message, Lock } from '@element-plus/icons-vue'
import { getCaptcha, verifyCaptcha } from '@/api/captcha'
import { agencyRegister } from '@/api/auth'

const router = useRouter()
const formRef = ref(null)
const loading = ref(false)
const countdown = ref(0)
const showAgreement = ref(false)

const captchaKey = ref('')
const captchaImg = ref('')

const form = ref({
  phone: '',
  captcha: '',
  password: '',
  confirmPassword: '',
  agreement: false,
})

const rules = {
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
  ],
  captcha: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { min: 4, max: 4, message: '验证码为4位', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请设置密码', trigger: 'blur' },
    { min: 6, message: '密码不能少于6个字符', trigger: 'blur' },
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== form.value.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur',
    },
  ],
}

async function refreshCaptcha() {
  const res = await getCaptcha()
  captchaKey.value = res.key
  captchaImg.value = res.image
}

onMounted(() => {
  refreshCaptcha()
})

// 发送验证码
async function handleSendCode() {
  try {
    // 验证手机号
    await formRef.value.validateField('phone')

    // TODO: 调用发送验证码接口
    ElMessage.success('验证码已发送')

    // 开始倒计时
    countdown.value = 60
    const timer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)
  } catch (error) {
    // 手机号验证失败
  }
}

// 注册
async function handleRegister() {
  if (!form.value.agreement) {
    ElMessage.warning('请先阅读并同意用户协议')
    return
  }

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    loading.value = true

    // 只调用注册接口，不再单独校验验证码
    try {
      await agencyRegister(
        form.value.phone,
        form.value.password,
        captchaKey.value,
        form.value.captcha,
      )
      ElMessage.success('注册成功，请登录')
      router.push('/login')
    } catch (e) {
      ElMessage.error(e.response?.data?.message || '注册失败')
      refreshCaptcha()
      return
    }
  } catch (error) {
    ElMessage.error('注册失败：' + (error.message || '请稍后重试'))
  } finally {
    loading.value = false
  }
}
</script>
